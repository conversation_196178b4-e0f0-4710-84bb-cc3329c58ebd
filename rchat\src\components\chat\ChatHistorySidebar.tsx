import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, MessageSquare, Plus, Trash2, Clock, MoreVertical } from 'lucide-react';
import { Conversation } from '@/types';
import { formatTimestamp, truncateText } from '@/lib/utils';
import { useConversationHistory } from '@/hooks/useConversationHistory';

interface ChatHistorySidebarProps {
  isOpen: boolean;
  onToggle: () => void;
  onConversationSelect: (conversationId: string) => void;
  onNewChat: () => void;
  selectedConversationId?: string | null;
  className?: string;
}

const ChatHistorySidebar: React.FC<ChatHistorySidebarProps> = ({
  isOpen,
  onToggle,
  onConversationSelect,
  onNewChat,
  selectedConversationId,
  className = "",
}) => {
  const {
    conversations,
    isLoading,
    error,
    createNewConversation,
    deleteConversation,
    refreshConversations,
  } = useConversationHistory();

  const [loadingConversationId, setLoadingConversationId] = useState<string | null>(null);

  const handleConversationClick = async (conversation: Conversation) => {
    setLoadingConversationId(conversation.id);
    
    // Simulate loading delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    onConversationSelect(conversation.id);
    setLoadingConversationId(null);
  };

  const handleNewChat = async () => {
    try {
      const conversationId = await createNewConversation();
      if (conversationId) {
        onNewChat();
      }
    } catch (error) {
      console.error('Error creating new conversation:', error);
    }
  };

  const handleTestConversations = async () => {
    console.log('Testing conversation retrieval...');
    await refreshConversations();
    console.log('Current conversations:', conversations);
  };

  const handleDeleteConversation = async (conversationId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await deleteConversation(conversationId);
    } catch (error) {
      console.error('Error deleting conversation:', error);
    }
  };

  const groupConversationsByDate = (conversations: Conversation[]) => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const lastWeek = new Date(today);
    lastWeek.setDate(lastWeek.getDate() - 7);

    return {
      today: conversations.filter(conv => {
        const date = new Date(conv.updated_at || conv.created_at);
        return date >= today;
      }),
      yesterday: conversations.filter(conv => {
        const date = new Date(conv.updated_at || conv.created_at);
        return date >= yesterday && date < today;
      }),
      thisWeek: conversations.filter(conv => {
        const date = new Date(conv.updated_at || conv.created_at);
        return date >= lastWeek && date < yesterday;
      }),
      older: conversations.filter(conv => {
        const date = new Date(conv.updated_at || conv.created_at);
        return date < lastWeek;
      }),
    };
  };

  const groupedConversations = groupConversationsByDate(conversations);

  const renderConversationGroup = (title: string, conversations: Conversation[]) => {
    if (conversations.length === 0) return null;

    return (
      <div className="mb-4">
        <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2 px-2">
          {title}
        </h3>
        <div className="space-y-1">
          {conversations.map((conversation) => (
            <div
              key={conversation.id}
              onClick={() => handleConversationClick(conversation)}
              className={`group relative p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                selectedConversationId === conversation.id
                  ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-800 border border-transparent'
              } ${loadingConversationId === conversation.id ? 'opacity-50' : ''}`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <MessageSquare className="w-4 h-4 text-gray-400 dark:text-gray-500 flex-shrink-0" />
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                      {truncateText(conversation.name || "Untitled Chat", 30)}
                    </h4>
                  </div>
                  <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                    <Clock className="w-3 h-3" />
                    <span>{formatTimestamp(conversation.updated_at)}</span>
                  </div>
                </div>

                <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                  <button
                    onClick={(e) => handleDeleteConversation(conversation.id, e)}
                    className="p-1 rounded hover:bg-red-100 dark:hover:bg-red-900/20 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
                    title="Delete conversation"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {loadingConversationId === conversation.id && (
                <div className="absolute inset-0 flex items-center justify-center bg-white/50 dark:bg-gray-900/50 rounded-lg">
                  <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Toggle button - only show when sidebar is closed */}
      {!isOpen && (
        <button
          onClick={onToggle}
          className="fixed top-20 left-4 z-50 p-2 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-all duration-200"
          title="Open chat history"
        >
          <ChevronRight className="w-5 h-5 text-gray-600 dark:text-gray-300" />
        </button>
      )}

      {/* Sidebar */}
      <div
        className={`fixed top-16 left-0 h-[calc(100vh-4rem)] w-80 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 transform transition-transform duration-300 ease-in-out z-40 ${
          isOpen ? "translate-x-0" : "-translate-x-full"
        } ${className}`}
      >
        <div className="flex flex-col h-full">
          {/* Header with New Chat Button */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Chat History</h2>
              <button
                onClick={onToggle}
                className="p-1.5 rounded-lg text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                title="Close chat history"
              >
                <ChevronLeft className="w-5 h-5" />
              </button>
            </div>
            <button
              onClick={handleNewChat}
              className="w-full flex items-center justify-center gap-2 text-white font-medium py-2.5 px-4 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-200 mb-2"
            >
              <Plus className="w-4 h-4" />
              New Chat
            </button>
            <button
              onClick={handleTestConversations}
              className="w-full flex items-center justify-center gap-2 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200"
            >
              Test API
            </button>
          </div>

          {/* Conversations List */}
          <div className="flex-1 overflow-y-auto p-4 scrollbar-thin">
            {error && (
              <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-lg">
                <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
                <button
                  onClick={refreshConversations}
                  className="mt-2 text-xs text-red-800 dark:text-red-200 hover:underline"
                >
                  Try again
                </button>
              </div>
            )}

            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              </div>
            ) : conversations.length === 0 ? (
              <div className="text-center py-8">
                <MessageSquare className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
                <p className="text-gray-500 dark:text-gray-400 text-sm">
                  No conversations yet
                </p>
                <p className="text-gray-400 dark:text-gray-500 text-xs mt-1">
                  Start a new chat to begin
                </p>
              </div>
            ) : (
              <div>
                {renderConversationGroup("Today", groupedConversations.today)}
                {renderConversationGroup("Yesterday", groupedConversations.yesterday)}
                {renderConversationGroup("This Week", groupedConversations.thisWeek)}
                {renderConversationGroup("Older", groupedConversations.older)}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
              Chat history powered by R2R
            </p>
          </div>
        </div>
      </div>

      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/20 z-30 lg:hidden"
          onClick={onToggle}
        />
      )}
    </>
  );
};

export default ChatHistorySidebar;



