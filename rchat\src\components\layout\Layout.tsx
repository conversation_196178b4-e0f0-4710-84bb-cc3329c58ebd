import React from 'react';
import { useUserContext } from '@/context/UserContext';

interface LayoutProps {
  children: React.ReactNode;
  includeFooter?: boolean;
  className?: string;
}

const Layout: React.FC<LayoutProps> = ({ 
  children, 
  includeFooter = true, 
  className = '' 
}) => {
  const { authState } = useUserContext();

  return (
    <div className={`min-h-screen bg-white dark:bg-black ${className}`}>
      <main className="flex-1">
        {children}
      </main>
      {includeFooter && (
        <footer className="border-t border-gray-200 dark:border-gray-800 py-4">
          <div className="container mx-auto px-4 text-center text-sm text-gray-600 dark:text-gray-400">
            <p>&copy; 2024 RChat. Powered by R2R.</p>
          </div>
        </footer>
      )}
    </div>
  );
};

export default Layout;
