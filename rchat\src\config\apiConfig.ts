/**
 * API Configuration for RChat Frontend
 *
 * This file contains the configuration for connecting to the R2R backend API.
 * It integrates with the main chat configuration system.
 */

import { loadChatConfig, getDeploymentUrl } from "./chatConfig";
import { ServerConfig } from '../types';

/**
 * Get the API configuration from the chat configuration
 */
export function getApiConfig(): ServerConfig {
  const chatConfig = loadChatConfig();
  return chatConfig.server;
}

/**
 * Get the deployment URL for the API
 * This is the main function used throughout the application
 */
export { getDeploymentUrl };

/**
 * Validate and sanitize a URL
 */
export function sanitizeUrl(url: string): string {
  if (!url || url === "http://" || url === "https://") {
    return getDeploymentUrl();
  }

  let sanitized = url.trim();
  sanitized = sanitized.replace(/\/+$/, ""); // Remove trailing slashes

  // Add protocol if missing
  if (!/^https?:\/\//i.test(sanitized)) {
    sanitized = "http://" + sanitized;
  }

  // Clean up multiple slashes
  sanitized = sanitized.replace(/(https?:\/\/)|(\/)+/g, "$1$2");

  return sanitized;
}

/**
 * Get the full API endpoint URL
 */
export function getApiEndpoint(path: string = ""): string {
  const config = getApiConfig();
  const baseUrl = getDeploymentUrl();
  const version = config.apiVersion || "v3";
  
  // Remove leading slash from path if present
  const cleanPath = path.replace(/^\//, "");
  
  return `${baseUrl}/${version}${cleanPath ? `/${cleanPath}` : ""}`;
}
