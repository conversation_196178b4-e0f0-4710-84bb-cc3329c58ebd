{"name": "rchat", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -p 3007", "build": "next build", "start": "next start", "lint": "eslint --fix .", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/react-tooltip": "^1.1.7", "@tailwindcss/forms": "^0.5.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^11.18.2", "lucide-react": "^0.395.0", "next": "^14.2.30", "next-themes": "^0.3.0", "r2r-js": "^0.4.34", "react": "18.3.1", "react-dom": "18.3.1", "react-markdown": "^9.0.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^10.0.0", "zustand": "^5.0.5"}, "devDependencies": {"@types/react": "18.3.3", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-next": "14.2.30", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.3", "prettier": "^3.4.2", "tailwindcss": "^3.4.17", "typescript": "5.5.2"}}