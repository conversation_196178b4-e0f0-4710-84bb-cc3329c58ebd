/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "./src/config/chatConfig.ts":
/*!**********************************!*\
  !*** ./src/config/chatConfig.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultChatConfig: () => (/* binding */ defaultChatConfig),\n/* harmony export */   getDeploymentUrl: () => (/* binding */ getDeploymentUrl),\n/* harmony export */   loadChatConfig: () => (/* binding */ loadChatConfig),\n/* harmony export */   loadPublicConfig: () => (/* binding */ loadPublicConfig),\n/* harmony export */   saveChatConfig: () => (/* binding */ saveChatConfig)\n/* harmony export */ });\n/**\n * Chat Configuration for RChat Frontend\n *\n * This file contains all configuration options for the chat application,\n * including search settings, server connection, and UI preferences.\n */ /**\n * Default configuration values\n */ const defaultChatConfig = {\n    server: {\n        apiUrl: \"http://localhost:7272\",\n        port: 7272,\n        useHttps: false,\n        apiVersion: \"v3\",\n        timeout: 30000\n    },\n    app: {\n        appName: \"RChat\",\n        appDescription: \"R2R-powered chat application\",\n        version: \"1.0.0\",\n        defaultMode: \"rag_agent\"\n    },\n    vectorSearch: {\n        enabled: true,\n        searchLimit: 10,\n        searchFilters: \"{}\",\n        indexMeasure: \"cosine_distance\",\n        includeMetadatas: false,\n        probes: undefined,\n        efSearch: undefined\n    },\n    hybridSearch: {\n        enabled: false,\n        fullTextWeight: undefined,\n        semanticWeight: undefined,\n        fullTextLimit: undefined,\n        rrfK: undefined\n    },\n    graphSearch: {\n        enabled: true,\n        kgSearchLevel: null,\n        maxCommunityDescriptionLength: 100,\n        localSearchLimits: {},\n        maxLlmQueries: undefined\n    },\n    ragGeneration: {\n        temperature: 0.1,\n        topP: 1,\n        topK: 100,\n        maxTokensToSample: 1024,\n        kgTemperature: 0.1,\n        kgTopP: 1,\n        kgTopK: 100,\n        kgMaxTokensToSample: 1024\n    }\n};\n/**\n * Load configuration from localStorage with fallback to defaults\n */ function loadChatConfig() {\n    if (true) {\n        return defaultChatConfig;\n    }\n    try {\n        const stored = localStorage.getItem(\"rchatConfig\");\n        if (stored) {\n            const parsed = JSON.parse(stored);\n            // Merge with defaults to ensure all properties exist\n            return {\n                ...defaultChatConfig,\n                ...parsed,\n                server: {\n                    ...defaultChatConfig.server,\n                    ...parsed.server\n                },\n                app: {\n                    ...defaultChatConfig.app,\n                    ...parsed.app\n                },\n                vectorSearch: {\n                    ...defaultChatConfig.vectorSearch,\n                    ...parsed.vectorSearch\n                },\n                hybridSearch: {\n                    ...defaultChatConfig.hybridSearch,\n                    ...parsed.hybridSearch\n                },\n                graphSearch: {\n                    ...defaultChatConfig.graphSearch,\n                    ...parsed.graphSearch\n                },\n                ragGeneration: {\n                    ...defaultChatConfig.ragGeneration,\n                    ...parsed.ragGeneration\n                }\n            };\n        }\n    } catch (error) {\n        console.warn(\"Failed to load chat config from localStorage:\", error);\n    }\n    return defaultChatConfig;\n}\n/**\n * Save configuration to localStorage\n */ function saveChatConfig(config) {\n    if (true) {\n        return;\n    }\n    try {\n        localStorage.setItem(\"rchatConfig\", JSON.stringify(config));\n    } catch (error) {\n        console.error(\"Failed to save chat config to localStorage:\", error);\n    }\n}\n/**\n * Load configuration from public config.json file\n */ async function loadPublicConfig() {\n    if (true) {\n        return null;\n    }\n    try {\n        const response = await fetch(\"/config.json\");\n        if (!response.ok) {\n            console.warn(\"Could not load public config.json\");\n            return null;\n        }\n        const config = await response.json();\n        return {\n            server: {\n                apiUrl: config.apiUrl,\n                useHttps: config.useHttps,\n                timeout: config.timeout\n            },\n            app: {\n                appName: config.appName,\n                appDescription: config.appDescription,\n                version: config.version,\n                defaultMode: \"rag_agent\"\n            }\n        };\n    } catch (error) {\n        console.warn(\"Error loading public config.json:\", error);\n        return null;\n    }\n}\n/**\n * Get the deployment URL for the API\n */ function getDeploymentUrl(config) {\n    const chatConfig = config || loadChatConfig();\n    // Check for runtime config first (for Docker deployments)\n    if (false) {}\n    return chatConfig.server.apiUrl;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/config/chatConfig.ts\n");

/***/ }),

/***/ "./src/context/UserContext.tsx":
/*!*************************************!*\
  !*** ./src/context/UserContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: () => (/* binding */ UserProvider),\n/* harmony export */   useUserContext: () => (/* binding */ useUserContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var r2r_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! r2r-js */ \"r2r-js\");\n/* harmony import */ var r2r_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(r2r_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _config_chatConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../config/chatConfig */ \"./src/config/chatConfig.ts\");\n\n\n\n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst UserProvider = ({ children })=>{\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isAuthenticated: false,\n        user: null,\n        token: null\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [client, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Initialize client and check for existing session\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            try {\n                const deploymentUrl = (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_3__.getDeploymentUrl)();\n                const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(deploymentUrl);\n                setClient(newClient);\n                // Check for stored token\n                const storedToken = localStorage.getItem(\"r2r_token\");\n                const storedUser = localStorage.getItem(\"r2r_user\");\n                if (storedToken && storedUser) {\n                    try {\n                        const user = JSON.parse(storedUser);\n                        setAuthState({\n                            isAuthenticated: true,\n                            user,\n                            token: storedToken\n                        });\n                    } catch (error) {\n                        console.error(\"Error parsing stored user data:\", error);\n                        localStorage.removeItem(\"r2r_token\");\n                        localStorage.removeItem(\"r2r_user\");\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeAuth();\n    }, []);\n    const login = async (email, password, deploymentUrl)=>{\n        try {\n            const apiUrl = deploymentUrl || (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_3__.getDeploymentUrl)();\n            const loginClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(apiUrl);\n            const response = await loginClient.users.login({\n                email,\n                password\n            });\n            if (!response.results) {\n                throw new Error(\"Login failed: No results returned\");\n            }\n            const { access_token, user_id } = response.results;\n            if (!access_token) {\n                throw new Error(\"Login failed: No access token received\");\n            }\n            // Get user info\n            const userResponse = await loginClient.users.me();\n            if (!userResponse.results) {\n                throw new Error(\"Failed to get user information\");\n            }\n            const userInfo = userResponse.results;\n            const user = {\n                id: userInfo.id || user_id,\n                email: userInfo.email || email,\n                name: userInfo.name,\n                role: userInfo.role\n            };\n            // Store auth data\n            localStorage.setItem(\"r2r_token\", access_token);\n            localStorage.setItem(\"r2r_user\", JSON.stringify(user));\n            // Update state\n            setAuthState({\n                isAuthenticated: true,\n                user,\n                token: access_token\n            });\n            // Update client with new URL if provided\n            if (deploymentUrl) {\n                const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(deploymentUrl);\n                setClient(newClient);\n            }\n            return user;\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            throw error;\n        }\n    };\n    const logout = ()=>{\n        localStorage.removeItem(\"r2r_token\");\n        localStorage.removeItem(\"r2r_user\");\n        setAuthState({\n            isAuthenticated: false,\n            user: null,\n            token: null\n        });\n    };\n    const getClient = async ()=>{\n        if (!authState.isAuthenticated || !authState.token) {\n            return null;\n        }\n        if (!client) {\n            const deploymentUrl = (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_3__.getDeploymentUrl)();\n            const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(deploymentUrl);\n            setClient(newClient);\n            return newClient;\n        }\n        return client;\n    };\n    const value = {\n        authState,\n        login,\n        logout,\n        getClient,\n        isLoading\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\context\\\\UserContext.tsx\",\n        lineNumber: 157,\n        columnNumber: 10\n    }, undefined);\n};\nconst useUserContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (context === undefined) {\n        throw new Error(\"useUserContext must be used within a UserProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/context/UserContext.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/UserContext */ \"./src/context/UserContext.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n        attribute: \"class\",\n        defaultTheme: \"system\",\n        enableSystem: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_UserContext__WEBPACK_IMPORTED_MODULE_2__.UserProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 10,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQzRDO0FBQ1M7QUFDdkI7QUFFZixTQUFTRSxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFZO0lBQzVELHFCQUNFLDhEQUFDSixzREFBYUE7UUFBQ0ssV0FBVTtRQUFRQyxjQUFhO1FBQVNDLFlBQVk7a0JBQ2pFLDRFQUFDTiw4REFBWUE7c0JBQ1gsNEVBQUNFO2dCQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJaEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yY2hhdC8uL3NyYy9wYWdlcy9fYXBwLnRzeD9mOWQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCc7XG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIH0gZnJvbSAnbmV4dC10aGVtZXMnO1xuaW1wb3J0IHsgVXNlclByb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0L1VzZXJDb250ZXh0JztcbmltcG9ydCAnQC9zdHlsZXMvZ2xvYmFscy5jc3MnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxUaGVtZVByb3ZpZGVyIGF0dHJpYnV0ZT1cImNsYXNzXCIgZGVmYXVsdFRoZW1lPVwic3lzdGVtXCIgZW5hYmxlU3lzdGVtPlxuICAgICAgPFVzZXJQcm92aWRlcj5cbiAgICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgICAgPC9Vc2VyUHJvdmlkZXI+XG4gICAgPC9UaGVtZVByb3ZpZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlRoZW1lUHJvdmlkZXIiLCJVc2VyUHJvdmlkZXIiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJhdHRyaWJ1dGUiLCJkZWZhdWx0VGhlbWUiLCJlbmFibGVTeXN0ZW0iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next-themes":
/*!******************************!*\
  !*** external "next-themes" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-themes");

/***/ }),

/***/ "r2r-js":
/*!*************************!*\
  !*** external "r2r-js" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("r2r-js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./src/pages/_app.tsx"));
module.exports = __webpack_exports__;

})();