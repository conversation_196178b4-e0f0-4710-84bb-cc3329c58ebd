(()=>{var e={};e.id=888,e.ids=[888],e.modules={6361:(e,r,t)=>{"use strict";t.d(r,{YQ:()=>o,pe:()=>a});let s={server:{apiUrl:"http://localhost:7272",port:7272,useHttps:!1,apiVersion:"v3",timeout:3e4},app:{appName:"RChat",appDescription:"R2R-powered chat application",version:"1.0.0",defaultMode:"rag_agent"},vectorSearch:{enabled:!0,searchLimit:10,searchFilters:"{}",indexMeasure:"cosine_distance",includeMetadatas:!1,probes:void 0,efSearch:void 0},hybridSearch:{enabled:!1,fullTextWeight:void 0,semanticWeight:void 0,fullTextLimit:void 0,rrfK:void 0},graphSearch:{enabled:!0,kgSearchLevel:null,maxCommunityDescriptionLength:100,localSearchLimits:{},maxLlmQueries:void 0},ragGeneration:{temperature:.1,topP:1,topK:100,maxTokensToSample:1024,kgTemperature:.1,kgTopP:1,kgTopK:100,kgMaxTokensToSample:1024}};function o(){return s}function a(e){return(e||s).server.apiUrl}},6259:(e,r,t)=>{"use strict";t.d(r,{d:()=>i,S:()=>c});var s=t(997),o=t(6689);let a=require("r2r-js");var n=t(6361);let l=(0,o.createContext)(void 0),i=({children:e})=>{let[r,t]=(0,o.useState)({isAuthenticated:!1,user:null,token:null}),[i,c]=(0,o.useState)(!0),[u,m]=(0,o.useState)(null);(0,o.useEffect)(()=>{(async()=>{try{let e=(0,n.pe)(),r=new a.r2rClient(e);m(r);let s=localStorage.getItem("chatAccessToken")||localStorage.getItem("r2r_token"),o=localStorage.getItem("chatRefreshToken"),l=localStorage.getItem("r2r_user");if(s&&l)try{let e=JSON.parse(l);o&&r.setTokens(s,o),t({isAuthenticated:!0,user:e,token:s})}catch(e){console.error("Error parsing stored user data:",e),localStorage.removeItem("r2r_token"),localStorage.removeItem("r2r_user"),localStorage.removeItem("chatAccessToken"),localStorage.removeItem("chatRefreshToken")}}catch(e){console.error("Error initializing auth:",e)}finally{c(!1)}})()},[]);let d=async(e,r,s)=>{try{let o=s||(0,n.pe)(),l=new a.r2rClient(o),i=await l.users.login({email:e,password:r});if(!i.results)throw Error("Login failed: No results returned");localStorage.setItem("chatAccessToken",i.results.accessToken.token),localStorage.setItem("chatRefreshToken",i.results.refreshToken.token),l.setTokens(i.results.accessToken.token,i.results.refreshToken.token),m(l);let c=await l.users.me();if(!c.results)throw Error("Failed to get user information");let u="user";try{await l.system.settings(),u="admin"}catch(e){e instanceof Error&&"status"in e&&403===e.status||console.error("Unexpected error when checking user role:",e)}let d={id:c.results.id,email:c.results.email||e,name:c.results.name,role:u};return localStorage.setItem("r2r_token",i.results.accessToken.token),localStorage.setItem("r2r_user",JSON.stringify(d)),t({isAuthenticated:!0,user:d,token:i.results.accessToken.token}),d}catch(e){if(console.error("Login error:",e),e instanceof Error){if(e.message.includes("401")||e.message.includes("Unauthorized"))throw Error("Invalid email or password. Please check your credentials.");if(e.message.includes("404")||e.message.includes("Not Found"))throw Error("User not found. Please check your email address.");if(e.message.includes("500")||e.message.includes("Internal Server Error"))throw Error("Server error. Please try again later.");if(e.message.includes("Network Error")||e.message.includes("fetch"))throw Error("Cannot connect to server. Please check if R2R backend is running.")}throw e}},h=async()=>{if(!r.isAuthenticated||!r.token)return null;if(!u){let e=(0,n.pe)(),r=new a.r2rClient(e),t=localStorage.getItem("chatAccessToken")||localStorage.getItem("r2r_token"),s=localStorage.getItem("chatRefreshToken");return t&&r.setTokens(t,s||""),m(r),r}return u};return s.jsx(l.Provider,{value:{authState:r,login:d,logout:()=>{localStorage.removeItem("r2r_token"),localStorage.removeItem("r2r_user"),localStorage.removeItem("chatAccessToken"),localStorage.removeItem("chatRefreshToken"),t({isAuthenticated:!1,user:null,token:null}),m(null)},getClient:h,isLoading:i},children:e})},c=()=>{let e=(0,o.useContext)(l);if(void 0===e)throw Error("useUserContext must be used within a UserProvider");return e}},7724:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(997);let o=require("next-themes");var a=t(6259);function n({Component:e,pageProps:r}){return s.jsx(o.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,children:s.jsx(a.d,{children:s.jsx(e,{...r})})})}t(108)},108:()=>{},6689:e=>{"use strict";e.exports=require("react")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")}};var r=require("../webpack-runtime.js");r.C(e);var t=r(r.s=7724);module.exports=t})();