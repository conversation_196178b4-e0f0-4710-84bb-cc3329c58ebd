{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' edo '-1' iraungitzerik ez izateko.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(adib. `sh webui.sh --api --api-auth erabiltzaile_pasahitza`)", "(e.g. `sh webui.sh --api`)": "(adib. `sh webui.sh --api`)", "(latest)": "(azkena)", "(leave blank for to use commercial endpoint)": "", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}}-ren <PERSON>", "{{webUIName}} Backend Required": "{{webUIName}} Backend-a Beharrezkoa", "*Prompt node ID(s) are required for image generation": "Prompt nodoaren IDa(k) beharrezkoak dira irudiak <PERSON>", "A new version (v{{LATEST_VERSION}}) is now available.": "<PERSON><PERSON> be<PERSON> bat (v{{LATEST_VERSION}}) eskuragarri dago orain.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Ataza eredua erabiltzen da txatentzako izenburuak eta web bilaketa kontsultak sortzeko bezalako atazak egitean", "a user": "<PERSON><PERSON><PERSON><PERSON><PERSON> bat", "About": "<PERSON><PERSON> b<PERSON>z", "Accept autocomplete generation / Jump to prompt variable": "", "Access": "Sarbidea", "Access Control": "Sarb<PERSON>", "Accessible to all users": "Erabiltzaile guztientzat eskuragarri", "Account": "Kontua", "Account Activation Pending": "Kontuaren Aktibazioa Zain", "Accurate information": "Informazi<PERSON>", "Actions": "<PERSON><PERSON><PERSON><PERSON>", "Activate": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "\"/{{COMMAND}}\" idatziz aktibatu komando hau txataren sarreran.", "Active Users": "Erabiltzaile Aktiboak", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Add a model ID": "Gehitu eredu ID bat", "Add a short description about what this model does": "Gehitu eredu honek egiten duenaren deskribapen labur bat", "Add a tag": "<PERSON><PERSON><PERSON>u et<PERSON> bat", "Add Arena Model": "Gehitu Arena Eredua", "Add Connection": "Gehitu Konexioa", "Add Content": "<PERSON><PERSON><PERSON><PERSON>", "Add content here": "G<PERSON><PERSON>u edukia hemen", "Add Custom Parameter": "", "Add custom prompt": "<PERSON><PERSON><PERSON><PERSON> prompt pertsonalizatua", "Add Files": "<PERSON>eh<PERSON>u <PERSON>", "Add Group": "<PERSON><PERSON><PERSON><PERSON>", "Add Memory": "<PERSON><PERSON><PERSON><PERSON>", "Add Model": "<PERSON><PERSON><PERSON><PERSON>", "Add Reaction": "", "Add Tag": "<PERSON><PERSON><PERSON><PERSON>", "Add Tags": "<PERSON><PERSON><PERSON><PERSON>", "Add text content": "Gehitu testu edukia", "Add User": "<PERSON><PERSON><PERSON><PERSON>", "Add User Group": "<PERSON><PERSON><PERSON><PERSON>", "Adjusting these settings will apply changes universally to all users.": "Ezarpen hauek aldatzeak aldaketak erabiltzaile guztiei aplikatuko dizkie.", "admin": "administratzailea", "Admin": "Administratzailea", "Admin Panel": "Administrazio Panela", "Admin Settings": "<PERSON><PERSON><PERSON><PERSON>", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Administratzaileek tresna guztietarako sarbidea dute beti; erabiltzaileek lan-eremuan eredu bakoitzeko esleituak behar dituzte tresnak.", "Advanced Parameters": "Parametro Aurreratuak", "Advanced Params": "Parametro Aurreratuak", "All": "", "All Documents": "Dokumentu Guztiak", "All models deleted successfully": "<PERSON><PERSON><PERSON> guztiak ongi ezabatu dira", "Allow Call": "", "Allow Chat Controls": "", "Allow Chat Delete": "Baimendu Txa<PERSON>", "Allow Chat Deletion": "Baimendu Txa<PERSON>", "Allow Chat Edit": "Baimendu Txata <PERSON>a", "Allow Chat Export": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow File Upload": "Baimendu Fitxategiak Igotzea", "Allow Multiple Models in Chat": "", "Allow non-local voices": "<PERSON><PERSON><PERSON> u<PERSON><PERSON> ah<PERSON>ak", "Allow Speech to Text": "", "Allow Temporary Chat": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "Allow Text to Speech": "", "Allow User Location": "<PERSON><PERSON><PERSON>", "Allow Voice Interruption in Call": "Baimendu Ahots Etena De<PERSON>", "Allowed Endpoints": "", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "Baduzu kontu bat?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Always Play Notification Sound": "", "Amazing": "Harrigarria", "an assistant": "la<PERSON>tz<PERSON><PERSON> bat", "Analyzed": "", "Analyzing...": "", "and": "eta", "and {{COUNT}} more": "eta {{COUNT}} gehiago", "and create a new shared link.": "eta sortu partekatutako esteka berri bat.", "Android": "", "API": "", "API Base URL": "API Oinarri URLa", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "API Gakoa", "API Key created.": "API Gakoa sortu da.", "API Key Endpoint Restrictions": "", "API keys": "API gakoak", "API Version": "", "Application DN": "Aplikazioaren DN", "Application DN Password": "Aplikazioaren DN Pasahitza", "applies to all users with the \"user\" role": "\"erabiltzaile\" rola duten erabiltzaile guztiei aplikatzen zaie", "April": "<PERSON><PERSON><PERSON><PERSON>", "Archive": "Artxibatu", "Archive All Chats": "Artxibatu Txat Guztiak", "Archived Chats": "Artxibatutako Txatak", "archived-chat-export": "artxibatutako-txat-esportazioa", "Are you sure you want to clear all memories? This action cannot be undone.": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "<PERSON><PERSON><PERSON> zaude artxibatutako txat guztiak desartxibatu nahi dituzula?", "Are you sure?": "<PERSON><PERSON><PERSON>?", "Arena Models": "Arena Ereduak", "Artifacts": "Artefaktuak", "Ask": "", "Ask a question": "Egin galdera bat", "Assistant": "Laguntzail<PERSON>", "Attach file from knowledge": "", "Attention to detail": "Xehetasunei arreta", "Attribute for Mail": "", "Attribute for Username": "Erabiltzaile-izenerako atributua", "Audio": "Audioa", "August": "Abuztua", "Auth": "", "Authenticate": "Autentifikatu", "Authentication": "", "Auto": "", "Auto-Copy Response to Clipboard": "Automatikoki Kopiatu Erantzuna Arbelera", "Auto-playback response": "Automatikoki erreproduzitu erantzuna", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 Api Autentifikazio Katea", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 <PERSON>inarri <PERSON>", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 Oinarri U<PERSON>a beharrezkoa da.", "Available list": "Zerrenda erabilgarria", "Available Tools": "", "available!": "eskuragarri!", "Awful": "Penagarria", "Azure AI Speech": "Azure AI Hizketa", "Azure Region": "Azure Eskualdea", "Back": "Atzera", "Bad Response": "Erantzun Txarra", "Banners": "<PERSON><PERSON>", "Base Model (From)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Nondik)", "before": "aurretik", "Being lazy": "Alferra izatea", "Beta": "", "Bing Search V7 Endpoint": "Bing Bilaketa V7 Endpointua", "Bing Search V7 Subscription Key": "Bing Bilaketa V7 Harpidetza Gakoa", "Bocha Search API Key": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "Brave Bilaketa API Gakoa", "By {{name}}": "{{name}}-k", "Bypass Embedding and Retrieval": "", "Bypass Web Loader": "", "Calendar": "", "Call": "<PERSON><PERSON>", "Call feature is not supported when using Web STT engine": "Dei funtzioa ez da onartzen Web STT motorra erabiltzean", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON>", "Capabilities": "Gaitasunak", "Capture": "", "Capture Audio": "", "Certificate Path": "Ziurtagiriaren Bidea", "Change Password": "<PERSON><PERSON><PERSON>", "Channel Name": "", "Channels": "", "Character": "Karakterea", "Character limit for autocomplete generation input": "", "Chart new frontiers": "Esplora<PERSON> <PERSON>a berriak", "Chat": "Txata", "Chat Background Image": "<PERSON><PERSON><PERSON><PERSON>", "Chat Bubble UI": "Txat Burbuilen Interfazea", "Chat Controls": "<PERSON><PERSON><PERSON>", "Chat direction": "Txataren <PERSON>", "Chat Overview": "<PERSON><PERSON><PERSON><PERSON>", "Chat Permissions": "<PERSON><PERSON><PERSON>", "Chat Tags Auto-Generation": "Txat Etiketen Auto-Sorkuntza", "Chats": "Txatak", "Check Again": "Egiaztat<PERSON>", "Check for updates": "Bilatu eguneraketak", "Checking for updates...": "Eguneraketak bilatzen...", "Choose a model before saving...": "Aukeratu eredu bat gorde aurretik...", "Chunk Overlap": "<PERSON><PERSON><PERSON>", "Chunk Size": "<PERSON><PERSON>", "Ciphers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Citation": "Aipamen<PERSON>", "Citations": "", "Clear memory": "Garbitu memoria", "Clear Memory": "", "click here": "klikatu hemen", "Click here for filter guides.": "Klikatu hemen iragazkien gidak ikusteko.", "Click here for help.": "Klikatu hemen laguntzarako.", "Click here to": "Klikatu hemen", "Click here to download user import template file.": "Klikatu hemen erabiltzaileen inportazio txantiloia deskargatzeko.", "Click here to learn more about faster-whisper and see the available models.": "Klikatu hemen faster-whisper-i buruz gehiago ikasteko eta eredu erabilgarriak ikusteko.", "Click here to see available models.": "", "Click here to select": "Klikatu hemen hautatzeko", "Click here to select a csv file.": "Klikatu hemen csv fitxategi bat hautatzeko.", "Click here to select a py file.": "Klikatu hemen py fitxategi bat hautatzeko.", "Click here to upload a workflow.json file.": "Klikatu hemen workflow.json fitxategia igotzeko.", "click here.": "klikatu hemen.", "Click on the user role button to change a user's role.": "Klikatu erabiltzaile rolaren botoian erabiltzaile baten rola al<PERSON>.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Arbelerako idazteko baimena ukatua. <PERSON><PERSON><PERSON>, egiaztatu zure nabigatzailearen ezarpenak beharrezko sarbidea emateko.", "Clone": "Klonatu", "Clone Chat": "", "Clone of {{TITLE}}": "", "Close": "Itxi", "Close modal": "", "Close settings modal": "", "Code execution": "<PERSON><PERSON>ren exekuz<PERSON>", "Code Execution": "", "Code Execution Engine": "", "Code Execution Timeout": "", "Code formatted successfully": "Kodea ongi formateatu da", "Code Interpreter": "", "Code Interpreter Engine": "", "Code Interpreter Prompt Template": "", "Collapse": "", "Collection": "<PERSON><PERSON><PERSON><PERSON>", "Color": "Kolorea", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "ComfyUI Oinarri URLa", "ComfyUI Base URL is required.": "ComfyUI Oinarri URLa beharrezkoa da.", "ComfyUI Workflow": "ComfyUI Lan-fluxua", "ComfyUI Workflow Nodes": "ComfyUI Lan-fluxu Nodoak", "Command": "<PERSON><PERSON><PERSON>", "Completions": "Osatzeak", "Concurrent Requests": "<PERSON><PERSON><PERSON>", "Configure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Confirm": "<PERSON><PERSON><PERSON><PERSON>", "Confirm Password": "<PERSON><PERSON><PERSON><PERSON>", "Confirm your action": "<PERSON><PERSON><PERSON><PERSON> zure eki<PERSON>", "Confirm your new password": "", "Connect to your own OpenAI compatible API endpoints.": "", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "Konexioak", "Connections saved successfully": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "<PERSON><PERSON><PERSON> Administratzailearekin WebUI Sarbiderako", "Content": "<PERSON><PERSON><PERSON>", "Content Extraction Engine": "", "Continue Response": "J<PERSON><PERSON><PERSON>", "Continue with {{provider}}": "Jarraitu {{provider}}-rekin", "Continue with Email": "Jarraitu Posta Elektronikoarekin", "Continue with LDAP": "Jarraitu LDAP-rekin", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Kontrolatu nola banatzen den mezuaren testua TTS eskaeretarako. 'Puntuazioa'-k esaldietan banatzen du, 'paragrafoak'-k paragrafoetan, eta 'bat ere ez'-ek mezua kate bakar gisa mantentzen du.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "<PERSON><PERSON><PERSON><PERSON>", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Copied": "Kopiatu<PERSON>", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "Partekatutako txataren URLa arbelera kopiatu da!", "Copied to clipboard": "Arb<PERSON>ra k<PERSON>ta", "Copy": "Kopiatu", "Copy Formatted Text": "", "Copy last code block": "Kopiatu azken kode blokea", "Copy last response": "Kopiatu azken erantzuna", "Copy Link": "Kopiatu <PERSON>ste<PERSON>", "Copy to clipboard": "Kopiatu arbelera", "Copying to clipboard was successful!": "Arbelera kop<PERSON>tzea arrakas<PERSON>sua izan da!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "", "Create": "Sortu", "Create a knowledge base": "<PERSON><PERSON><PERSON>-base bat", "Create a model": "Sortu eredu bat", "Create Account": "Sortu Kontua", "Create Admin Account": "Sortu Administratzaile Kontua", "Create Channel": "", "Create Group": "Sortu Taldea", "Create Knowledge": "<PERSON>rt<PERSON>", "Create new key": "Sortu gako berria", "Create new secret key": "Sortu gako sekretu berria", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "Sortze data", "Created At": "Sortze Data", "Created by": "<PERSON><PERSON><PERSON><PERSON>", "CSV Import": "CSV Inportazioa", "Ctrl+Enter to Send": "", "Current Model": "<PERSON><PERSON><PERSON>", "Current Password": "<PERSON><PERSON><PERSON>", "Custom": "Pertsonalizatua", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "", "Dark": "Iluna", "Database": "Datu-basea", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "December": "Abendua", "Default": "Lehenetsia", "Default (Open AI)": "<PERSON><PERSON><PERSON><PERSON> (Open AI)", "Default (SentenceTransformers)": "Lehenetsia (SentenceTransformers)", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "<PERSON><PERSON><PERSON>", "Default model updated": "Eredu lehenetsia eguneratu da", "Default Models": "<PERSON><PERSON><PERSON>", "Default permissions": "<PERSON><PERSON>", "Default permissions updated successfully": "Baimen lehen<PERSON>iak ongi eguneratu dira", "Default Prompt Suggestions": "Prompt <PERSON><PERSON><PERSON><PERSON><PERSON>", "Default to 389 or 636 if TLS is enabled": "Lehenetsi 389 edo 636 TLS gaituta badago", "Default to ALL": "Lehenetsi GUZTIAK", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "Erabiltzaile Rol Lehenetsia", "Delete": "Ezabat<PERSON>", "Delete a model": "<PERSON>za<PERSON>u eredu bat", "Delete All Chats": "Ezabatu Txat Guztiak", "Delete All Models": "Ezabatu Eredu <PERSON>", "Delete chat": "Ezabatu txata", "Delete Chat": "Ezabatu Txata", "Delete chat?": "Ezabatu txata?", "Delete folder?": "Ezabatu karpeta?", "Delete function?": "Ezabatu funtzioa?", "Delete Message": "", "Delete message?": "", "Delete note?": "", "Delete prompt?": "<PERSON>zabatu prompta?", "delete this link": "ezabatu esteka hau", "Delete tool?": "Ezabatu tresna?", "Delete User": "Ezabatu <PERSON>", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} eza<PERSON><PERSON> da", "Deleted {{name}}": "{{name}} e<PERSON><PERSON><PERSON> da", "Deleted User": "Ezabatuta<PERSON>", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "Deskribatu zure ezagutza-basea eta helburuak", "Description": "Deskribapena", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "Ez ditu jarraibideak guztiz jarraitu", "Direct": "", "Direct Connections": "", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "", "Direct Connections settings updated": "", "Direct Tool Servers": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "Desgai<PERSON><PERSON>", "Discover a function": "Aurkitu funtz<PERSON> bat", "Discover a model": "Aurkitu eredu bat", "Discover a prompt": "<PERSON><PERSON><PERSON><PERSON> prompt bat", "Discover a tool": "Aurkitu tresna bat", "Discover how to use Open WebUI and seek support from the community.": "", "Discover wonders": "Aurk<PERSON><PERSON>", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON><PERSON>, deskargatu eta esploratu funtzio pertsonalizatuak", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON><PERSON>, deskargatu eta esploratu prompt pertsonalizatuak", "Discover, download, and explore custom tools": "<PERSON><PERSON><PERSON><PERSON>, deskargatu eta esploratu tresna pertsonalizatuak", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON><PERSON>, deskargatu eta esploratu ereduen aurrezarpenak", "Dismissible": "Baztergarria", "Display": "Bistaratu", "Display Emoji in Call": "<PERSON><PERSON><PERSON><PERSON>", "Display the username instead of You in the Chat": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>n <PERSON><PERSON>", "Displays citations in the response": "Erakutsi aipamenak erantzu<PERSON>n", "Dive into knowledge": "<PERSON><PERSON><PERSON><PERSON>", "Do not install functions from sources you do not fully trust.": "Ez instalatu guztiz fidagarriak ez diren iturrietatik datozen funtzioak.", "Do not install tools from sources you do not fully trust.": "Ez instalatu guztiz fidagarriak ez diren iturrietatik datozen tresnak.", "Docling": "", "Docling Server URL required.": "", "Document": "Dokumentua", "Document Intelligence": "", "Document Intelligence endpoint and key required.": "", "Documentation": "Dokumentazioa", "Documents": "Dokumentuak", "does not make any external connections, and your data stays securely on your locally hosted server.": "ez du kanpo konexiorik egiten, eta zure datuak modu seguruan mantentzen dira zure zerbitzari lokalean.", "Domain Filter List": "", "Don't have an account?": "Ez duzu konturik?", "don't install random functions from sources you don't trust.": "ez instalatu fidagarriak ez diren iturrietatik datozen ausazko fun<PERSON>.", "don't install random tools from sources you don't trust.": "ez instalatu fidagarriak ez diren iturrietatik datozen ausazko tresnak.", "Don't like the style": "Ez zaizu estiloa gustatzen?", "Done": "Eginda", "Download": "<PERSON><PERSON><PERSON><PERSON>", "Download as SVG": "", "Download canceled": "<PERSON><PERSON><PERSON> bertan behera utzi da", "Download Database": "<PERSON><PERSON><PERSON><PERSON>a", "Drag and drop a file to upload or select a file to view": "Arrastatu eta jaregin fitxategi bat igotzeko edo hautatu fitxategi bat ikusteko", "Draw": "<PERSON><PERSON><PERSON>", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "adib. '30s','10m'. Denbora unitate baliodunak dira 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "", "e.g. A filter to remove profanity from text": "adib. Testutik lizunkeriak kentzeko iragazki bat", "e.g. en": "", "e.g. My Filter": "adib. <PERSON><PERSON>", "e.g. My Tools": "adib. <PERSON><PERSON>", "e.g. my_filter": "adib. nire_i<PERSON>a", "e.g. my_tools": "adib. nire_tresnak", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "adib. <PERSON><PERSON><PERSON> eragiketa egiteko t<PERSON>k", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "e.g.) en,fr,de": "", "Edit": "Editatu", "Edit Arena Model": "Editatu Arena Eredua", "Edit Channel": "", "Edit Connection": "Editatu Konexioa", "Edit Default Permissions": "Editatu Bai<PERSON>", "Edit Memory": "Editatu Memoria", "Edit User": "Editatu Erabiltzailea", "Edit User Group": "Editatu Erabiltzaile <PERSON>ldea", "Eject": "", "ElevenLabs": "ElevenLabs", "Email": "Posta elektronikoa", "Embark on adventures": "<PERSON><PERSON>", "Embedding": "", "Embedding Batch Size": "Embedding <PERSON><PERSON>", "Embedding Model": "Embedding <PERSON><PERSON><PERSON>", "Embedding Model Engine": "Embedding <PERSON>redu Motorea", "Embedding model set to \"{{embedding_model}}\"": "Embedding eredua \"{{embedding_model}}\"-ra e<PERSON><PERSON> da", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Code Execution": "", "Enable Code Interpreter": "", "Enable Community Sharing": "<PERSON><PERSON><PERSON>", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Gaitu Memoria Blokeatzea (mlock) ereduaren datuak RAM memoriatik kanpo ez trukatzeko. Aukera honek ereduaren lan-orri multzoa RAMean blokatzen du, diskora ez direla trukatuko ziurtatuz. Honek errendimendua mantentzen lagun dezake, orri-hutsegiteak saihestuz eta datuen sarbide azkarra bermatuz.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Gaitu Memoria Mapaketa (mmap) ereduaren datuak kargatzeko. Aukera honek sistemari disko-biltegiratzea RAM memoriaren luzapen gisa erabiltzea ahalbidetzen dio, diskoko fitxategiak RAMean baleude bezala tratatuz. Honek ereduaren errendimen<PERSON> hobe dezake, datuen sarbide azkarragoa ahalbidetuz. <PERSON>a ere, baliteke sistema guztietan behar bezala ez funtzionatzea eta disko-espazio handia kontsumitu dezake.", "Enable Message Rating": "Gaitu Mezuen Balorazioa", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "<PERSON><PERSON><PERSON>", "Enabled": "<PERSON><PERSON><PERSON>", "Endpoint URL": "", "Enforce Temporary Chat": "", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Ziurtatu zure CSV fitxategiak 4 zutabe dituela ordena honetan: <PERSON><PERSON><PERSON>, Posta elektronikoa, <PERSON><PERSON><PERSON><PERSON>, Rola.", "Enter {{role}} message here": "Sartu {{role}} mezua hemen", "Enter a detail about yourself for your LLMs to recall": "Sartu zure buruari buruzko xeheta<PERSON> bat LLMek go<PERSON>", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "Sartu api autentifi<PERSON><PERSON> ka<PERSON> (adib. erabiltzailea:pasahitza)", "Enter Application DN": "Sartu Aplikazioaren DN", "Enter Application DN Password": "Sartu Aplikazioaren DN Pasahitza", "Enter Bing Search V7 Endpoint": "Sartu Bing Bilaketa V7 Endpointua", "Enter Bing Search V7 Subscription Key": "Sartu Bing Bilaketa V7 Harpidetza Gakoa", "Enter BM25 Weight": "", "Enter Bocha Search API Key": "", "Enter Brave Search API Key": "Sartu Brave Bilaketa API Gakoa", "Enter certificate path": "Sartu ziurtagiriaren bidea", "Enter CFG Scale (e.g. 7.0)": "Sartu CFG Eskala (adib. 7.0)", "Enter Chunk Overlap": "Sartu Zatien <PERSON> (chunk overlap)", "Enter Chunk Size": "Sartu Zati Tamaina", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter Datalab Marker API Key": "", "Enter description": "Sartu deskribapena", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "", "Enter Document Intelligence Key": "", "Enter domains separated by commas (e.g., example.com,site.org)": "", "Enter Exa API Key": "", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter Github Raw URL": "Sartu Github Raw URLa", "Enter Google PSE API Key": "Sartu Google PSE API Gakoa", "Enter Google PSE Engine Id": "Sartu Google PSE Motor IDa", "Enter Image Size (e.g. 512x512)": "<PERSON><PERSON><PERSON> <PERSON><PERSON> (adib. 512x512)", "Enter Jina API Key": "Sartu Jina API Gakoa", "Enter Jupyter Password": "", "Enter Jupyter Token": "", "Enter Jupyter URL": "", "Enter Kagi Search API Key": "", "Enter Key Behavior": "", "Enter language codes": "Sartu hizkunt<PERSON> kodeak", "Enter Mistral API Key": "", "Enter Model ID": "Sartu Eredu IDa", "Enter model tag (e.g. {{modelTag}})": "Sartu eredu etiketa (adib. {{modelTag}})", "Enter Mojeek Search API Key": "Sartu Mojeek Bilaketa API Gakoa", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "Sartu Urrats Kopurua (adib. 50)", "Enter Perplexity API Key": "", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "", "Enter reasoning effort": "", "Enter Sampler (e.g. Euler a)": "Sartu Sampler-a (adib. Euler a)", "Enter Scheduler (e.g. Karras)": "Sartu <PERSON> (adib. Karra<PERSON>)", "Enter Score": "Sartu Puntuazioa", "Enter SearchApi API Key": "Sartu SearchApi API Gakoa", "Enter SearchApi Engine": "Sartu SearchApi Motorea", "Enter Searxng Query URL": "Sartu Searxng Kontsulta URLa", "Enter Seed": "Sartu Hazia", "Enter SerpApi API Key": "", "Enter SerpApi Engine": "", "Enter Serper API Key": "Sartu Serper API Gakoa", "Enter Serply API Key": "Sartu Serply API Gakoa", "Enter Serpstack API Key": "Sartu Serpstack API Gakoa", "Enter server host": "Sartu zerbitzariaren ostalaria", "Enter server label": "Sartu zerbitzariaren etiketa", "Enter server port": "Sartu zerbitzariaren portua", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "Sartu gelditze sekuentzia", "Enter system prompt": "Sartu sistema prompta", "Enter system prompt here": "", "Enter Tavily API Key": "Sartu Tavily API Gakoa", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "Sartu Tika Zerbitzari URLa", "Enter timeout in seconds": "", "Enter to Send": "", "Enter Top K": "Sartu Top K", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "Sartu URLa (adib. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Sartu URLa (adib. http://localhost:11434)", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your current password": "", "Enter Your Email": "Sartu Zure Posta Elektronikoa", "Enter Your Full Name": "Sartu Zure Izen-abizenak", "Enter your message": "Sartu zure mezua", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "", "Enter Your Password": "Sartu Z<PERSON>", "Enter Your Role": "Sartu Zure Rola", "Enter Your Username": "Sartu Zure Erabiltzaile-izena", "Enter your webhook URL": "", "Error": "Errorea", "ERROR": "ERROREA", "Error accessing Google Drive: {{error}}": "", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "Ebaluazioak", "Exa API Key": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Adibidea: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Adibidea: GUZTIAK", "Example: mail": "", "Example: ou=users,dc=foo,dc=example": "Adibidea: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Adibidea: sAMAccountName edo uid edo userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "", "Exclude": "Baztertu", "Execute code for analysis": "", "Executing **{{NAME}}**...": "", "Expand": "", "Experimental": "Esperimentala", "Explain": "", "Explore the cosmos": "Esploratu kos<PERSON>a", "Export": "Esportatu", "Export All Archived Chats": "Esportatu Artxibatutako Txat Guztiak", "Export All Chats (All Users)": "Esportatu Txat Guztiak (Erabiltzaile Guztiak)", "Export chat (.json)": "Esportatu txata (.json)", "Export Chats": "Esportatu Txatak", "Export Config to JSON File": "Esportatu Konfigurazioa JSON Fitxategira", "Export Functions": "Esportatu Funtzioak", "Export Models": "Esportatu Ereduak", "Export Presets": "Esportatu Aurrezarpenak", "Export Prompt Suggestions": "", "Export Prompts": "Esportatu Promptak", "Export to CSV": "Esportatu CSVra", "Export Tools": "Esportatu Tresnak", "External": "", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Failed to add file.": "<PERSON><PERSON> egin du fitxategia gehitzean.", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to copy link": "", "Failed to create API Key.": "<PERSON><PERSON> egin du API Gakoa sortzean.", "Failed to delete note": "", "Failed to fetch models": "", "Failed to load file content.": "", "Failed to read clipboard contents": "<PERSON><PERSON> egin du arbelaren edukia <PERSON>", "Failed to save connections": "", "Failed to save models configuration": "<PERSON>ts egin du ereduen konfigurazioa gordetzean", "Failed to update settings": "<PERSON>ts egin du ezarpenak eguneratzean", "Failed to upload file.": "<PERSON><PERSON> egin du fitxategia igotzean.", "Features": "", "Features Permissions": "", "February": "<PERSON><PERSON><PERSON><PERSON>", "Feedback Details": "", "Feedback History": "<PERSON><PERSON><PERSON><PERSON> Historia", "Feedbacks": "<PERSON><PERSON><PERSON><PERSON>", "Feel free to add specific details": "Gehitu xeh<PERSON>sun zehatzak nahi izanez gero", "File": "Fitxategia", "File added successfully.": "Fitxategia ongi gehitu da.", "File content updated successfully.": "Fitxategiaren edukia ongi eguneratu da.", "File Mode": "Fitxategi <PERSON>", "File not found.": "<PERSON>z da fitxategia aurkitu.", "File removed successfully.": "Fitxategia ongi ezabatu da.", "File size should not exceed {{maxSize}} MB.": "Fitxategiaren ta<PERSON>k ez luke {{maxSize}} MB gainditu behar.", "File Upload": "", "File uploaded successfully": "", "Files": "Fitxategiak", "Filter is now globally disabled": "Iragazkia orain globalki desgaituta dago", "Filter is now globally enabled": "Iragazkia orain <PERSON>ki gaituta dago", "Filters": "Iragaz<PERSON>ak", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Hatz-marka faltsutzea detektatu da: Ezin dira inizialak avatar gisa erabili. Profil irudi lehenetsia erabiliko da.", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Fluidly stream large external response chunks": "Modu jariagarrian transmititu kanpoko erantzun zati handiak", "Focus chat input": "<PERSON><PERSON><PERSON><PERSON> txataren sa<PERSON>a", "Folder deleted successfully": "<PERSON><PERSON><PERSON> ongi ezabatu da", "Folder name cannot be empty.": "Karpetaren izena ezin da hutsik egon.", "Folder name updated successfully": "Karpetaren izena ongi eguneratu da", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "Jarraibideak perfektuki jarraitu ditu", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "Sortu bide berriak", "Form": "Inprimakia", "Format your variables using brackets like this:": "Formateatu zure aldagaiak kortxeteak erabiliz honela:", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "", "Function": "Funtzioa", "Function Calling": "", "Function created successfully": "Funtzioa ongi sortu da", "Function deleted successfully": "Funtzioa ongi ezabatu da", "Function Description": "Funtzioaren Des<PERSON>", "Function ID": "<PERSON>tz<PERSON>", "Function imported successfully": "", "Function is now globally disabled": "Funtzioa orain globalki desgaituta dago", "Function is now globally enabled": "Funtzioa orain globalki gaituta dago", "Function Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Function updated successfully": "Funtzioa ongi eguneratu da", "Functions": "<PERSON><PERSON><PERSON><PERSON>", "Functions allow arbitrary code execution.": "Funtzioek kode arbitrarioa exekutatzea ahalbidetzen dute.", "Functions imported successfully": "Funtzioak ongi inportatu dira", "Gemini": "", "Gemini API Config": "", "Gemini API Key is required.": "", "General": "Orokorra", "Generate": "", "Generate an image": "", "Generate Image": "Sortu Irudia", "Generate prompt pair": "", "Generating search query": "Bilaketa kontsulta sortzen", "Generating...": "", "Get started": "<PERSON><PERSON>", "Get started with {{WEBUI_NAME}}": "Hasi {{W<PERSON><PERSON><PERSON>_NAME}}-rekin", "Global": "Globala", "Good Response": "<PERSON><PERSON><PERSON><PERSON>", "Google Drive": "", "Google PSE API Key": "Google PSE API Gakoa", "Google PSE Engine Id": "Google PSE Motor IDa", "Group created successfully": "<PERSON><PERSON><PERSON> ongi sortu da", "Group deleted successfully": "<PERSON><PERSON><PERSON> ongi eza<PERSON>u da", "Group Description": "Taldearen Deskribapena", "Group Name": "Taldearen <PERSON>", "Group updated successfully": "Taldea ongi eguneratu da", "Groups": "<PERSON><PERSON><PERSON>", "Haptic Feedback": "Feed<PERSON>", "Hello, {{name}}": "<PERSON><PERSON><PERSON>, {{name}}", "Help": "<PERSON><PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "Lagundu komunitatearen sailkapen onena sortzen zure feedback historia partekatuz!", "Hex Color": "<PERSON><PERSON>", "Hex Color - Leave empty for default color": "Hex <PERSON> - <PERSON><PERSON><PERSON> hutsik kolore lehenetsia erabiltzeko", "Hide": "Ezkutatu", "Hide from Sidebar": "", "Hide Model": "", "High Contrast Mode": "", "Home": "", "Host": "Ostalaria", "How can I help you today?": "<PERSON><PERSON>an lagun zait<PERSON>et gaur?", "How would you rate this response?": "<PERSON><PERSON> baloratuko zenuke erantzun hau?", "HTML": "", "Hybrid Search": "Bilaketa Hibridoa", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Onartzen dut irakurri dudala eta nire ekintzaren ondorioak ulertzen ditudala. Kode arbitrarioa exekutatzearekin lotutako arriskuez jabetzen naiz eta iturriaren fidagarritasuna egiaztatu dut.", "ID": "IDa", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "<PERSON><PERSON><PERSON> jakin-mina", "Image": "", "Image Compression": "", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "", "Image Generation (Experimental)": "<PERSON><PERSON><PERSON> (Esperimentala)", "Image Generation Engine": "<PERSON><PERSON><PERSON>", "Image Max Compression Size": "", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "", "Image Prompt Generation Prompt": "", "Image Settings": "<PERSON><PERSON><PERSON>", "Images": "<PERSON><PERSON><PERSON><PERSON>", "Import": "", "Import Chats": "Inportatu Txatak", "Import Config from JSON File": "Inportatu Konfigurazioa JSON Fitxategitik", "Import From Link": "", "Import Functions": "Inportatu Funtzioak", "Import Models": "Inportatu Ereduak", "Import Notes": "", "Import Presets": "Inportatu Aurrezarpenak", "Import Prompt Suggestions": "", "Import Prompts": "Inportatu Promptak", "Import Tools": "Inportatu Tresnak", "Include": "Sartu", "Include `--api-auth` flag when running stable-diffusion-webui": "Sartu `--api-auth` bandera stable-diffusion-webui exekutatzean", "Include `--api` flag when running stable-diffusion-webui": "Sartu `--api` bandera stable-diffusion-webui exekutatzean", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "Informazioa", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input commands": "Sartu komandoak", "Install from Github URL": "Instalatu Github URLtik", "Instant Auto-Send After Voice Transcription": "Bidalketa Automatiko Berehalakoa Ahots Transkripzioaren Ondoren", "Integration": "", "Interface": "Interfazea", "Invalid file content": "", "Invalid file format.": "Fitxategi formatu baliogabea.", "Invalid JSON file": "", "Invalid Tag": "Etiketa <PERSON>", "is typing...": "", "January": "<PERSON><PERSON><PERSON><PERSON>", "Jina API Key": "Jina API Gakoa", "join our Discord for help.": "batu gure Discord-era laguntzarako.", "JSON": "JSON", "JSON Preview": "JSON Aurrebista", "July": "<PERSON>ztail<PERSON>", "June": "<PERSON><PERSON><PERSON>", "Jupyter Auth": "", "Jupyter URL": "", "JWT Expiration": "JWT Iraungitzea", "JWT Token": "JWT Tokena", "Kagi Search API Key": "", "Keep in Sidebar": "", "Key": "Gakoa", "Keyboard shortcuts": "<PERSON><PERSON><PERSON><PERSON>", "Knowledge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Knowledge Access": "Ezagutzarako Sarbidea", "Knowledge created successfully.": "<PERSON><PERSON><PERSON><PERSON><PERSON> ongi sortu da.", "Knowledge deleted successfully.": "<PERSON><PERSON><PERSON><PERSON><PERSON> ongi ezabatu da.", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "<PERSON><PERSON><PERSON><PERSON><PERSON> ongi berre<PERSON> da.", "Knowledge updated successfully": "<PERSON><PERSON><PERSON><PERSON><PERSON> ongi eguneratu da.", "Kokoro.js (Browser)": "", "Kokoro.js Dtype": "", "Label": "Etiketa", "Landing Page Mode": "<PERSON><PERSON>", "Language": "Hizkuntza", "Language Locales": "", "Languages": "", "Last Active": "Azken Aktibitatea", "Last Modified": "Azken Aldaketa", "Last reply": "", "LDAP": "LDAP", "LDAP server updated": "LDAP zerbitzaria eguneratu da", "Leaderboard": "<PERSON><PERSON><PERSON><PERSON>", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "<PERSON><PERSON><PERSON> hutsik mugarik ez jartzeko", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "<PERSON><PERSON><PERSON> hutsik eredu guztiak sartzeko edo hautatu eredu zehatzak", "Leave empty to use the default prompt, or enter a custom prompt": "<PERSON><PERSON><PERSON> prompt le<PERSON><PERSON><PERSON> era<PERSON>, edo sartu prompt per<PERSON><PERSON><PERSON><PERSON> bat", "Leave model field empty to use the default model.": "", "License": "", "Light": "<PERSON><PERSON><PERSON>", "Listening...": "Entzuten...", "Llama.cpp": "", "LLMs can make mistakes. Verify important information.": "LLMek akatsak egin ditzakete. Egiaztatu informazio garrantzi<PERSON>a.", "Loader": "", "Loading Kokoro.js...": "", "Local": "Lokala", "Local Task Model": "", "Location access not allowed": "", "Lost": "<PERSON><PERSON><PERSON>", "LTR": "LTR", "Made by Open WebUI Community": "OpenWebUI Komunitateak egina", "Make password visible in the user interface": "", "Make sure to enclose them with": "Ziurtatu hauek gehitzen dituzula", "Make sure to export a workflow.json file as API format from ComfyUI.": "Ziurtatu workflow.json fitxategia API formatu gisa esportatzen duzula ComfyUI-tik.", "Manage": "Kudeatu", "Manage Direct Connections": "", "Manage Models": "", "Manage Ollama": "<PERSON><PERSON><PERSON>", "Manage Ollama API Connections": "Kudeatu Ollama API Konexioak", "Manage OpenAI API Connections": "Kudeatu OpenAI API Konexioak", "Manage Pipelines": "Kudeatu Pipeline-ak", "Manage Tool Servers": "", "March": "Mart<PERSON><PERSON>", "Markdown": "", "Max Speakers": "", "Max Upload Count": "<PERSON>rga kopuru maximoa", "Max Upload Size": "<PERSON><PERSON> tamaina maximoa", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Gehienez 3 modelo deskarga daitezke aldi berean. Saiatu berriro gero<PERSON>.", "May": "<PERSON><PERSON><PERSON>", "Memories accessible by LLMs will be shown here.": "LLMek atzitu ditzaketen memoriak hemen erakutsiko dira.", "Memory": "Memoria", "Memory added successfully": "Memoria ongi gehitu da", "Memory cleared successfully": "Memoria ongi garbitu da", "Memory deleted successfully": "Memoria ongi ezabatu da", "Memory updated successfully": "Memoria ongi eguneratu da", "Merge Responses": "<PERSON><PERSON>", "Merged Response": "Erantzun bateratua", "Message rating should be enabled to use this feature": "Mezuen balorazioa gaitu behar da funtzionalitate hau erabiltzeko", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Esteka sortu ondoren bidaltzen dituzun mezuak ez dira partekatuko. URLa duten erabiltzaileek partekatutako txata ikusi ahal izango dute.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "Modeloa", "Model '{{modelName}}' has been successfully downloaded.": "'{{modelName}}' modeloa ongi deskargatu da.", "Model '{{modelTag}}' is already in queue for downloading.": "'{{modelTag}}' model<PERSON> da<PERSON><PERSON>ga il<PERSON> dago.", "Model {{modelId}} not found": "{{modelId}} modeloa ez da aurkitu", "Model {{modelName}} is not vision capable": "{{modelName}} modeloak ez du ikusmen gaitasunik", "Model {{name}} is now {{status}}": "{{name}} modeloa orain {{status}} dago", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts file inputs": "", "Model accepts image inputs": "Modeloak i<PERSON> on<PERSON>zen ditu", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "<PERSON><PERSON> ongi sortu da!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Modeloaren fitxategi sistemaren bidea detektatu da. Modeloaren izen laburra behar da eguneratzeko, ezin da jarrai<PERSON>.", "Model Filtering": "<PERSON>o iraga<PERSON>", "Model ID": "Modelo ID", "Model IDs": "<PERSON><PERSON>", "Model Name": "Modeloaren izena", "Model not selected": "Ez da modelorik hautatu", "Model Params": "Modelo parametroak", "Model Permissions": "<PERSON><PERSON>", "Model unloaded successfully": "", "Model updated successfully": "<PERSON><PERSON> ongi eguneratu da", "Model(s) do not support file upload": "", "Modelfile Content": "<PERSON><PERSON><PERSON> edukia", "Models": "Modeloak", "Models Access": "Modeloen sarbidea", "Models configuration saved successfully": "Modeloen konfigurazioa ongi gorde da", "Models Public Sharing": "", "Mojeek Search API Key": "Mojeek bilaketa API gakoa", "more": "gehiago", "More": "Gehiago", "My Notes": "", "Name": "<PERSON><PERSON><PERSON>", "Name your knowledge base": "Izendatu zure ezagutza-basea", "Native": "", "New Chat": "<PERSON><PERSON><PERSON> be<PERSON>a", "New Folder": "", "New Function": "", "New Note": "", "New Password": "<PERSON><PERSON><PERSON><PERSON>", "New Tool": "", "new-channel": "", "Next message": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "<PERSON>z da edukirik aurkitu", "No content found in file.": "", "No content to speak": "Ez dago hitz egiteko edukirik", "No distance available": "<PERSON>z dago <PERSON><PERSON>", "No feedbacks found": "Ez da iritzirik aurkitu", "No file selected": "Ez da fitxategirik hautatu", "No groups with access, add a group to grant access": "Ez dago sarbidea duen talderik, gehitu talde bat sarbidea emateko", "No HTML, CSS, or JavaScript content found.": "<PERSON><PERSON> da H<PERSON>, CSS, edo JavaScript edukirik aurkitu.", "No inference engine with management support found": "", "No knowledge found": "Ez da ezagutzarik aurkitu", "No memories to clear": "", "No model IDs": "<PERSON>z dago modelo <PERSON>", "No models found": "Ez da modelorik aurkitu", "No models selected": "Ez da modelorik hautatu", "No Notes": "", "No results found": "Ez da emaitzarik aurkitu", "No search query generated": "Ez da bilaketa kontsultarik sortu", "No source available": "<PERSON>z dago itur<PERSON><PERSON>", "No users were found.": "<PERSON>z da erabiltzailerik aurkitu.", "No valves to update": "<PERSON>z dago bal<PERSON>rik eguneratzeko", "None": "Bat ere ez", "Not factually correct": "Ez da faktikoki zuzena", "Not helpful": "<PERSON>z da lagungarria", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Oharra: Gutxieneko puntuazio bat e<PERSON><PERSON><PERSON> baduzu, bilaketak gutxieneko puntuazioa baino handiagoa edo berdina duten dokumentuak soilik itzuliko ditu.", "Notes": "Oharrak", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Jakinarazpenak", "November": "<PERSON><PERSON><PERSON>", "OAuth ID": "OAuth ID", "October": "<PERSON><PERSON><PERSON>", "Off": "<PERSON><PERSON><PERSON>", "Okay, Let's Go!": "<PERSON><PERSON>, <PERSON><PERSON>!", "OLED Dark": "OLED iluna", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "Ollama API ezarpenak eguneratu dira", "Ollama Version": "<PERSON>lla<PERSON> bertsioa", "On": "Piztuta", "OneDrive": "", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "Karaktere alfanumerikoak eta marratxoak soilik onartzen dira", "Only alphanumeric characters and hyphens are allowed in the command string.": "Karaktere alfanumerikoak eta marratxoak soilik onartzen dira komando katean.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Bildumak soilik edita daitezke, sortu ezagutza-base berri bat dokumentuak editatzeko/gehitzeko.", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "Baimena duten erabiltzaile eta talde hautatuek soilik sar daitezke", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Ui! URLa ez da baliozkoa. Mesedez, egiaztatu eta saiatu berriro.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Ui! Oraindik fitxategiak kargatzen ari dira. <PERSON><PERSON><PERSON>, itxaron karga amaitu arte.", "Oops! There was an error in the previous response.": "Ui! Errore bat egon da aurreko erantzu<PERSON>n.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Ui! Onartzen ez den metodo bat erabiltzen ari zara (frontend soilik). Mesedez, zerbitzatu WebUI-a backendetik.", "Open file": "<PERSON><PERSON><PERSON> fit<PERSON>", "Open in full screen": "<PERSON><PERSON><PERSON> pantaila osoan", "Open modal to configure connection": "", "Open new chat": "<PERSON><PERSON><PERSON> txat berria", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "Open WebUI-k faster-whisper erabiltzen du barnean.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI-k SpeechT5 eta CMU Arctic hiztun txertaketak erabiltzen ditu.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Open WebUI bertsioa (v{{OPEN_WEBUI_VERSION}}) beharrezko bertsioa (v{{REQUIRED_VERSION}}) baino baxuagoa da", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API konfigurazioa", "OpenAI API Key is required.": "OpenAI API gakoa beharrezkoa da.", "OpenAI API settings updated": "OpenAI API ezarpenak eguneratu dira", "OpenAI URL/Key required.": "OpenAI URL/Gakoa beharrez<PERSON>a da.", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "edo", "Organize your users": "An<PERSON><PERSON><PERSON> zure erabiltzaileak", "Other": "Bestelakoa", "OUTPUT": "IRTEERA", "Output format": "Irteera formatua", "Output Format": "", "Overview": "Ikuspegi orokorra", "page": "orria", "Paginate": "", "Parameters": "", "Password": "<PERSON><PERSON><PERSON><PERSON>", "Paste Large Text as File": "Itsatsi testu luzea fitxategi gisa", "PDF document (.pdf)": "PDF dokumentua (.pdf)", "PDF Extract Images (OCR)": "PDF irudiak erauzi (OCR)", "pending": "zain", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "Baimena ukatu da multimedia gailuak atzitzean", "Permission denied when accessing microphone": "Baimena ukatu da mikrofonoa atzitzean", "Permission denied when accessing microphone: {{error}}": "Baimena ukatu da mikrofonoa atzitzean: {{error}}", "Permissions": "Baimenak", "Perplexity API Key": "", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "Pertsonalizazioa", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "<PERSON><PERSON><PERSON><PERSON>", "Pinned": "<PERSON><PERSON><PERSON><PERSON>", "Pioneer insights": "Ikuspegi <PERSON>", "Pipeline deleted successfully": "Pipeline-a ongi e<PERSON>u da", "Pipeline downloaded successfully": "Pipeline-a ongi <PERSON>u da", "Pipelines": "Pipeline-ak", "Pipelines Not Detected": "Ez da Pipeline-rik detektatu", "Pipelines Valves": "Pipeline balbulak", "Plain text (.md)": "", "Plain text (.txt)": "<PERSON><PERSON> (.txt)", "Playground": "Jolaslekua", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "<PERSON><PERSON><PERSON>, berrikusi arretaz hurrengo oharrak:", "Please do not close the settings page while loading the model.": "", "Please enter a prompt": "<PERSON><PERSON><PERSON>, sartu prompt bat", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "<PERSON><PERSON><PERSON>, bete eremu guztiak.", "Please select a model first.": "", "Please select a model.": "", "Please select a reason": "Mesedez, hautatu arrazoi bat", "Port": "<PERSON><PERSON>", "Positive attitude": "Jarrera positiboa", "Prefix ID": "Aurrizki ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Aurrizki IDa erabiltzen da beste konexioekin gatazkak saihesteko modelo IDei aurrizki bat gehituz - utzi hutsik desgaitzeko", "Prevent file creation": "", "Preview": "", "Previous 30 days": "Aurreko 30 egunak", "Previous 7 days": "Aurreko 7 egunak", "Previous message": "", "Private": "", "Profile Image": "<PERSON>il <PERSON>", "Prompt": "", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt-a (adib. <PERSON><PERSON><PERSON> dibertiga<PERSON> bat Erromatar Inperioari buruz)", "Prompt Autocompletion": "", "Prompt Content": "Prompt edukia", "Prompt created successfully": "Prompt-a ongi sortu da", "Prompt suggestions": "Prompt iradokizunak", "Prompt updated successfully": "Prompt-a ongi eguneratu da", "Prompts": "Prompt-ak", "Prompts Access": "Prompt sarbidea", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "<PERSON><PERSON><PERSON> \"{{searchValue}}\" Ollama.com-etik", "Pull a model from Ollama.com": "<PERSON><PERSON><PERSON> modelo bat Ollama.com-etik", "Query Generation Prompt": "<PERSON><PERSON><PERSON><PERSON> sortzeko prompt-a", "RAG Template": "RAG txantiloia", "Rating": "Balorazioa", "Re-rank models by topic similarity": "Berrantolatu modeloak gai antzekotasunaren arabera", "Read": "", "Read Aloud": "<PERSON><PERSON><PERSON>en", "Reason": "", "Reasoning Effort": "", "Record": "", "Record voice": "<PERSON><PERSON><PERSON><PERSON> ah<PERSON>a", "Redirecting you to Open WebUI Community": "OpenWebUI Komunitatera berbideratzen", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Egin erreferentzia zure buruari \"Erabiltzaile\" gisa (adib., \"Erabiltzailea gaztelania ikasten ari da\")", "References from": "Erreferentziak hemendik", "Refused when it shouldn't have": "<PERSON><PERSON>u duenean ukatu behar ez zu<PERSON>an", "Regenerate": "<PERSON><PERSON><PERSON><PERSON>", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "<PERSON><PERSON>", "Releases": "", "Relevance": "Garrantzia", "Relevance Threshold": "", "Remove": "<PERSON><PERSON>", "Remove {{MODELID}} from list.": "", "Remove Model": "Kendu modeloa", "Remove this tag from list": "", "Rename": "Berrizendatu", "Reorder Models": "Berrantolatu modeloak", "Reply in Thread": "", "Reranking Engine": "", "Reranking Model": "Berrantolatze modeloa", "Reset": "<PERSON><PERSON><PERSON><PERSON>", "Reset All Models": "", "Reset Upload Directory": "<PERSON><PERSON><PERSON><PERSON> karga direktorioa", "Reset Vector Storage/Knowledge": "Be<PERSON><PERSON>ri bektore bilteg<PERSON>/eza<PERSON><PERSON><PERSON>", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Erantzunen jakinarazpenak ezin dira aktibatu webgunearen baimenak ukatu direlako. <PERSON><PERSON><PERSON>, bisitatu zure nabigatzailearen ezarpenak beharrezko sarbidea emateko.", "Response splitting": "Erant<PERSON><PERSON> banaketa", "Response Watermark": "", "Result": "<PERSON><PERSON><PERSON>", "Retrieval": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "<PERSON><PERSON> aber<PERSON>o sa<PERSON> txa<PERSON>", "RK": "RK", "Role": "Rola", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "Exekutatu", "Running": "Exekutatzen", "Save": "Gorde", "Save & Create": "Gorde eta sortu", "Save & Update": "Gorde eta eguneratu", "Save As Copy": "Gorde kopia gisa", "Save Tag": "<PERSON><PERSON> etiketa", "Saved": "Gordeta", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Txat erregistroak zuzenean zure nabigatzailearen biltegian gordetzea ez da jadanik onartzen. <PERSON><PERSON><PERSON>, hartu une bat zure txat erregistroak deskargatu eta ezabatzeko beheko botoia sakatuz. <PERSON><PERSON> kezkat<PERSON>, zure txat erregistroak erraz inportatu ditzakezu berriro backendera honen bidez", "Scroll On Branch Change": "", "Search": "Bilatu", "Search a model": "Bilatu modelo bat", "Search Base": "Bilaket<PERSON> o<PERSON>a", "Search Chats": "Bilatu txatak", "Search Collection": "<PERSON><PERSON><PERSON> bilduma", "Search Filters": "Bilaketa iragazkiak", "search for tags": "bilatu et<PERSON>k", "Search Functions": "<PERSON><PERSON><PERSON>", "Search Knowledge": "<PERSON><PERSON><PERSON>", "Search Models": "Bilatu modeloak", "Search options": "<PERSON><PERSON><PERSON><PERSON> au<PERSON>", "Search Prompts": "Bilatu prompt-ak", "Search Result Count": "<PERSON><PERSON>ket<PERSON> emait<PERSON> kop<PERSON>a", "Search the internet": "", "Search Tools": "Bilaketa tresnak", "SearchApi API Key": "SearchApi API gakoa", "SearchApi Engine": "SearchApi motorra", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "\"{{searchQuery}}\" bilatzen", "Searching Knowledge for \"{{searchQuery}}\"": "\"{{searchQuery}}\"rent<PERSON>o eza<PERSON>a bilatzen", "Searching the web...": "", "Searxng Query URL": "Searxng kontsulta URLa", "See readme.md for instructions": "Ikusi readme.md argibideetarako", "See what's new": "Ikus<PERSON>", "Seed": "Hazia", "Select a base model": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "Select a engine": "Hautatu motor bat", "Select a function": "Hautatu funtzio bat", "Select a group": "Hautatu talde bat", "Select a model": "Hautatu modelo bat", "Select a pipeline": "Hautatu pipeline bat", "Select a pipeline url": "Hautatu pipeline url bat", "Select a tool": "Hautatu tresna bat", "Select an auth method": "", "Select an Ollama instance": "", "Select Engine": "Hautatu motorra", "Select Knowledge": "<PERSON><PERSON><PERSON>", "Select only one model to call": "<PERSON><PERSON><PERSON> <PERSON>o b<PERSON><PERSON>o", "Selected model(s) do not support image inputs": "Hautatutako modelo(e)k ez dute irudi sarrerarik onartzen", "Semantic distance to query": "Kontsultarako distantzia semantikoa", "Send": "<PERSON><PERSON><PERSON>", "Send a Message": "<PERSON><PERSON><PERSON> mezu bat", "Send message": "<PERSON><PERSON><PERSON> mezua", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Bidaltzen du `stream_options: { include_usage: true }` eskaeran.\nOnartutako hornitzaileek token erabileraren informazioa itzuliko dute erantzunean e<PERSON><PERSON> da<PERSON>.", "September": "<PERSON><PERSON>", "SerpApi API Key": "", "SerpApi Engine": "", "Serper API Key": "Serper API gakoa", "Serply API Key": "Serply API gakoa", "Serpstack API Key": "Serpstack API gakoa", "Server connection verified": "Zerbitzari konexioa egiaztatuta", "Set as default": "<PERSON><PERSON><PERSON> le<PERSON> gisa", "Set CFG Scale": "Ezarri CFG eskala", "Set Default Model": "<PERSON><PERSON><PERSON> modeloa", "Set embedding model": "<PERSON><PERSON><PERSON> t<PERSON> modeloa", "Set embedding model (e.g. {{model}})": "<PERSON><PERSON><PERSON> t<PERSON>e modeloa (adib. {{model}})", "Set Image Size": "<PERSON><PERSON><PERSON>", "Set reranking model (e.g. {{model}})": "<PERSON><PERSON><PERSON> model<PERSON> (adib. {{model}})", "Set Sampler": "<PERSON><PERSON><PERSON>", "Set Scheduler": "<PERSON><PERSON><PERSON>", "Set Steps": "<PERSON><PERSON><PERSON>", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "E<PERSON><PERSON> kalkulurako erabilitako langile harien kopurua. Aukera honek kontrolatzen du zenbat hari erabiltzen diren sarrerako eskaerak aldi berean prozesatzeko. Balio hau handitzeak errendimendua hobetu dezake konkurrentzia altuko lan-kargetan, baina CPU baliabide gehiago kontsumitu ditzake.", "Set Voice": "<PERSON><PERSON><PERSON>", "Set whisper model": "E<PERSON>ri whisper modeloa", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Ezartzen ditu erabiliko diren gelditzeko sekuent<PERSON>k. Patroi hau aurkitz<PERSON> denean, LLMak testua sortzeari utziko dio eta itzuli egingo da. Gelditzeko patroi anitz ezar daitezke modelfile batean gelditzeko parametro anitz zehaztuz.", "Settings": "Ezarpenak", "Settings saved successfully!": "Ezarpenak ongi gorde dira!", "Share": "Partekatu", "Share Chat": "Partekatu txata", "Share to Open WebUI Community": "Partekatu OpenWebUI komunitatearekin", "Sharing Permissions": "", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "<PERSON><PERSON><PERSON><PERSON>", "Show \"What's New\" modal on login": "Era<PERSON><PERSON><PERSON> \"Berritasunak\" modala saioa hastean", "Show Admin Details in Account Pending Overlay": "Erakutsi administratzaile xehetasunak kontu zain geruzan", "Show All": "", "Show Less": "", "Show Model": "", "Show shortcuts": "<PERSON><PERSON><PERSON><PERSON>", "Show your support!": "<PERSON><PERSON><PERSON>i zure babesa!", "Showcased creativity": "<PERSON><PERSON><PERSON><PERSON><PERSON> so<PERSON>", "Sign in": "<PERSON><PERSON> sa<PERSON>a", "Sign in to {{WEBUI_NAME}}": "<PERSON>i sa<PERSON>a {{WEBUI_NAME}}-n", "Sign in to {{WEBUI_NAME}} with LDAP": "<PERSON>i sa<PERSON>a {{WEBUI_NAME}}-n LDAP bidez", "Sign Out": "Amaitu sa<PERSON>a", "Sign up": "Erregistratu", "Sign up to {{WEBUI_NAME}}": "Erregistratu {{WEBUI_NAME}}-n", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "", "Signing in to {{WEBUI_NAME}}": "{{WEBUI_NAME}}-n saioa hasten", "sk-1234": "", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "Itur<PERSON>", "Speech Playback Speed": "Ahots erreproduk<PERSON> a<PERSON>", "Speech recognition error: {{error}}": "<PERSON>ots ezagutze errorea: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "Ahotsetik-testura motorra", "Stop": "<PERSON><PERSON><PERSON><PERSON>", "Stop Generating": "", "Stop Sequence": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Stream Chat Response": "Transmititu txat erantzuna", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "STT modeloa", "STT Settings": "STT ezarpenak", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "Azpititulua (adib. Erromatar Inperioari buruz)", "Success": "Arrakasta", "Successfully updated.": "<PERSON><PERSON> e<PERSON> da.", "Suggested": "Iradokitua", "Support": "<PERSON><PERSON><PERSON><PERSON>", "Support this plugin:": "Lagundu plugin hau:", "Supported MIME Types": "", "Sync directory": "Sinkronizatu direktorioa", "System": "Sistema", "System Instructions": "<PERSON><PERSON><PERSON>", "System Prompt": "Sistema prompta", "Tags": "", "Tags Generation": "", "Tags Generation Prompt": "<PERSON><PERSON><PERSON><PERSON> prompta", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "", "Tap to interrupt": "<PERSON><PERSON><PERSON>", "Task Model": "", "Tasks": "", "Tavily API Key": "Tavily <PERSON> gakoa", "Tavily Extract Depth": "", "Tell us more:": "Kontatu gehiago:", "Temperature": "Tenperatura", "Temporary Chat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> txata", "Text Splitter": "<PERSON><PERSON>", "Text-to-Speech": "", "Text-to-Speech Engine": "Testutik-ahotsera motorra", "Thanks for your feedback!": "Eskerrik asko zure iritzia emateagatik!", "The Application Account DN you bind with for search": "Bilaketarako lotzen duzun aplikazio kontuaren DN-a", "The base to search for users": "Erabiltzaileak bilatzeko o<PERSON>rria", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Plugin honen atzean dauden garatzaileak komunitateko boluntario sutsuak dira. Plugin hau baliagarria iruditz<PERSON> bazaizu, mesedez kontuan hartu bere garapenean laguntzea.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Ebaluazio sailkapena Elo sailkapen sisteman oinarritzen da eta denbora errealean eguneratzen da.", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "", "The LDAP attribute that maps to the username that users use to sign in.": "Erabiltzaileek saioa hasteko erabiltzen duten erabiltzaile-izenarekin mapeatzen den LDAP atributua.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Sailkapena beta fasean dago, eta balorazioen kalkuluak doitu ditzakegu algoritmoa fintzen dugun heinean.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Fitxategiaren gehienezko tamaina MB-tan. Fitxategiaren tamainak muga hau gainditzen badu, fitxategia ez da kargatuko.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Txatean aldi berean erabili daitezkeen fitxategien gehienezko kopurua. Fitxategi kopuruak muga hau gainditzen badu, fitxategiak ez dira kargatuko.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Puntuazioa 0.0 (0%) eta 1.0 (100%) arteko balio bat izan behar da.", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "Gaia", "Thinking...": "Pentsatzen...", "This action cannot be undone. Do you wish to continue?": "E<PERSON><PERSON>a hau ezin da desegin. Jarraitu nahi duzu?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won't appear in history and your messages will not be saved.": "", "This chat won’t appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Honek zure elkarrizketa baliotsuak modu seguruan zure backend datu-basean gordeko direla ziurtatzen du. Eskerrik asko!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Hau funtzionalitate esperimental bat da, baliteke espero bezala ez funtzionatzea eta edozein unetan aldaketak izatea.", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Aukera honek bilduman dauden fitxategi guztiak ezabatuko ditu eta berriki kargatutako fitxategiekin ordezkatuko ditu.", "This response was generated by \"{{model}}\"": "Erantzun hau \"{{model}}\" modeloak sortu du", "This will delete": "<PERSON><PERSON> du", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "<PERSON>ek <strong>{{NAME}}</strong> eta <strong>bere eduki guztiak</strong> e<PERSON><PERSON><PERSON><PERSON> <PERSON>.", "This will delete all models including custom models": "Honek modelo guztiak e<PERSON>, modelo pertsonalizatuak barne", "This will delete all models including custom models and cannot be undone.": "Honek modelo guztiak e<PERSON>, modelo pertsonalizatuak barne, eta ezin da desegin.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Honek ezagutza-basea berrezarri eta fitxategi guztiak sinkronizatuko ditu. Jarraitu nahi duzu?", "Thorough explanation": "<PERSON><PERSON><PERSON> sakona", "Thought for {{DURATION}}": "", "Thought for {{DURATION}} seconds": "", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "<PERSON><PERSON> zerbitzariaren URLa beharrezkoa da.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Aholkua: <PERSON><PERSON><PERSON><PERSON> aldagai slot anitz jarraian txateko sarreran tabuladore tekla sakatuz ordezpen bakoitzaren ondoren.", "Title": "<PERSON><PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "<PERSON><PERSON><PERSON><PERSON> (adib. Kontatu datu dibertigarri bat)", "Title Auto-Generation": "Izenburuen sorrera automatikoa", "Title cannot be an empty string.": "Izenburua ezin da kate hutsa izan.", "Title Generation": "", "Title Generation Prompt": "<PERSON><PERSON><PERSON><PERSON> prompta", "TLS": "TLS", "To access the available model names for downloading,": "<PERSON><PERSON><PERSON><PERSON><PERSON> es<PERSON> dauden modelo izenak <PERSON>zitz<PERSON>,", "To access the GGUF models available for downloading,": "Deskargatzeko <PERSON> dauden GGUF modeloak atzitzeko,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "WebUI-a at<PERSON><PERSON><PERSON><PERSON>, mesedez jarri harremanetan administratzailearekin. Administratzaileek erabiltzaileen egoerak kudeatu ditzakete Admin Paneletik.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Ezagutza-basea hemen era<PERSON>, gehitu e<PERSON><PERSON> \"<PERSON>zagutz<PERSON>\" lan-eremura.", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "<PERSON><PERSON> pri<PERSON><PERSON><PERSON><PERSON>, zure feedback<PERSON>k bakarrik partekatzen dira balorazioak, <PERSON><PERSON>, etiketak eta metadatuak—zure txat erregistroak pribatuak dira eta ez dira sartzen.", "To select actions here, add them to the \"Functions\" workspace first.": "Ekintzak hemen hautatzeko, gehitu itza<PERSON> lehen<PERSON> \"Funtzioak\" lan-eremura.", "To select filters here, add them to the \"Functions\" workspace first.": "Iragazkiak hemen hautatzeko, gehitu itza<PERSON> lehen<PERSON> \"Funtzioak\" lan-eremura.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Tresna-multzoak hemen hautatzeko, gehitu itza<PERSON> lehen<PERSON> \"Tresnak\" lan-eremura.", "Toast notifications for new updates": "Toast jakinarazpenak eguneraketa berrie<PERSON>", "Today": "<PERSON><PERSON><PERSON>", "Toggle search": "", "Toggle settings": "Aldatu <PERSON>", "Toggle sidebar": "Aldatu alboko barra", "Toggle whether current connection is active.": "", "Token": "Tokena", "Too verbose": "Luzeegia", "Tool created successfully": "<PERSON><PERSON><PERSON> ongi sortu da", "Tool deleted successfully": "<PERSON><PERSON><PERSON> ongi e<PERSON> da", "Tool Description": "Tresnaren deskribapena", "Tool ID": "Tresna ID", "Tool imported successfully": "<PERSON><PERSON>na ongi inportatu da", "Tool Name": "Tresnaren izena", "Tool Servers": "", "Tool updated successfully": "<PERSON><PERSON><PERSON> ongi eguneratu da", "Tools": "Tresnak", "Tools Access": "Tresnen sarbidea", "Tools are a function calling system with arbitrary code execution": "Tresnak kode arbitrarioa exekutatzeko funtzio deitzeko sistema bat dira", "Tools Function Calling Prompt": "", "Tools have a function calling system that allows arbitrary code execution.": "Tresnek kode arbitrarioa exekutatzeko aukera ematen duen funtzio deitzeko sistema dute.", "Tools Public Sharing": "", "Top K": "<PERSON><PERSON>", "Top K Reranker": "", "Transformers": "Transformatzaileak", "Trouble accessing Ollama?": "Arazoak Ollama atzitzeko?", "Trust Proxy Environment": "", "TTS Model": "TTS modeloa", "TTS Settings": "TTS ezarpenak", "TTS Voice": "TTS ahotsa", "Type": "<PERSON><PERSON>", "Type Hugging Face Resolve (Download) URL": "<PERSON><PERSON><PERSON> Face Resolve (Deskarga) URLa", "Uh-oh! There was an issue with the response.": "", "UI": "Erabiltzaile interfazea", "Unarchive All": "Desartxibatu guztiak", "Unarchive All Archived Chats": "Desartxibatu artxibatutako txat guztiak", "Unarchive Chat": "Desartxibatu txata", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "Askatu misterioak", "Unpin": "<PERSON><PERSON>", "Unravel secrets": "<PERSON><PERSON><PERSON>", "Untagged": "Etiketatu gabea", "Untitled": "", "Update": "Eguneratu", "Update and Copy Link": "Eguneratu eta kopiatu esteka", "Update for the latest features and improvements.": "Eguneratu azken ezaugarri eta hobekuntzak izateko.", "Update password": "<PERSON><PERSON><PERSON><PERSON>", "Updated": "Eguneratuta", "Updated at": "Noiz eguneratuta", "Updated At": "Noiz eguneratuta", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "", "Upload": "<PERSON><PERSON><PERSON>", "Upload a GGUF model": "Kargatu GGUF modelo bat", "Upload Audio": "", "Upload directory": "<PERSON><PERSON> direkt<PERSON><PERSON>", "Upload files": "<PERSON><PERSON><PERSON>", "Upload Files": "<PERSON><PERSON><PERSON>", "Upload Pipeline": "<PERSON><PERSON><PERSON>-a", "Upload Progress": "<PERSON><PERSON><PERSON>", "URL": "URLa", "URL Mode": "URL modua", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "Erabili '#' prompt sa<PERSON>an zure ezagutza kargatu eta sartzeko.", "Use Gravatar": "Era<PERSON><PERSON> Gravatar", "Use groups to group your users and assign permissions.": "Erabili taldeak zure erabiltzaileak taldekatu eta baimenak esleitzeko.", "Use Initials": "Erabili inizialak", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "erabiltz<PERSON><PERSON>", "User": "Erabiltz<PERSON><PERSON>", "User location successfully retrieved.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kokapena ongi berre<PERSON>tu da.", "User Webhooks": "", "Username": "Erabiltzaile-izena", "Users": "Erabiltzaileak", "Using the default arena model with all models. Click the plus button to add custom models.": "Arena modelo lehenetsia erabiltzen modelo guztiekin. Egin klik plus botoian modelo pertsonalizatuak gehitzeko.", "Utilize": "Erabili", "Valid time units:": "Denbora unitate baliozkoak:", "Valves": "Balbulak", "Valves updated": "Balbulak eguneratuta", "Valves updated successfully": "Balbulak ongi eguneratu dira", "variable": "aldagaia", "variable to have them replaced with clipboard content.": "aldagaia arbeleko edukiarekin ordezkatzeko.", "Verify Connection": "", "Verify SSL Certificate": "", "Version": "<PERSON><PERSON><PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "{{totalVersions}}-tik {{selectedVersion}}. bertsioa", "View Replies": "", "View Result from **{{NAME}}**": "", "Visibility": "Ikusgarritasuna", "Vision": "", "Voice": "<PERSON><PERSON><PERSON>", "Voice Input": "<PERSON><PERSON> sarrera", "Voice mode": "", "Warning": "Abisua", "Warning:": "Abisua:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Abisua: <PERSON><PERSON> gait<PERSON>ak erabiltzaileei zerbitzarian kode arbitrarioa kargatzea ahalbide<PERSON>ko die.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Abisua: <PERSON><PERSON> t<PERSON>tze modeloa eguneratu edo aldatzen baduzu, dokumentu guztiak berriz inportatu beharko di<PERSON>zu.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "", "Web": "Weba", "Web API": "Web APIa", "Web Loader Engine": "", "Web Search": "Web bilaketa", "Web Search Engine": "Web bilaketa motorra", "Web Search in Chat": "", "Web Search Query Generation": "", "Webhook URL": "Webhook URLa", "WebUI Settings": "WebUI ezarpenak", "WebUI URL": "", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI-k eskaerak egingo ditu \"{{url}}/api/chat\"-era", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI-k eskaerak egingo ditu \"{{url}}/chat/completions\"-era", "Weight of BM25 Retrieval": "", "What are you trying to achieve?": "Zer lortu nahi duzu?", "What are you working on?": "<PERSON><PERSON>an ari zara lanean?", "What's New in": "<PERSON>er berri honetan:", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "<PERSON><PERSON><PERSON>, modeloak txat mezu bakoitzari denbora errealean erantzungo dio, erantzun bat sortuz erabiltzaileak mezua bidaltzen duen bezain laster. Modu hau erabilgarria da zuzeneko txat aplik<PERSON><PERSON><PERSON>ko, baina errendimenduan eragina izan dezake hardware motelagoan.", "wherever you are": "zauden to<PERSON>an zaudela", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "Whisper (Lokala)", "Why?": "Zergatik?", "Widescreen Mode": "Pantaila zabaleko modua", "Won": "Ira<PERSON><PERSON> du", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "Lan-eremua", "Workspace Permissions": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON> bai<PERSON>ak", "Write": "", "Write a prompt suggestion (e.g. Who are you?)": "<PERSON><PERSON><PERSON> prompt i<PERSON><PERSON><PERSON><PERSON> bat (adib. Nor zara zu?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Idatzi 50 hitzeko laburpen bat [gaia edo gako-hitza] laburbiltzen duena.", "Write something...": "<PERSON><PERSON><PERSON>...", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "<PERSON><PERSON>", "You": "<PERSON><PERSON>", "You are currently using a trial license. Please contact support to upgrade your license.": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Gehienez {{maxCount}} fitxategirekin txateatu dezakezu aldi berean.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "LLMekin dituzun interakzioak pertsonalizatu ditzakezu memoriak gehituz beheko 'Kudeatu' boto<PERSON><PERSON> bidez, lagungarriagoak eta zuretzat egokituagoak eginez.", "You cannot upload an empty file.": "<PERSON><PERSON> duzu fitxategi huts bat kargatu.", "You do not have permission to upload files.": "<PERSON>z duzu fitxategiak kargatzeko baimenik.", "You have no archived conversations.": "Ez duzu artxibatutako <PERSON>k.", "You have shared this chat": "Txat hau partekatu duzu", "You're a helpful assistant.": "Laguntzaile baliagarri bat zara.", "You're now logged in.": "<PERSON><PERSON> sa<PERSON>a hasita duzu.", "Your account status is currently pending activation.": "Zure kontuaren egoera aktibazio zain dago.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Zure ekarpen osoa zuzenean plugin garatzaileari joango <PERSON>; Open WebUI-k ez du ehunekorik hartzen. <PERSON><PERSON> ere, aukeratutako finantzaketa plataformak bere komisioak izan ditzake.", "Youtube": "Youtube", "Youtube Language": "", "Youtube Proxy URL": ""}