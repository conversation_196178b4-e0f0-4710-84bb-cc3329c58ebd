{"-1 for no limit, or a positive integer for a specific limit": "-1은 제한 없음을 의미하며, 양의 정수는 특정 제한을 나타냅니다", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "만료 없음은 's', 'm', 'h', 'd', 'w' 아니면 '-1' 중 하나를 사용하세요.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(예: `sh webui.sh --api --api-auth 사용자이름_비밀번호`)", "(e.g. `sh webui.sh --api`)": "(예: `sh webui.sh --api`)", "(latest)": "(최근)", "(leave blank for to use commercial endpoint)": "", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "사용 가능한 도구 {{COUNT}}개", "{{COUNT}} hidden lines": "숨겨진 줄 {{COUNT}}개", "{{COUNT}} Replies": "답글 {{COUNT}}개", "{{user}}'s Chats": "{{user}}의 채팅", "{{webUIName}} Backend Required": "{{webUIName}} 백엔드가 필요합니다.", "*Prompt node ID(s) are required for image generation": "이미지 생성에는 프롬프트 노드 ID가 필요합니다.", "A new version (v{{LATEST_VERSION}}) is now available.": "새로운 버전 (v{{LATEST_VERSION}})을 사용할 수 있습니다.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "작업 모델은 채팅 및 웹 검색 쿼리에 대한 제목 생성 등의 작업 수행 시 사용됩니다.", "a user": "사용자", "About": "정보", "Accept autocomplete generation / Jump to prompt variable": "자동 완성 생성 수락 / 프롬프트 변수로 이동", "Access": "접근", "Access Control": "접근 제어", "Accessible to all users": "모든 사용자가 이용할 수 있음", "Account": "계정", "Account Activation Pending": "계정 활성화 대기", "Accurate information": "정확한 정보", "Actions": "작업", "Activate": "활성화", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "채팅 입력창에 \"{{COMMAND}}\"을 입력해 명령을 실행하세요.", "Active Users": "활성화된 사용자", "Add": "추가", "Add a model ID": "모델 ID 추가", "Add a short description about what this model does": "모델의 기능에 대한 간단한 설명 추가", "Add a tag": "태그 추가", "Add Arena Model": "아레나 모델 추가", "Add Connection": "연결 추가", "Add Content": "내용 추가", "Add content here": "여기에 내용을 추가하세요", "Add Custom Parameter": "", "Add custom prompt": "사용자 정의 프롬프트 추가", "Add Files": "파일 추가", "Add Group": "그룹 추가", "Add Memory": "메모리 추가", "Add Model": "모델 추가", "Add Reaction": "리액션 추가", "Add Tag": "태그 추가", "Add Tags": "태그 추가", "Add text content": "글 추가", "Add User": "사용자 추가", "Add User Group": "사용자 그룹 추가", "Adjusting these settings will apply changes universally to all users.": "이 설정을 조정하면 모든 사용자에게 변경 사항이 일괄 적용됩니다.", "admin": "관리자", "Admin": "관리자", "Admin Panel": "관리자 패널", "Admin Settings": "관리자 설정", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "관리자는 항상 모든 도구에 접근할 수 있지만, 사용자는 워크스페이스에서 모델마다 도구를 할당받아야 합니다.", "Advanced Parameters": "고급 매개변수", "Advanced Params": "고급 매개변수", "All": "전체", "All Documents": "모든 문서", "All models deleted successfully": "성공적으로 모든 모델이 삭제되었습니다", "Allow Call": "음성 통화 허용", "Allow Chat Controls": "채팅 제어 허용", "Allow Chat Delete": "채팅 삭제 허용", "Allow Chat Deletion": "채팅 삭제 허용", "Allow Chat Edit": "채팅 수정 허용", "Allow Chat Export": "채팅 내보내기 허용", "Allow Chat Share": "채팅 공유 허용", "Allow Chat System Prompt": "", "Allow File Upload": "파일 업로드 허용", "Allow Multiple Models in Chat": "채팅에서 여러 모델 허용", "Allow non-local voices": "외부 음성 허용", "Allow Speech to Text": "음성 텍스트 변환 허용", "Allow Temporary Chat": "임시 채팅 허용", "Allow Text to Speech": "텍스트 음성 변환 허용", "Allow User Location": "사용자 위치 활용 허용", "Allow Voice Interruption in Call": "음성 기능에서 음성 방해 허용", "Allowed Endpoints": "허용 엔드포인트", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "이미 계정이 있으신가요?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "top_p의 대안으로, 품질과 다양성 간의 균형을 보장하는 것을 목표로 합니다. 매개변수 p는 가장 가능성이 높은 토큰의 확률 대비 고려될 토큰의 최소 확률을 나타냅니다. 예를 들어, p=0.05이고 가장 가능성이 높은 토큰의 확률이 0.9인 경우, 값이 0.045보다 작은 로짓은 필터링됩니다.", "Always": "항상", "Always Collapse Code Blocks": "항상 코드 블록 접기", "Always Expand Details": "항상 세부 정보 펼치기", "Always Play Notification Sound": "항상 알림 소리 재생", "Amazing": "놀라움", "an assistant": "어시스턴트", "Analyzed": "분석됨", "Analyzing...": "분석 중...", "and": "그리고", "and {{COUNT}} more": "그리고 {{COUNT}} 더", "and create a new shared link.": "새로운 공유 링크를 생성합니다.", "Android": "안드로이드", "API": "", "API Base URL": "API 기본 URL", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "API 키", "API Key created.": "API 키가 생성되었습니다.", "API Key Endpoint Restrictions": "API 키 엔드포인트 제한", "API keys": "API 키", "API Version": "", "Application DN": "Application DN", "Application DN Password": "Application DN 비밀번호", "applies to all users with the \"user\" role": "\"사용자\" 권한의 모든 사용자에게 적용됩니다", "April": "4월", "Archive": "보관", "Archive All Chats": "모든 채팅 보관", "Archived Chats": "보관된 채팅", "archived-chat-export": "보관된 채팅 내보내기", "Are you sure you want to clear all memories? This action cannot be undone.": "정말 모든 메모리를 지우시겠습니까? 이 작업은 되돌릴 수 없습니다.", "Are you sure you want to delete this channel?": "정말 이 채널을 삭제하시겠습니까?", "Are you sure you want to delete this message?": "정말 이 메세지를 삭제하시겠습니까?", "Are you sure you want to unarchive all archived chats?": "정말 보관된 모든 채팅을 보관 해제하시겠습니까?", "Are you sure?": "확실합니까?", "Arena Models": "Arena 모델", "Artifacts": "아티팩트", "Ask": "질문", "Ask a question": "질문하기", "Assistant": "어시스턴트", "Attach file from knowledge": "지식 베이스에서 파일 첨부", "Attention to detail": "세부 사항에 대한 주의", "Attribute for Mail": "메일 속성", "Attribute for Username": "사용자 이름 속성", "Audio": "오디오", "August": "8월", "Auth": "인증", "Authenticate": "인증하다", "Authentication": "인증", "Auto": "자동", "Auto-Copy Response to Clipboard": "응답을 클립보드에 자동 복사", "Auto-playback response": "응답 자동 재생", "Autocomplete Generation": "자동완성 생성", "Autocomplete Generation Input Max Length": "자동완성 생성 입력 최대 길이", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "Automatic1111 API 인증 문자", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 기본 URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 기본 URL 설정이 필요합니다.", "Available list": "가능한 목록", "Available Tools": "사용 가능한 도구", "available!": "사용 가능!", "Awful": "형편없음", "Azure AI Speech": "Azure AI 음성", "Azure Region": "Azure 지역", "Back": "뒤로", "Bad Response": "잘못된 응답", "Banners": "배너", "Base Model (From)": "기본 모델(시작)", "before": "이전", "Being lazy": "게으름 피우기", "Beta": "베타", "Bing Search V7 Endpoint": "Bing Search V7 엔드포인트", "Bing Search V7 Subscription Key": "Bing Search V7 구독 키", "Bocha Search API Key": "Bocha Search API 키", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "특정 토큰을 가중 상향/하향하여 응답을 제약합니다. 값은 -100 ~ 100(기본값: 없음)", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "Docling OCR 엔진과 언어(s) 모두 제공 또는 모두 비워두세요.", "Brave Search API Key": "Brave Search API 키", "By {{name}}": "작성자: {{name}}", "Bypass Embedding and Retrieval": "임베딩 검색 우회", "Bypass Web Loader": "", "Calendar": "캘린더", "Call": "음성 기능", "Call feature is not supported when using Web STT engine": "웹 STT 엔진 사용 시, 음성 기능은 지원되지 않습니다.", "Camera": "카메라", "Cancel": "취소", "Capabilities": "기능", "Capture": "캡처", "Capture Audio": "오디오 캡처", "Certificate Path": "인증서 경로", "Change Password": "비밀번호 변경", "Channel Name": "채널 이름", "Channels": "채널", "Character": "캐릭터", "Character limit for autocomplete generation input": "자동 완성 생성 입력 문자 제한", "Chart new frontiers": "새로운 영역 개척", "Chat": "채팅", "Chat Background Image": "채팅 배경 이미지", "Chat Bubble UI": "버블형 채팅 UI", "Chat Controls": "채팅 제어", "Chat direction": "채팅 방향", "Chat Overview": "채팅", "Chat Permissions": "채팅 권한", "Chat Tags Auto-Generation": "채팅 태그 자동생성", "Chats": "채팅", "Check Again": "다시 확인", "Check for updates": "업데이트 확인", "Checking for updates...": "업데이트 확인중...", "Choose a model before saving...": "저장하기 전에 모델을 선택하세요...", "Chunk Overlap": "Chunk 오버랩", "Chunk Size": "Chunk 크기", "Ciphers": "암호", "Citation": "인용", "Citations": "", "Clear memory": "메모리 초기화", "Clear Memory": "메모리 지우기", "click here": "여기를 클릭하세요", "Click here for filter guides.": "필터 가이드를 보려면 여기를 클릭하세요.", "Click here for help.": "도움말을 보려면 여기를 클릭하세요.", "Click here to": "여기를 클릭하면", "Click here to download user import template file.": "사용자 삽입 템플렛 파일을 다운받으려면 여기를 클릭하세요", "Click here to learn more about faster-whisper and see the available models.": "빠른 속삭임에 대해 배우거나 가능한 모델을 보려면 여기를 클릭하세요", "Click here to see available models.": "사용 가능한 모델을 보려면 여기를 클릭하세요.", "Click here to select": "선택하려면 여기를 클릭하세요.", "Click here to select a csv file.": "csv 파일을 선택하려면 여기를 클릭하세요.", "Click here to select a py file.": "py 파일을 선택하려면 여기를 클릭하세요.", "Click here to upload a workflow.json file.": "workflow.json 파일을 업로드하려면 여기를 클릭하세요", "click here.": "여기를 클릭하세요.", "Click on the user role button to change a user's role.": "사용자 역할 버튼을 클릭하여 사용자의 역할을 변경하세요.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "클립보드 쓰기 권한이 거부되었습니다. 브라우저 설정에서 권한을 허용해주세요.", "Clone": "복제", "Clone Chat": "채팅 복제", "Clone of {{TITLE}}": "{{TITLE}}의 복제본", "Close": "닫기", "Close modal": "", "Close settings modal": "", "Code execution": "코드 실행", "Code Execution": "코드 실행", "Code Execution Engine": "코드 실행 엔진", "Code Execution Timeout": "코드 실행 시간 초과", "Code formatted successfully": "성공적으로 코드가 생성되었습니다", "Code Interpreter": "코드 인터프리터", "Code Interpreter Engine": "코드 인터프리터 엔진", "Code Interpreter Prompt Template": "코드 인터프리터 프롬프트 템플릿", "Collapse": "접기", "Collection": "컬렉션", "Color": "색상", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI API 키", "ComfyUI Base URL": "ComfyUI 기본 URL", "ComfyUI Base URL is required.": "ComfyUI 기본 URL이 필요합니다.", "ComfyUI Workflow": "ComfyUI 워크플로", "ComfyUI Workflow Nodes": "ComfyUI 워크플로 노드", "Command": "명령", "Completions": "완성됨", "Concurrent Requests": "동시 요청 수", "Configure": "구성", "Confirm": "확인", "Confirm Password": "비밀번호 확인", "Confirm your action": "액션 확인", "Confirm your new password": "새로운 비밀번호를 한 번 더 입력해 주세요", "Connect to your own OpenAI compatible API endpoints.": "OpenAI 호환 API 엔드포인트에 연결합니다.", "Connect to your own OpenAPI compatible external tool servers.": "OpenAPI 호환 외부 도구 서버에 연결합니다.", "Connection failed": "연결 실패", "Connection successful": "연결 성공", "Connection Type": "", "Connections": "연결", "Connections saved successfully": "연결이 성공적으로 저장되었습니다", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "추론 모델의 추론 난이도를 제한합니다. 추론 난이도를 지원하는 특정 공급자의 추론 모델에만 적용됩니다.", "Contact Admin for WebUI Access": "WebUI 접속을 위해서는 관리자에게 연락에 연락하십시오", "Content": "내용", "Content Extraction Engine": "콘텐츠 추출 엔진", "Continue Response": "응답 이어 받기", "Continue with {{provider}}": "{{provider}}로 계속", "Continue with Email": "이메일로 계속", "Continue with LDAP": "LDAP로 계속", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "TTS 요청에 메시지가 어떻게 나뉘어지는지 제어하십시오. '문장 부호'는 문장으로 나뉘고, '문단'은 문단으로 나뉘고, '없음'은 메세지를 하나의 문자열로 인식합니다.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "생성된 텍스트에서 토큰 시퀀스의 반복을 제어합니다. 높은 값(예: 1.5)은 반복에 더 강한 페널티를 부과하고, 낮은 값(예: 1.1)은 더 관대합니다. 1일 경우 비활성화됩니다.", "Controls": "제어", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "출력의 일관성과 다양성 간의 균형을 제어합니다. 낮은 값은 더 집중되고 일관성 있는 텍스트를 생성합니다.", "Copied": "복사됨", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "채팅 공유 URL이 클립보드에 복사되었습니다!", "Copied to clipboard": "클립보드에 복사되었습니다", "Copy": "복사", "Copy Formatted Text": "서식 있는 텍스트 복사", "Copy last code block": "마지막 코드 블록 복사", "Copy last response": "마지막 응답 복사", "Copy Link": "링크 복사", "Copy to clipboard": "클립보드에 복사", "Copying to clipboard was successful!": "성공적으로 클립보드에 복사되었습니다!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "Open WebUI의 요청을 허용하려면 공급자가 CORS를 올바르게 구성해야 합니다.", "Create": "생성", "Create a knowledge base": "지식 기반 생성", "Create a model": "모델 생성", "Create Account": "계정 생성", "Create Admin Account": "관리자 계정 생성", "Create Channel": "채널 생성", "Create Group": "그룹 생성", "Create Knowledge": "지식 생성", "Create new key": "새로운 키 생성", "Create new secret key": "새로운 비밀 키 생성", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "생성일", "Created At": "생성일", "Created by": "생성자", "CSV Import": "CSV 가져오기", "Ctrl+Enter to Send": "Ctrl+Enter로 보내기", "Current Model": "현재 모델", "Current Password": "현재 비밀번호", "Custom": "사용자 정의", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "위험 기능", "Dark": "다크", "Database": "데이터베이스", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "December": "12월", "Default": "기본값", "Default (Open AI)": "기본값 (Open AI)", "Default (SentenceTransformers)": "기본값 (SentenceTransformers)", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "기본 모드는 실행 전에 도구를 한 번 호출하여 더 다양한 모델에서 작동합니다. 기본 모드는 모델에 내장된 도구 호출 기능을 활용하지만, 모델이 이 기능을 본질적으로 지원해야 합니다.", "Default Model": "기본 모델", "Default model updated": "기본 모델이 업데이트되었습니다.", "Default Models": "기본 모델", "Default permissions": "기본 권한", "Default permissions updated successfully": "성공적으로 기본 권한이 수정되었습니다", "Default Prompt Suggestions": "기본 프롬프트 제안", "Default to 389 or 636 if TLS is enabled": "TLS가 활성화된 경우 기본값은 389 또는 636입니다", "Default to ALL": "기본값: 전체", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "집중적이고 관련성 있는 콘텐츠 추출을 위해 세분화된 검색을 기본으로 하며, 대부분의 경우에 권장됩니다.", "Default User Role": "기본 사용자 역할", "Delete": "삭제", "Delete a model": "모델 삭제", "Delete All Chats": "모든 채팅 삭제", "Delete All Models": "모든 모델 삭제", "Delete chat": "채팅 삭제", "Delete Chat": "채팅 삭제", "Delete chat?": "채팅을 삭제하시겠습니까?", "Delete folder?": "폴더를 삭제하시겠습니까?", "Delete function?": "함수를 삭제하시겠습니까?", "Delete Message": "메시지 삭제", "Delete message?": "메시지를 삭제하시겠습니까?", "Delete note?": "노트를 삭제하시겠습니까?", "Delete prompt?": "프롬프트를 삭제하시겠습니까?", "delete this link": "이 링크를 삭제합니다.", "Delete tool?": "도구를 삭제하시겠습니까?", "Delete User": "사용자 삭제", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} 삭제됨", "Deleted {{name}}": "{{name}}을(를) 삭제했습니다.", "Deleted User": "삭제된 사용자", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "지식 기반에 대한 설명과 목적을 입력하세요", "Description": "설명", "Detect Artifacts Automatically": "아티팩트 자동 감지", "Dictate": "마이크 사용", "Didn't fully follow instructions": "완전히 지침을 따르지 않음", "Direct": "", "Direct Connections": "직접 연결", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "직접 연결을 통해 사용자는 자체 OpenAI 호환 API 엔드포인트에 연결할 수 있습니다.", "Direct Connections settings updated": "직접 연결 설정이 업데이트되었습니다", "Direct Tool Servers": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "제한됨", "Discover a function": "함수 검색", "Discover a model": "모델 검색", "Discover a prompt": "프롬프트 검색", "Discover a tool": "도구 검색", "Discover how to use Open WebUI and seek support from the community.": "Open WebUI 사용 방법을 알아보고 커뮤니티에서 지원을 받으세요.", "Discover wonders": "놀라움을 체험하세요", "Discover, download, and explore custom functions": "사용자 정의 함수 검색, 다운로드 및 탐색", "Discover, download, and explore custom prompts": "사용자 정의 프롬프트 검색, 다운로드 및 탐색", "Discover, download, and explore custom tools": "사용자 정의 도구 검색, 다운로드 및 탐색", "Discover, download, and explore model presets": "모델 사전 설정 검색, 다운로드 및 탐색", "Dismissible": "제외가능", "Display": "표시", "Display Emoji in Call": "음성기능에서 이모지 표시", "Display the username instead of You in the Chat": "채팅에서 '당신' 대신 사용자 이름 표시", "Displays citations in the response": "응답에 인용 표시", "Dive into knowledge": "지식 탐구", "Do not install functions from sources you do not fully trust.": "불분명한 출처를 가진 함수를 설치하지마세요", "Do not install tools from sources you do not fully trust.": "불분명한 출처를 가진 도구를 설치하지마세요", "Docling": "", "Docling Server URL required.": "Docling 서버 URL이 필요합니다.", "Document": "문서", "Document Intelligence": "", "Document Intelligence endpoint and key required.": "Document Intelligence 엔드포인트 및 키가 필요합니다.", "Documentation": "문서", "Documents": "문서", "does not make any external connections, and your data stays securely on your locally hosted server.": "외부와 어떠한 연결도 하지 않으며, 데이터는 로컬에서 호스팅되는 서버에 안전하게 유지됩니다.", "Domain Filter List": "도메인 필터 목록", "Don't have an account?": "계정이 없으신가요?", "don't install random functions from sources you don't trust.": "불분명한 출처를 가진 임의의 함수를 설치하지마세요", "don't install random tools from sources you don't trust.": "불분명한 출처를 가진 임의의 도구를 설치하지마세요", "Don't like the style": "스타일이 마음에 안 드시나요?", "Done": "완료됨", "Download": "다운로드", "Download as SVG": "SVG로 다운로드", "Download canceled": "다운로드 취소", "Download Database": "데이터베이스 다운로드", "Drag and drop a file to upload or select a file to view": "파일을 끌어다 놓아 업로드하거나 파일을 선택하여 보기", "Draw": "그리기", "Drop any files here to upload": "여기에 파일을 끌어다 놓아 업로드하세요", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "예: '30초','10분'. 유효한 시간 단위는 '초', '분', '시'입니다.", "e.g. \"json\" or a JSON schema": "예: \\\"json\\\" 또는 JSON 스키마", "e.g. 60": "예: 60", "e.g. A filter to remove profanity from text": "예: 텍스트에서 비속어를 제거하는 필터", "e.g. en": "", "e.g. My Filter": "예: 내 필터", "e.g. My Tools": "예: 내 도구", "e.g. my_filter": "예: my_filter", "e.g. my_tools": "예: my_tools", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "예: 다양한 작업을 수행하는 도구", "e.g., 3, 4, 5 (leave blank for default)": "예: 3, 4, 5 (기본값을 위해 비워 두세요)", "e.g., audio/wav,audio/mpeg (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "예: en-<PERSON>, ja-<PERSON> (자동 감지를 위해 비워 두세요)", "e.g., westus (leave blank for eastus)": "예: west<PERSON> (eastus를 위해 비워 두세요)", "e.g.) en,fr,de": "", "Edit": "편집", "Edit Arena Model": "아레나 모델 편집", "Edit Channel": "채널 편집", "Edit Connection": "연결 편집", "Edit Default Permissions": "기본 권한 편집", "Edit Memory": "메모리 편집", "Edit User": "사용자 편집", "Edit User Group": "사용자 그룹 편집", "Eject": "", "ElevenLabs": "ElevenLabs", "Email": "이메일", "Embark on adventures": "모험을 떠나기", "Embedding": "임베딩", "Embedding Batch Size": "임베딩 배치 크기", "Embedding Model": "임베딩 모델", "Embedding Model Engine": "임베딩 모델 엔진", "Embedding model set to \"{{embedding_model}}\"": "임베딩 모델을 \"{{embedding_model}}\"로 설정함", "Enable API Key": "API 키 활성화", "Enable autocomplete generation for chat messages": "채팅 메시지에 대한 자동 완성 생성 활성화", "Enable Code Execution": "코드 실행 활성화", "Enable Code Interpreter": "코드 인터프리터 활성화", "Enable Community Sharing": "커뮤니티 공유 활성화", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "모델 데이터가 RAM에서 스왑 아웃되는 것을 방지하기 위해 메모리 잠금(mlock)을 활성화합니다. 이 옵션은 모델의 작업 페이지 집합을 RAM에 잠가 디스크로 스왑 아웃되지 않도록 보장합니다. 이는 페이지 폴트를 피하고 빠른 데이터 액세스를 보장하여 성능을 유지하는 데 도움이 될 수 있습니다.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "모델 데이터를 로드하기 위해 메모리 매핑(mmap)을 활성화합니다. 이 옵션을 사용하면 시스템이 디스크 파일을 RAM에 있는 것처럼 처리하여 디스크 스토리지를 RAM의 확장으로 사용할 수 있습니다. 이는 더 빠른 데이터 액세스를 허용하여 모델 성능을 향상시킬 수 있습니다. 그러나 모든 시스템에서 올바르게 작동하지 않을 수 있으며 상당한 양의 디스크 공간을 소비할 수 있습니다.", "Enable Message Rating": "메시지 평가 활성화", "Enable Mirostat sampling for controlling perplexity.": "퍼플렉서티 제어를 위해 Mirostat 샘플링 활성화", "Enable New Sign Ups": "새 회원가입 활성화", "Enabled": "활성화됨", "Endpoint URL": "엔드포인트 URL", "Enforce Temporary Chat": "임시 채팅 강제 적용", "Enhance": "향상", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "CSV 파일에 이름, 이메일, 비밀번호, 역할 4개의 열이 순서대로 포함되어 있는지 확인하세요.", "Enter {{role}} message here": "여기에 {{role}} 메시지 입력", "Enter a detail about yourself for your LLMs to recall": "자신에 대한 세부사항을 입력하여 LLM들이 기억할 수 있도록 하세요.", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "API 인증 문자 입력 (예: 사용자 이름:비밀번호)", "Enter Application DN": "애플리케이션 DN 입력", "Enter Application DN Password": "애플리케이션 DN 비밀번호 입력", "Enter Bing Search V7 Endpoint": "Bing Search V7 엔드포인트 입력", "Enter Bing Search V7 Subscription Key": "Bing Search V7 구독 키 입력", "Enter BM25 Weight": "", "Enter Bocha Search API Key": "Bocha 검색 API 키 입력", "Enter Brave Search API Key": "Brave Search API Key 입력", "Enter certificate path": "인증서 경로 입력", "Enter CFG Scale (e.g. 7.0)": "CFG Scale 입력 (예: 7.0)", "Enter Chunk Overlap": "청크 오버랩 입력", "Enter Chunk Size": "청크 크기 입력", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "쉼표로 구분된 \\\"토큰:편향_값\\\" 쌍 입력 (예: 5432:100, 413:-100)", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter Datalab Marker API Key": "", "Enter description": "설명 입력", "Enter Docling OCR Engine": "Docling OCR 엔진 입력", "Enter Docling OCR Language(s)": "Docling OCR 언어(s) 입력", "Enter Docling Server URL": "Docling 서버 URL 입력", "Enter Document Intelligence Endpoint": "Document Intelligence 엔드포인트 입력", "Enter Document Intelligence Key": "Document Intelligence 키 입력", "Enter domains separated by commas (e.g., example.com,site.org)": "쉼표로 구분된 도메인 입력 (예: example.com, site.org)", "Enter Exa API Key": "Exa API 키 입력", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "외부 웹 로더 API 키 입력", "Enter External Web Loader URL": "외부 웹 로더 URL 입력", "Enter External Web Search API Key": "외부 웹 검색 API 키 입력", "Enter External Web Search URL": "외부 웹 검색 URL 입력", "Enter Firecrawl API Base URL": "Firecrawl API 기본 URL 입력", "Enter Firecrawl API Key": "Firecrawl API 키 입력", "Enter Github Raw URL": "Github Raw URL 입력", "Enter Google PSE API Key": "Google PSE API 키 입력", "Enter Google PSE Engine Id": "Google PSE 엔진 ID 입력", "Enter Image Size (e.g. 512x512)": "이미지 크기 입력(예: 512x512)", "Enter Jina API Key": "Jina API 키 입력", "Enter Jupyter Password": "<PERSON><PERSON><PERSON> 비밀번호 입력", "Enter Jupyter Token": "<PERSON><PERSON><PERSON> 토큰 입력", "Enter Jupyter URL": "Jupyter URL 입력", "Enter Kagi Search API Key": "Kagi Search API 키 입력", "Enter Key Behavior": "키 동작 입력", "Enter language codes": "언어 코드 입력", "Enter Mistral API Key": "Mistral API 키 입력", "Enter Model ID": "모델 ID 입력", "Enter model tag (e.g. {{modelTag}})": "모델 태그 입력(예: {{modelTag}})", "Enter Mojeek Search API Key": "Mojeek Search API 키 입력", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "단계 수 입력(예: 50)", "Enter Perplexity API Key": "Perplexity API 키 입력", "Enter Playwright Timeout": "Playwright 시간 초과 입력", "Enter Playwright WebSocket URL": "Playwright WebSocket URL 입력", "Enter proxy URL (e.g. **************************:port)": "프록시 URL 입력(예: **************************:port)", "Enter reasoning effort": "추론 난이도", "Enter Sampler (e.g. Euler a)": "샘플러 입력 (예: 오일러 a(<PERSON><PERSON><PERSON> a))", "Enter Scheduler (e.g. Karras)": "스케쥴러 입력 (예: 카라스(Ka<PERSON><PERSON>))", "Enter Score": "점수 입력", "Enter SearchApi API Key": "SearchApi API 키 입력", "Enter SearchApi Engine": "SearchApi 엔진 입력", "Enter Searxng Query URL": "Searxng 쿼리 URL 입력", "Enter Seed": "Seed 입력", "Enter SerpApi API Key": "SerpApi API 키 입력", "Enter SerpApi Engine": "SerpApi 엔진 입력", "Enter Serper API Key": "Serper API 키 입력", "Enter Serply API Key": "Serply API 키 입력", "Enter Serpstack API Key": "Serpstack API 키 입력", "Enter server host": "서버 호스트 입력", "Enter server label": "서버 레이블 입력", "Enter server port": "서버 포트 입력", "Enter Sougou Search API sID": "Sougou 검색 API sID 입력", "Enter Sougou Search API SK": "Sougou 검색 API SK 입력", "Enter stop sequence": "중지 시퀀스 입력", "Enter system prompt": "시스템 프롬프트 입력", "Enter system prompt here": "여기에 시스템 프롬프트 입력", "Enter Tavily API Key": "Tavily API 키 입력", "Enter Tavily Extract Depth": "Tavily 추출 깊이 입력", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "WebUI의 공개 URL을 입력해 주세요. 이 URL은 알림에서 링크를 생성하는 데 사용합니다.", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "Tika 서버 URL 입력", "Enter timeout in seconds": "시간 초과(초) 입력", "Enter to Send": "Enter로 보내기", "Enter Top K": "Top K 입력", "Enter Top K Reranker": "Top K 리랭커 입력", "Enter URL (e.g. http://127.0.0.1:7860/)": "URL 입력(예: http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "URL 입력(예: http://localhost:11434)", "Enter Yacy Password": "<PERSON><PERSON> 비밀번호 입력", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "Yacy URL 입력(예: http://yacy.example.com:8090)", "Enter Yacy Username": "Ya<PERSON> 사용자 이름 입력", "Enter your current password": "현재 비밀번호를 입력해 주세요", "Enter Your Email": "이메일 입력", "Enter Your Full Name": "전체 이름 입력", "Enter your message": "메세지 입력", "Enter your name": "이름 입력", "Enter Your Name": "이름 입력", "Enter your new password": "새로운 비밀번호를 입력해 주세요", "Enter Your Password": "비밀번호 입력", "Enter Your Role": "역할 입력", "Enter Your Username": "사용자 이름 입력", "Enter your webhook URL": "웹훅 URL을 입력해 주세요", "Error": "오류", "ERROR": "오류", "Error accessing Google Drive: {{error}}": "Google Drive 액세스 오류: {{error}}", "Error accessing media devices.": "미디어 장치 액세스 오류", "Error starting recording.": "녹화 시작 오류", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "파일 업로드 오류: {{error}}", "Evaluations": "평가", "Exa API Key": "Exa API 키", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "예: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "예: 전체", "Example: mail": "예: 메일", "Example: ou=users,dc=foo,dc=example": "예: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "예: sAMAccountName or uid or userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "라이선스의 좌석 수를 초과했습니다. 좌석 수를 늘리려면 지원팀에 문의해 주세요.", "Exclude": "미포함", "Execute code for analysis": "분석을 위한 코드 실행", "Executing **{{NAME}}**...": "**{{NAME}}** 실행 중...", "Expand": "확장", "Experimental": "실험적", "Explain": "설명", "Explore the cosmos": "우주 탐험", "Export": "내보내기", "Export All Archived Chats": "모든 보관된 채팅 내보내기", "Export All Chats (All Users)": "모든 채팅 내보내기(모든 사용자)", "Export chat (.json)": "채팅 내보내기 (.json)", "Export Chats": "채팅 내보내기", "Export Config to JSON File": "Config를 JSON 파일로 내보내기", "Export Functions": "함수 내보내기", "Export Models": "모델 내보내기", "Export Presets": "프리셋 내보내기", "Export Prompt Suggestions": "", "Export Prompts": "프롬프트 내보내기", "Export to CSV": "CSV로 내보내기", "Export Tools": "도구 내보내기", "External": "외부", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "외부 웹 로더 API 키", "External Web Loader URL": "외부 웹 로더 URL", "External Web Search API Key": "외부 웹 검색 API 키", "External Web Search URL": "외부 웹 검색 URL", "Failed to add file.": "파일추가에 실패했습니다", "Failed to connect to {{URL}} OpenAPI tool server": "{{URL}} OpenAPI 도구 서버 연결 실패", "Failed to copy link": "", "Failed to create API Key.": "API 키 생성에 실패했습니다.", "Failed to delete note": "노트 삭제 실패", "Failed to fetch models": "모델 가져오기 실패", "Failed to load file content.": "파일 내용 로드 실패.", "Failed to read clipboard contents": "클립보드 내용 가져오기를 실패하였습니다.", "Failed to save connections": "연결 저장 실패", "Failed to save models configuration": "모델 구성 저장 실패", "Failed to update settings": "설정 업데이트에 실패하였습니다.", "Failed to upload file.": "파일 업로드에 실패했습니다", "Features": "기능", "Features Permissions": "기능 권한", "February": "2월", "Feedback Details": "", "Feedback History": "피드백 기록", "Feedbacks": "피드백", "Feel free to add specific details": "자세한 내용을 자유롭게 추가하세요.", "File": "파일", "File added successfully.": "파일이 성공적으로 추가되었습니다", "File content updated successfully.": "내용이 성공적으로 업데이트되었습니다", "File Mode": "파일 모드", "File not found.": "파일을 찾을 수 없습니다.", "File removed successfully.": "파일이 성공적으로 삭제되었습니다", "File size should not exceed {{maxSize}} MB.": "파일 사이즈가 {{maxSize}} MB를 초과하면 안됩니다.", "File Upload": "", "File uploaded successfully": "파일이 성공적으로 업로드되었습니다", "Files": "파일", "Filter is now globally disabled": "전반적으로 필터 비활성화됨", "Filter is now globally enabled": "전반적으로 필터 활성화됨", "Filters": "필터", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Fingerprint spoofing 감지: 이니셜을 아바타로 사용할 수 없습니다. 기본 프로필 이미지로 설정합니다.", "Firecrawl API Base URL": "Firecrawl API 기본 URL", "Firecrawl API Key": "Firecrawl API 키", "Fluidly stream large external response chunks": "대규모 외부 응답 청크를 유연하게 스트리밍", "Focus chat input": "채팅 입력창에 포커스", "Folder deleted successfully": "성공적으로 폴터가 생성되었습니다", "Folder name cannot be empty.": "폴더 이름을 작성해주세요", "Folder name updated successfully": "성공적으로 폴더 이름이 저장되었습니다", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "명령을 완벽히 수행함", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "새로운 경로 만들기", "Form": "폼", "Format your variables using brackets like this:": "변수를 다음과 같이 괄호를 사용하여 생성하세요", "Forwards system user session credentials to authenticate": "인증을 위해 시스템 사용자 세션 자격 증명 전달", "Full Context Mode": "전체 컨텍스트 모드", "Function": "함수", "Function Calling": "함수 호출", "Function created successfully": "성공적으로 함수가 생성되었습니다", "Function deleted successfully": "성공적으로 함수가 삭제되었습니다", "Function Description": "함수 설명", "Function ID": "함수 ID", "Function imported successfully": "", "Function is now globally disabled": "전반적으로 함수 비활성화됨", "Function is now globally enabled": "전반적으로 함수 활성화됨", "Function Name": "함수 이름", "Function updated successfully": "성공적으로 함수가 업데이트되었습니다", "Functions": "함수", "Functions allow arbitrary code execution.": "함수가 임의의 코드를 실행하도록 허용하였습니다", "Functions imported successfully": "성공적으로 함수를 가져왔습니다", "Gemini": "Gemini", "Gemini API Config": "Gemini API 구성", "Gemini API Key is required.": "Gemini API 키가 필요합니다.", "General": "일반", "Generate": "생성", "Generate an image": "이미지 생성", "Generate Image": "이미지 생성", "Generate prompt pair": "프롬프트 쌍 생성", "Generating search query": "검색 쿼리 생성", "Generating...": "", "Get started": "시작하기", "Get started with {{WEBUI_NAME}}": "{{WEBUI_NAME}} 시작하기", "Global": "글로벌", "Good Response": "좋은 응답", "Google Drive": "구글 드라이브", "Google PSE API Key": "Google PSE API 키", "Google PSE Engine Id": "Google PSE 엔진 ID", "Group created successfully": "성공적으로 그룹을 생성했습니다", "Group deleted successfully": "성공적으로 그룹을 삭제했습니다", "Group Description": "그룹 설명", "Group Name": "그룹 명", "Group updated successfully": "성공적으로 그룹을 수정했습니다", "Groups": "그룹", "Haptic Feedback": "햅틱 피드백", "Hello, {{name}}": "안녕하세요, {{name}}", "Help": "도움말", "Help us create the best community leaderboard by sharing your feedback history!": "당신의 피드백 기록을 공유함으로서 최고의 커뮤니티 리더보드를 만드는데 도와주세요!", "Hex Color": "Hex 색상", "Hex Color - Leave empty for default color": "Hex 색상 - 기본 색상의 경우 빈 상태로 유지", "Hide": "숨기기", "Hide from Sidebar": "", "Hide Model": "모델 숨기기", "High Contrast Mode": "", "Home": "홈", "Host": "호스트", "How can I help you today?": "오늘 어떻게 도와드릴까요?", "How would you rate this response?": "이 응답을 어떻게 평가하시겠어요?", "HTML": "", "Hybrid Search": "하이브리드 검색", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "저는 제 행동의 의미를 읽고 이해했음을 인정합니다. 임의 코드 실행과 관련된 위험을 인지하고 있으며 출처의 신뢰성을 확인했습니다.", "ID": "ID", "iframe Sandbox Allow Forms": "iframe 샌드박스 허용 양식", "iframe Sandbox Allow Same Origin": "iframe 샌드박스에서 동일한 오리진 허용", "Ignite curiosity": "호기심 자극", "Image": "이미지", "Image Compression": "이미지 압축", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "이미지 생성", "Image Generation (Experimental)": "이미지 생성(실험적)", "Image Generation Engine": "이미지 생성 엔진", "Image Max Compression Size": "이미지 최대 압축 크기", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "이미지 프롬프트 생성", "Image Prompt Generation Prompt": "이미지 프롬프트를 생성하기 위한 프롬프트", "Image Settings": "이미지 설정", "Images": "이미지", "Import": "", "Import Chats": "채팅 불러오기", "Import Config from JSON File": "JSON 파일에서 설정 가져오기", "Import From Link": "", "Import Functions": "함수 가져오기", "Import Models": "모델 가져오기", "Import Notes": "노트 가져오기", "Import Presets": "프리셋 가져오기", "Import Prompt Suggestions": "", "Import Prompts": "프롬프트 가져오기", "Import Tools": "도구 가져오기", "Include": "포함", "Include `--api-auth` flag when running stable-diffusion-webui": "stable-diffusion-webui를 실행 시 `--api-auth` 플래그를 포함하세요", "Include `--api` flag when running stable-diffusion-webui": "stable-diffusion-webui를 실행 시 `--api` 플래그를 포함하세요", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "생성된 텍스트의 피드백에 알고리즘이 얼마나 빨리 반응하는지에 영향을 미칩니다. 학습률이 낮을수록 조정 속도가 느려지고 학습률이 높아지면 알고리즘의 반응 속도가 빨라집니다.", "Info": "정보", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "전체 콘텐츠를 포괄적인 처리를 위해 컨텍스트로 삽입하세요. 이는 복잡한 쿼리에 권장됩니다.", "Input commands": "명령어 입력", "Install from Github URL": "G<PERSON><PERSON> URL에서 설치", "Instant Auto-Send After Voice Transcription": "음성 변환 후 즉시 자동 전송", "Integration": "통합", "Interface": "인터페이스", "Invalid file content": "", "Invalid file format.": "잘못된 파일 형식", "Invalid JSON file": "", "Invalid Tag": "잘못된 태그", "is typing...": "입력 중...", "January": "1월", "Jina API Key": "Jina API 키", "join our Discord for help.": "도움말을 보려면 Discord에 가입하세요.", "JSON": "JSON", "JSON Preview": "JSON 미리 보기", "July": "7월", "June": "6월", "Jupyter Auth": "<PERSON><PERSON><PERSON> 인증", "Jupyter URL": "Jupyter URL", "JWT Expiration": "JWT 만료", "JWT Token": "JWT 토큰", "Kagi Search API Key": "Kagi Search API 키", "Keep in Sidebar": "", "Key": "키", "Keyboard shortcuts": "키보드 단축키", "Knowledge": "지식 기반", "Knowledge Access": "지식 접근", "Knowledge created successfully.": "성공적으로 지식 기반이 생성되었습니다", "Knowledge deleted successfully.": "성공적으로 지식 기반이 삭제되었습니다", "Knowledge Public Sharing": "지식 Public 공유", "Knowledge reset successfully.": "성공적으로 지식 기반이 초기화되었습니다", "Knowledge updated successfully": "성공적으로 지식 기반이 업데이트되었습니다", "Kokoro.js (Browser)": "Kokoro.js (Browser)", "Kokoro.js Dtype": "", "Label": "라벨", "Landing Page Mode": "랜딩페이지 모드", "Language": "언어", "Language Locales": "언어 로케일", "Languages": "", "Last Active": "최근 활동", "Last Modified": "마지막 수정", "Last reply": "마지막 답글", "LDAP": "", "LDAP server updated": "LDAP 서버가 업데이트되었습니다", "Leaderboard": "리더보드", "Learn more about OpenAPI tool servers.": "OpenAPI 도구 서버에 대해 자세히 알아보세요.", "Leave empty for no compression": "", "Leave empty for unlimited": "무제한을 위해 빈칸으로 남겨두세요", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "\\\"{{url}}/api/tags\\\" 엔드포인트의 모든 모델을 포함하려면 비워 두세요", "Leave empty to include all models from \"{{url}}/models\" endpoint": "\\\"{{url}}/models\\\" 엔드포인트의 모든 모델을 포함하려면 비워 두세요", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "기본 프롬프트를 사용하기 위해 빈칸으로 남겨두거나, 커스텀 프롬프트를 입력하세요", "Leave model field empty to use the default model.": "기본 모델을 사용하려면 모델 필드를 비워 두세요.", "License": "라이선스", "Light": "라이트", "Listening...": "듣는 중...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "LLM에 오류가 있을 수 있습니다. 중요한 정보는 확인이 필요합니다.", "Loader": "로더", "Loading Kokoro.js...": "Kokoro.js 로딩 중...", "Local": "로컬", "Local Task Model": "", "Location access not allowed": "위치 접근이 허용되지 않음", "Lost": "패배", "LTR": "LTR", "Made by Open WebUI Community": "OpenWebUI 커뮤니티에 의해 개발됨", "Make password visible in the user interface": "", "Make sure to enclose them with": "꼭 다음으로 감싸세요:", "Make sure to export a workflow.json file as API format from ComfyUI.": "꼭 workflow.json 파일을 ComfyUI의 API 형식대로 내보내세요", "Manage": "관리", "Manage Direct Connections": "다이렉트 연결 관리", "Manage Models": "모델 관리", "Manage Ollama": "Ollama 관리", "Manage Ollama API Connections": "Ollama API 연결 관리", "Manage OpenAI API Connections": "OpenAI API 연결 관리", "Manage Pipelines": "파이프라인 관리", "Manage Tool Servers": "도구 서버 관리", "March": "3월", "Markdown": "", "Max Speakers": "", "Max Upload Count": "업로드 최대 수", "Max Upload Size": "업로드 최대 사이즈", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "최대 3개의 모델을 동시에 다운로드할 수 있습니다. 나중에 다시 시도하세요.", "May": "5월", "Memories accessible by LLMs will be shown here.": "LLM에서 접근할 수 있는 메모리는 여기에 표시됩니다.", "Memory": "메모리", "Memory added successfully": "성공적으로 메모리가 추가되었습니다", "Memory cleared successfully": "성공적으로 메모리가 정리되었습니다", "Memory deleted successfully": "성공적으로 메모리가 삭제되었습니다", "Memory updated successfully": "성공적으로 메모리가 업데이트되었습니다", "Merge Responses": "응답들 결합하기", "Merged Response": "결합된 응답", "Message rating should be enabled to use this feature": "이 기능을 사용하려면 메시지 평가가 활성화되어야합니다", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "링크 생성 후에 보낸 메시지는 공유되지 않습니다. URL이 있는 사용자는 공유된 채팅을 볼 수 있습니다.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "Microsoft OneDrive (개인용)", "Microsoft OneDrive (work/school)": "Microsoft OneDrive (회사/학교용)", "Mistral OCR": "", "Mistral OCR API Key required.": "Mistral OCR API Key가 필요합니다.", "Model": "모델", "Model '{{modelName}}' has been successfully downloaded.": "모델 '{{modelName}}'이/가 성공적으로 다운로드되었습니다.", "Model '{{modelTag}}' is already in queue for downloading.": "모델 '{{modelTag}}'은/는 이미 다운로드 대기열에 있습니다.", "Model {{modelId}} not found": "모델 {{modelId}}을/를 찾을 수 없습니다.", "Model {{modelName}} is not vision capable": "모델 {{modelName}}은/는 비전을 사용할 수 없습니다.", "Model {{name}} is now {{status}}": "모델 {{name}}은/는 이제 {{status}} 상태입니다.", "Model {{name}} is now hidden": "모델 {{name}}은/는 이제 숨겨졌습니다.", "Model {{name}} is now visible": "모델 {{name}}은/는 이제 볼 수 있습니다.", "Model accepts file inputs": "", "Model accepts image inputs": "모델이 이미지 입력을 허용합니다", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "성공적으로 모델이 생성되었습니다", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "모델 파일 시스템 경로가 감지되었습니다. 업데이트하려면 모델 단축 이름이 필요하며 계속할 수 없습니다.", "Model Filtering": "모델 필터링", "Model ID": "모델 ID", "Model IDs": "모델 IDs", "Model Name": "모델 이름", "Model not selected": "모델이 선택되지 않았습니다.", "Model Params": "모델 파라미터", "Model Permissions": "모델 권한", "Model unloaded successfully": "", "Model updated successfully": "성공적으로 모델이 업데이트되었습니다", "Model(s) do not support file upload": "", "Modelfile Content": "모델 파일 내용", "Models": "모델", "Models Access": "모델 접근", "Models configuration saved successfully": "모델 구성이 성공적으로 저장되었습니다", "Models Public Sharing": "모델 공개 공유", "Mojeek Search API Key": "Mojeek Search API 키", "more": "더보기", "More": "더보기", "My Notes": "내 노트", "Name": "이름", "Name your knowledge base": "지식 기반 이름을 지정하세요", "Native": "네이티브", "New Chat": "새 채팅", "New Folder": "새 폴더", "New Function": "", "New Note": "새 노트", "New Password": "새 비밀번호", "New Tool": "", "new-channel": "새 채널", "Next message": "", "No chats found for this user.": "", "No chats found.": "", "No content": "내용 없음", "No content found": "내용을 찾을 수 없음", "No content found in file.": "파일에서 내용을 찾을 수 없습니다.", "No content to speak": "음성 출력할 내용을 찾을 수 없음", "No distance available": "거리 불가능", "No feedbacks found": "피드백 없음", "No file selected": "파일이 선택되지 않음", "No groups with access, add a group to grant access": "접근 권한이 있는 그룹이 없습니다. 접근 권한을 부여하려면 그룹을 추가하세요.", "No HTML, CSS, or JavaScript content found.": "HTML, CSS, JavaScript이 발견되지 않음", "No inference engine with management support found": "관리 지원이 포함된 추론 엔진을 찾을 수 없음", "No knowledge found": "지식 기반 없음", "No memories to clear": "지울 메모리 없음", "No model IDs": "모델 ID 없음", "No models found": "모델 없음", "No models selected": "모델 선택 안됨", "No Notes": "노트 없음", "No results found": "결과 없음", "No search query generated": "검색어가 생성되지 않았습니다.", "No source available": "사용 가능한 소스가 없습니다.", "No users were found.": "사용자를 찾을 수 없습니다.", "No valves to update": "업데이트 할 밸브가 없습니다.", "None": "없음", "Not factually correct": "사실상 맞지 않음", "Not helpful": "도움이 되지않음", "Note deleted successfully": "노트가 성공적으로 삭제되었습니다", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "참고: 최소 점수를 설정하면, 검색 결과로 최소 점수 이상의 점수를 가진 문서만 반환합니다.", "Notes": "노트", "Notification Sound": "알림 소리", "Notification Webhook": "알림 웹훅", "Notifications": "알림", "November": "11월", "OAuth ID": "OAuth ID", "October": "10월", "Off": "끄기", "Okay, Let's Go!": "좋아요, 시작합시다!", "OLED Dark": "OLED 다크", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "Ollama API 세팅이 업데이트 되었습니다.", "Ollama Version": "Ollama 버전", "On": "켜기", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "영문자, 숫자 및 하이픈(-)만 허용됩니다.", "Only alphanumeric characters and hyphens are allowed in the command string.": "명령어 문자열에는 영문자, 숫자 및 하이픈(-)만 허용됩니다.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "가지고 있는 컬렉션만 수정 가능합니다, 새 지식 기반을 생성하여 문서를 수정 혹은 추가하십시오.", "Only markdown files are allowed": "마크다운 파일만 허용됩니다", "Only select users and groups with permission can access": "권한이 있는 사용자와 그룹만 접근 가능합니다.", "Oops! Looks like the URL is invalid. Please double-check and try again.": "이런! URL이 잘못된 것 같습니다. 다시 한번 확인하고 다시 시도해주세요.", "Oops! There are files still uploading. Please wait for the upload to complete.": "이런! 파일이 계속 업로드중 입니다. 업로드가 완료될 때까지 잠시만 기다려주세요.", "Oops! There was an error in the previous response.": "이런! 이전 응답에 에러가 있었던 것 같습니다.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "이런! 지원되지 않는 방식(프론트엔드만)을 사용하고 계십니다. 백엔드에서 WebUI를 제공해주세요.", "Open file": "파일 열기", "Open in full screen": "전체화면으로 열기", "Open modal to configure connection": "", "Open new chat": "새 채팅 열기", "Open WebUI can use tools provided by any OpenAPI server.": "Open WebUI는 모든 OpenAPI 서버에서 제공하는 도구를 사용할 수 있습니다.", "Open WebUI uses faster-whisper internally.": "Open WebUI는 내부적으로 패스트 위스퍼를 사용합니다.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI는 SpeechT5와 CMU Arctic 스피커 임베딩을 사용합니다.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "열린 WebUI 버젼(v{{OPEN_WEBUI_VERSION}})은 최소 버젼 (v{{REQUIRED_VERSION}})보다 낮습니다", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API 설정", "OpenAI API Key is required.": "OpenAI API 키가 필요합니다.", "OpenAI API settings updated": "OpenAI API 설정이 업데이트되었습니다.", "OpenAI URL/Key required.": "OpenAI URL/키가 필요합니다.", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "또는", "Organize your users": "사용자를 ", "Other": "기타", "OUTPUT": "출력", "Output format": "출력 형식", "Output Format": "", "Overview": "개요", "page": "페이지", "Paginate": "", "Parameters": "", "Password": "비밀번호", "Paste Large Text as File": "큰 텍스트를 파일로 붙여넣기", "PDF document (.pdf)": "PDF 문서(.pdf)", "PDF Extract Images (OCR)": "PDF 이미지 추출(OCR)", "pending": "보류 중", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "미디어 장치 접근 권한이 거부되었습니다.", "Permission denied when accessing microphone": "마이크 접근 권한이 거부되었습니다.", "Permission denied when accessing microphone: {{error}}": "마이크 접근 권환이 거부되었습니다: {{error}}", "Permissions": "권한", "Perplexity API Key": "Perplexity API 키", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "개인화", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "고정", "Pinned": "고정됨", "Pioneer insights": "선구자 인사이트", "Pipeline deleted successfully": "성공적으로 파이프라인이 삭제되었습니다.", "Pipeline downloaded successfully": "성공적으로 파이프라인이 설치되었습니다.", "Pipelines": "파이프라인", "Pipelines Not Detected": "파이프라인이 발견되지 않았습니다.", "Pipelines Valves": "파이프라인 밸브", "Plain text (.md)": "", "Plain text (.txt)": "일반 텍스트(.txt)", "Playground": "놀이터", "Playwright Timeout (ms)": "Playwright 시간 초과 (ms)", "Playwright WebSocket URL": "Playwright WebSocket URL", "Please carefully review the following warnings:": "다음 주의를 조심히 확인해주십시오", "Please do not close the settings page while loading the model.": "모델을 로드하는 동안 설정 페이지를 닫지 마세요.", "Please enter a prompt": "프롬프트를 입력해주세요", "Please enter a valid path": "유효한 경로를 입력하세요", "Please enter a valid URL": "유효한 URL을 입력하세요", "Please fill in all fields.": "모두 빈칸없이 채워주세요", "Please select a model first.": "먼저 모델을 선택하세요.", "Please select a model.": "모델을 선택하세요.", "Please select a reason": "이유를 선택해주세요", "Port": "포트", "Positive attitude": "긍정적인 자세", "Prefix ID": "Prefix ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Prefix ID는 모델 ID에 접두사를 추가하여 다른 연결과의 충돌을 방지하는 데 사용됩니다. - 비활성화하려면 비워 둡니다.", "Prevent file creation": "", "Preview": "", "Previous 30 days": "이전 30일", "Previous 7 days": "이전 7일", "Previous message": "", "Private": "비공개", "Profile Image": "프로필 이미지", "Prompt": "프롬프트", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "프롬프트 (예: 로마 황제에 대해 재미있는 사실을 알려주세요)", "Prompt Autocompletion": "프롬프트 자동 완성", "Prompt Content": "프롬프트 내용", "Prompt created successfully": "성공적으로 프롬프트를 생성했습니다", "Prompt suggestions": "프롬프트 제안", "Prompt updated successfully": "성공적으로 프롬프트를 수정했습니다", "Prompts": "프롬프트", "Prompts Access": "프롬프트 접근", "Prompts Public Sharing": "프롬프트 공개 공유", "Public": "공개", "Pull \"{{searchValue}}\" from Ollama.com": "Ollama.com에서 \"{{searchValue}}\" 가져오기", "Pull a model from Ollama.com": "Ollama.com에서 모델 가져오기(pull)", "Query Generation Prompt": "쿼리 생성 프롬프트", "RAG Template": "RAG 템플릿", "Rating": "평가", "Re-rank models by topic similarity": "주제 유사성으로 모델을 재정렬하기", "Read": "읽기", "Read Aloud": "읽어주기", "Reason": "", "Reasoning Effort": "추론 난이도", "Record": "", "Record voice": "음성 녹음", "Redirecting you to Open WebUI Community": "OpenWebUI 커뮤니티로 리디렉션 중", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "넌센스를 생성할 확률을 줄입니다. 값이 높을수록(예: 100) 더 다양한 답변을 제공하는 반면, 값이 낮을수록(예: 10) 더 보수적입니다.", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "스스로를 \"사용자\" 라고 지칭하세요. (예: \"사용자는 영어를 배우고 있습니다\")", "References from": "출처", "Refused when it shouldn't have": "허용되지 않았지만 허용되어야 합니다.", "Regenerate": "재생성", "Reindex": "재색인", "Reindex Knowledge Base Vectors": "전체 지식 베이스 재색인", "Release Notes": "릴리스 노트", "Releases": "", "Relevance": "관련도", "Relevance Threshold": "관련성 임계값", "Remove": "삭제", "Remove {{MODELID}} from list.": "", "Remove Model": "모델 삭제", "Remove this tag from list": "", "Rename": "이름 변경", "Reorder Models": "모델 재정렬", "Reply in Thread": "스레드로 답장하기", "Reranking Engine": "", "Reranking Model": "Reranking 모델", "Reset": "초기화", "Reset All Models": "모든 모델 초기화", "Reset Upload Directory": "업로드 디렉토리 초기화", "Reset Vector Storage/Knowledge": "벡터 저장 공간/지식 기반 초기화", "Reset view": "보기 초기화", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "웹사이트 권환과 같이 응답 알림이 활성화될 수 없습니다. 필요한 접근을 사용하기 위해 브라우져 설정을 확인 부탁드립니다.", "Response splitting": "응답 나누기", "Response Watermark": "", "Result": "결과", "Retrieval": "검색", "Retrieval Query Generation": "검색 쿼리 생성", "Rich Text Input for Chat": "채팅을 위한 풍부한 텍스트(Rich Text) 입력", "RK": "RK", "Role": "역할", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "실행시키기", "Running": "실행 중", "Save": "저장", "Save & Create": "저장 및 생성", "Save & Update": "저장 및 업데이트", "Save As Copy": "다른 이름으로 저장", "Save Tag": "태그 저장", "Saved": "저장됨", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "브라우저의 저장소에 채팅 로그를 직접 저장하는 것은 더 이상 지원되지 않습니다. 아래 버튼을 클릭하여 채팅 로그를 다운로드하고 삭제하세요. 걱정 마세요. 백엔드를 통해 채팅 로그를 쉽게 다시 가져올 수 있습니다.", "Scroll On Branch Change": "브랜치 변경 시 스크롤", "Search": "검색", "Search a model": "모델 검색", "Search Base": "검색 기반", "Search Chats": "채팅 검색", "Search Collection": "컬렉션 검색", "Search Filters": "필터 검색", "search for tags": "태그 검색", "Search Functions": "함수 검색", "Search Knowledge": "지식 기반 검색", "Search Models": "모델 검색", "Search options": "검색 옵션", "Search Prompts": "프롬프트 검색", "Search Result Count": "검색 결과 수", "Search the internet": "인터넷 검색", "Search Tools": "검색 도구", "SearchApi API Key": "SearchApi API 키", "SearchApi Engine": "SearchApi 엔진", "Searched {{count}} sites": "{{count}}개 사이트 검색됨", "Searching \"{{searchQuery}}\"": "\"{{searchQuery}}\" 검색 중", "Searching Knowledge for \"{{searchQuery}}\"": "\"{{searchQuery}}\"위한 지식 기반 검색 중", "Searching the web...": "웹에서 검색 중...", "Searxng Query URL": "Searxng 쿼리 URL", "See readme.md for instructions": "설명은 readme.md를 참조하세요.", "See what's new": "새로운 기능 보기", "Seed": "시드", "Select a base model": "기본 모델 선택", "Select a engine": "엔진 선택", "Select a function": "함수 선택", "Select a group": "그룹 선택", "Select a model": "모델 선택", "Select a pipeline": "파이프라인 선택", "Select a pipeline url": "파이프라인 URL 선택", "Select a tool": "도구 선택", "Select an auth method": "인증 방법 선택", "Select an Ollama instance": "Ollama 인스턴스 선택", "Select Engine": "엔진 선택", "Select Knowledge": "지식 기반 선택", "Select only one model to call": "음성 기능을 위해서는 모델을 하나만 선택해야 합니다.", "Selected model(s) do not support image inputs": "선택한 모델은 이미지 입력을 지원하지 않습니다.", "Semantic distance to query": "쿼리까지 의미적 거리", "Send": "보내기", "Send a Message": "메시지 보내기", "Send message": "메시지 보내기", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "'stream_options: { include_usage: true }' 요청 보내기 \n지원되는 제공자가 토큰 사용 정보를 응답할 예정입니다", "September": "9월", "SerpApi API Key": "SerpApi API 키", "SerpApi Engine": "SerpApi 엔진", "Serper API Key": "Serper API 키", "Serply API Key": "Serply API 키", "Serpstack API Key": "Serpstack API 키", "Server connection verified": "서버 연결 확인됨", "Set as default": "기본값으로 설정", "Set CFG Scale": "CFG Scale 설정", "Set Default Model": "기본 모델 설정", "Set embedding model": "임베딩 모델 설정", "Set embedding model (e.g. {{model}})": "임베딩 모델 설정 (예: {{model}})", "Set Image Size": "이미지 크기 설정", "Set reranking model (e.g. {{model}})": "Reranking 모델 설정 (예: {{model}})", "Set Sampler": "샘플러 설정", "Set Scheduler": "스케쥴러 설정", "Set Steps": "단계 설정", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "GPU에 오프로드될 레이어 수를 설정합니다. 이 값을 높이면 GPU 가속에 최적화된 모델의 성능이 크게 향상될 수 있지만 더 많은 전력과 GPU 리소스를 소비할 수도 있습니다.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "계산에 사용되는 작업자 스레드 수를 설정합니다. 이 옵션은 들어오는 요청을 동시에 처리하는 데 사용되는 스레드 수를 제어합니다. 이 값을 높이면 동시성이 높은 워크로드에서 성능을 향상시킬 수 있지만 더 많은 CPU 리소스를 소비할 수도 있습니다.", "Set Voice": "음성 설정", "Set whisper model": "자막 생성기 모델 설정", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "적어도 한 번 이상 나타난 토큰에 대해 평평한 편향을 설정합니다. 값이 높을수록 반복에 더 강력한 불이익을 주는 반면, 값이 낮을수록(예: 0.9) 더 관대해집니다. 0에서는 비활성화됩니다.", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "토큰에 대한 스케일링 편향을 설정하여 반복 횟수에 따라 반복 횟수에 불이익을 줍니다. 값이 높을수록(예: 1.5) 반복 횟수에 더 강하게 불이익을 주는 반면, 값이 낮을수록(예: 0.9) 더 관대해집니다. 0에서는 반복 횟수가 비활성화됩니다.", "Sets how far back for the model to look back to prevent repetition.": "모델이 반복을 방지하기 위해 되돌아볼 수 있는 거리를 설정합니다.", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "생성에 사용할 난수 시드를 설정합니다. 이를 특정 숫자로 설정하면 모델이 동일한 프롬프트에 대해 동일한 텍스트를 생성하게 됩니다.", "Sets the size of the context window used to generate the next token.": "다음 토큰을 생성하는 데 사용되는 컨텍스트 창의 크기를 설정합니다.", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "중단 시퀀스를 설정합니다. 이 패턴이 발생하면 LLM은 텍스트 생성을 중단하고 반환합니다. 여러 중단 패턴은 모델 파일에서 여러 개의 별도 중단 매개변수를 지정하여 설정할 수 있습니다.", "Settings": "설정", "Settings saved successfully!": "설정이 성공적으로 저장되었습니다!", "Share": "공유", "Share Chat": "채팅 공유", "Share to Open WebUI Community": "OpenWebUI 커뮤니티에 공유", "Sharing Permissions": "권한 공유", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "보기", "Show \"What's New\" modal on login": "로그인시 \"새로운 기능\" 모달 보기", "Show Admin Details in Account Pending Overlay": "사용자용 계정 보류 설명창에, 관리자 상세 정보 노출", "Show All": "모두 보기", "Show Less": "간략히 보기", "Show Model": "모델 보기", "Show shortcuts": "단축키 보기", "Show your support!": "당신의 응원을 보내주세요!", "Showcased creativity": "창의성 발휘", "Sign in": "로그인", "Sign in to {{WEBUI_NAME}}": "{{WEBUI_NAME}}로 로그인", "Sign in to {{WEBUI_NAME}} with LDAP": "LDAP로 {{WEBUI_NAME}}에 로그인", "Sign Out": "로그아웃", "Sign up": "가입", "Sign up to {{WEBUI_NAME}}": "{{WEBUI_NAME}}로 가입", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "", "Signing in to {{WEBUI_NAME}}": "{{WEBUI_NAME}}로 가입중", "sk-1234": "", "Skip Cache": "캐시 무시", "Skip the cache and re-run the inference. Defaults to False.": "캐시를 무시하고 추론을 다시 실행합니다. 기본값은 False입니다.", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "출처", "Speech Playback Speed": "음성 재생 속도", "Speech recognition error: {{error}}": "음성 인식 오류: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "음성-텍스트 변환 엔진", "Stop": "정지", "Stop Generating": "", "Stop Sequence": "중지 시퀀스", "Stream Chat Response": "스트림 채팅 응답", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "STT 모델", "STT Settings": "STT 설정", "Stylized PDF Export": "서식이 적용된 PDF 내보내기", "Subtitle (e.g. about the Roman Empire)": "자막 (예: 로마 황제)", "Success": "성공", "Successfully updated.": "성공적으로 업데이트되었습니다.", "Suggested": "제안", "Support": "지원", "Support this plugin:": "플러그인 지원", "Supported MIME Types": "", "Sync directory": "디렉토리 연동", "System": "시스템", "System Instructions": "시스템 설명서", "System Prompt": "시스템 프롬프트", "Tags": "태그", "Tags Generation": "태그 생성", "Tags Generation Prompt": "태그 생성 프롬프트", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "꼬리 자유 샘플링은 출력에서 확률이 낮은 토큰의 영향을 줄이기 위해 사용됩니다. 값이 클수록(예: 2.0) 이러한 토큰의 영향이 더 줄어들며, 1.0으로 설정하면 이 기능은 비활성화됩니다.", "Talk to model": "모델과 대화하기", "Tap to interrupt": "탭하여 중단", "Task Model": "", "Tasks": "작업", "Tavily API Key": "Tavily API 키", "Tavily Extract Depth": "Tabily 깊이 추출", "Tell us more:": "더 알려주세요:", "Temperature": "온도", "Temporary Chat": "임시 채팅", "Text Splitter": "텍스트 나누기", "Text-to-Speech": "", "Text-to-Speech Engine": "텍스트-음성 변환 엔진", "Thanks for your feedback!": "피드백 감사합니다!", "The Application Account DN you bind with for search": "검색을 위해 바인딩하는 애플리케이션 계정 DN", "The base to search for users": "사용자를 검색할 수 있는 기반", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "배치 크기에 따라 한 번에 처리되는 텍스트 요청의 수가 결정됩니다. 배치 크기가 크면 모델의 성능과 속도가 향상될 수 있지만 더 많은 메모리가 필요하기도 합니다.", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "이 플러그인은 커뮤니티의 열정적인 자원봉사자들이 개발했습니다. 유용하게 사용하셨다면 개발에 기여해 주시는 것도 고려해 주세요.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "평가 리더보드는 Elo 평가 시스템을 기반으로 하고 실시간으로 업데이트됩니다", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "사용자가 로그인하는 데 사용하는 메일에 매핑되는 LDAP 속성입니다.", "The LDAP attribute that maps to the username that users use to sign in.": "사용자가 로그인할 때 사용하는 사용자 이름에 매핑되는 LDAP 속성입니다.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "리더보드는 현재 베타 버전이며, 알고리즘 개선에 따라 평가 방식이 변경될 수 있습니다.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "최대 파일 크기(MB). 만약 파일 크기가 한도를 초과할 시, 파일은 업로드되지 않습니다", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "하나의 채팅에서는 사용가능한 최대 파일 수가 있습니다. 만약 파일 수가 한도를 초과할 시, 파일은 업로드되지 않습니다.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "점수는 0.0(0%)에서 1.0(100%) 사이의 값이어야 합니다.", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "모델의 온도. 온도를 높이면 모델이 더 창의적으로 답변할 수 있습니다.", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "테마", "Thinking...": "생각 중...", "This action cannot be undone. Do you wish to continue?": "이 action은 되돌릴 수 없습니다. 계속 하시겠습니까?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "이 채널은 {{createAt}}에서 생성되었습니다. 이것은 {{channelName}} 채널의 시작입니다.", "This chat won't appear in history and your messages will not be saved.": "이 채팅은 기록에 나타나지 않으며 메시지가 저장되지 않습니다.", "This chat won’t appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "이렇게 하면 소중한 대화 내용이 백엔드 데이터베이스에 안전하게 저장됩니다. 감사합니다!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "이것은 실험적 기능으로, 예상대로 작동하지 않을 수 있으며 언제든지 변경될 수 있습니다.", "This model is not publicly available. Please select another model.": "이 모델은 공개적으로 사용할 수 없습니다. 다른 모델을 선택해주세요.", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "이 옵션은 요청 처리 후 모델이 메모리에 유지하는 시간을 제어합니다. (기본값: 5분)", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "이 옵션은 컨텍스트를 새로 고칠 때 보존되는 토큰의 수를 제어합니다. 예를 들어 2로 설정하면 대화 컨텍스트의 마지막 2개 토큰이 유지됩니다. 컨텍스트를 보존하면 대화의 연속성을 유지하는 데 도움이 될 수 있지만 새로운 주제에 대한 응답 능력이 감소할 수 있습니다.", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "이 옵션은 모델이 응답에서 생성할 수 있는 최대 토큰 수를 설정합니다. 이 한도를 늘리면 모델이 더 긴 답변을 제공할 수 있지만, 도움이 되지 않거나 관련 없는 콘텐츠가 생성될 가능성도 높아질 수 있습니다.", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "이 옵션을 선택하면 기존 컬렉션의 모든 파일이 삭제되고, 새로 업로드된 파일로 대체됩니다.", "This response was generated by \"{{model}}\"": "\"{{model}}\"이 생성한 응답입니다", "This will delete": "이것은 다음을 삭제합니다.", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "<strong>{{NAME}}</strong> 와 <strong>모든 내용</strong>을 삭제합니다.", "This will delete all models including custom models": "이렇게 하면 사용자 지정 모델을 포함한 모든 모델이 삭제됩니다", "This will delete all models including custom models and cannot be undone.": "이렇게 하면 사용자 지정 모델을 포함한 모든 모델이 삭제되며 실행 취소할 수 없습니다.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "지식 기반과 모든 파일 연동을 초기화합니다. 계속 하시겠습니까?", "Thorough explanation": "완전한 설명", "Thought for {{DURATION}}": "{{DURATION}} 동안 생각함", "Thought for {{DURATION}} seconds": "{{DURATION}}초 동안 생각함", "Tika": "티카(<PERSON><PERSON>)", "Tika Server URL required.": "티카 서버 URL이 필요합니다.", "Tiktoken": "틱토큰 (Tiktoken)", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "팁: 각각의 교체 후 채팅 입력에서 탭 키를 눌러 여러 개의 변수 슬롯을 연속적으로 업데이트하세요.", "Title": "제목", "Title (e.g. Tell me a fun fact)": "제목 (예: 재미있는 사실을 알려주세요.)", "Title Auto-Generation": "제목 자동 생성", "Title cannot be an empty string.": "제목은 빈 문자열일 수 없습니다.", "Title Generation": "제목 생성", "Title Generation Prompt": "제목 생성 프롬프트", "TLS": "TLS", "To access the available model names for downloading,": "다운로드 가능한 모델명을 확인하려면,", "To access the GGUF models available for downloading,": "다운로드 가능한 GGUF 모델을 확인하려면,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "WebUI에 접속하려면 관리자에게 문의하십시오. 관리자는 관리자 패널에서 사용자 상태를 관리할 수 있습니다.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "지식 기반을 여기에 첨부하려면. \"지식 기반\" 워크스페이스에 먼저 추가하세요", "To learn more about available endpoints, visit our documentation.": "사용 가능한 엔드포인트에 대해 자세히 알아보려면 문서를 방문하세요.", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "개인정보를 보호하기 위해, 당신의 채팅 로그는 비공개로 유지되고, 오직 당신의 피드백에서 평가, 모델 ID, 태그, 그리고 메타데이타만 공유됩니다", "To select actions here, add them to the \"Functions\" workspace first.": "여기서 행동을 선택하려면, \"함수\" 워크스페이스에 먼저 추가하세요", "To select filters here, add them to the \"Functions\" workspace first.": "여기서 필터를 선택하려면, \"함수\" 워크스페이스에 먼저 추가하세요", "To select toolkits here, add them to the \"Tools\" workspace first.": "여기서 도구를 선택하려면, \"도구\" 워크스페이스에 먼저 추가하세요.", "Toast notifications for new updates": "업데이트 토스트 알림", "Today": "오늘", "Toggle search": "검색 전환", "Toggle settings": "설정 전환", "Toggle sidebar": "사이드바 전환", "Toggle whether current connection is active.": "", "Token": "토큰", "Too verbose": "말이 너무 많음", "Tool created successfully": "성공적으로 도구가 생성되었습니다.", "Tool deleted successfully": "성공적으로 도구가 삭제되었습니다.", "Tool Description": "도구 설명", "Tool ID": "도구 ID", "Tool imported successfully": "성공적으로 도구를 가져왔습니다", "Tool Name": "도구 이름", "Tool Servers": "도구 서버", "Tool updated successfully": "성공적으로 도구가 업데이트되었습니다", "Tools": "도구", "Tools Access": "도구 접근", "Tools are a function calling system with arbitrary code execution": "도구는 임의 코드를 실행시키는 함수를 불러오는 시스템입니다", "Tools Function Calling Prompt": "도구 함수 호출 프롬프트", "Tools have a function calling system that allows arbitrary code execution.": "도구에 임의 코드 실행을 허용하는 함수가 포함되어 있습니다.", "Tools Public Sharing": "도구 공개 및 공유", "Top K": "Top K", "Top K Reranker": "", "Transformers": "", "Trouble accessing Ollama?": "올라마(<PERSON><PERSON><PERSON>)에 접근하는 데 문제가 있나요?", "Trust Proxy Environment": "신뢰 할 수 있는 프록시 환경", "TTS Model": "TTS 모델", "TTS Settings": "TTS 설정", "TTS Voice": "TTS 음성", "Type": "입력", "Type Hugging Face Resolve (Download) URL": "Hugging Face Resolve (다운로드) URL 입력", "Uh-oh! There was an issue with the response.": "이런! 응답에 문제가 발생했습니다.", "UI": "UI", "Unarchive All": "모두 보관 해제", "Unarchive All Archived Chats": "보관된 모든 채팅을 보관 해제", "Unarchive Chat": "채팅 보관 해제", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "미스터리 풀기", "Unpin": "고정 해제", "Unravel secrets": "비밀 풀기", "Untagged": "태그 해제", "Untitled": "", "Update": "업데이트", "Update and Copy Link": "링크 업데이트 및 복사", "Update for the latest features and improvements.": "이번 업데이트의 새로운 기능과 개선", "Update password": "비밀번호 업데이트", "Updated": "업데이트됨", "Updated at": "다음에 업데이트됨", "Updated At": "다음에 업데이트됨", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "맞춤형 테마 설정 및 브랜딩, 전용 지원을 포함한 향상된 기능을 위해 라이선스 플랜으로 업그레이드하세요.", "Upload": "업로드", "Upload a GGUF model": "GGUF 모델 업로드", "Upload Audio": "오디오 업로드", "Upload directory": "디렉토리 업로드", "Upload files": "파일 업로드", "Upload Files": "파일 업로드", "Upload Pipeline": "업로드 파이프라인", "Upload Progress": "업로드 진행 상황", "URL": "URL", "URL Mode": "URL 모드", "Usage": "사용량", "Use '#' in the prompt input to load and include your knowledge.": "프롬프트 입력에서 '#'를 사용하여 지식 기반을 불러오고 포함하세요.", "Use Gravatar": "Gravatar 사용", "Use groups to group your users and assign permissions.": "그룹을 사용하여 사용자를 그룹화하고 권한을 할당하세요.", "Use Initials": "초성 사용", "Use LLM": "LLM 사용", "Use no proxy to fetch page contents.": "페이지 콘텐츠를 가져오려면 프록시를 사용하지 마세요.", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "http_proxy 및 https_proxy 환경 변수로 지정된 프록시를 사용하여 페이지 콘텐츠를 가져옵니다.", "user": "사용자", "User": "사용자", "User location successfully retrieved.": "성공적으로 사용자의 위치를 불러왔습니다", "User Webhooks": "사용자 웹훅", "Username": "사용자 이름", "Users": "사용자", "Using the default arena model with all models. Click the plus button to add custom models.": "모든 모델은 기본 아레나 모델을 사용중입니다. 플러스 버튼을 눌러 커스텀 모델을 추가하세요.", "Utilize": "활용", "Valid time units:": "유효 시간 단위:", "Valves": "밸브", "Valves updated": "밸브 업데이트됨", "Valves updated successfully": "성공적으로 밸브가 업데이트되었습니다", "variable": "변수", "variable to have them replaced with clipboard content.": "변수를 사용하여 클립보드 내용으로 바꾸세요.", "Verify Connection": "연결 확인", "Verify SSL Certificate": "SSL 인증서 확인", "Version": "버전", "Version {{selectedVersion}} of {{totalVersions}}": "버전 {{totalVersions}}의 {{selectedVersion}}", "View Replies": "답글 보기", "View Result from **{{NAME}}**": "**{{NAME}}**의 결과 보기", "Visibility": "공개 범위", "Vision": "", "Voice": "음성", "Voice Input": "음성 입력", "Voice mode": "음성 모드 사용", "Warning": "경고", "Warning:": "주의:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "주의: 이 기능을 활성화하면 사용자가 서버에 임의 코드를 업로드할 수 있습니다.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "주의: 기존 임베딩 모델을 변경 또는 업데이트하는 경우, 모든 문서를 다시 가져와야 합니다.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "경고: <PERSON><PERSON><PERSON> 실행은 임의의 코드 실행을 가능하게 하여 심각한 보안 위험을 초래합니다. — 매우 신중하게 진행하세요.", "Web": "웹", "Web API": "웹 API", "Web Loader Engine": "웹 로더 엔진", "Web Search": "웹 검색", "Web Search Engine": "웹 검색 엔진", "Web Search in Chat": "채팅에서 웹 검색", "Web Search Query Generation": "웹 검색 쿼리 생성", "Webhook URL": "웹훅 URL", "WebUI Settings": "WebUI 설정", "WebUI URL": "", "WebUI will make requests to \"{{url}}\"": "WebUI가 \"{{url}}\"로 요청을 보냅니다", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI가 \"{{url}}/api/chat\"로 요청을 보냅니다", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI가 \"{{url}}/chat/completions\"로 요청을 보냅니다", "Weight of BM25 Retrieval": "", "What are you trying to achieve?": "무엇을 성취하고 싶으신가요?", "What are you working on?": "어떤 작업을 하고 계신가요?", "What's New in": "새로운 기능:", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "활성화하면 모델이 각 채팅 메시지에 실시간으로 응답하여 사용자가 메시지를 보내는 즉시 응답을 생성합니다. 이 모드는 실시간 채팅 애플리케이션에 유용하지만, 느린 하드웨어에서는 성능에 영향을 미칠 수 있습니다.", "wherever you are": "당신이 어디에 있든", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "<PERSON>his<PERSON> (로컬)", "Why?": "이유는?", "Widescreen Mode": "와이드스크린 모드", "Won": "승리", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "top-k와 함께 작동합니다. 값이 높을수록(예: 0.95) 더 다양한 텍스트가 생성되고, 값이 낮을수록(예: 0.5) 더 집중적이고 보수적인 텍스트가 생성됩니다.", "Workspace": "워크스페이스", "Workspace Permissions": "워크스페이스 권한", "Write": "작성", "Write a prompt suggestion (e.g. Who are you?)": "프롬프트 제안 작성 (예: 당신은 누구인가요?)", "Write a summary in 50 words that summarizes [topic or keyword].": "[주제 또는 키워드]에 대한 50단어 요약문을 작성하시오.", "Write something...": "내용을 입력하세요…", "Yacy Instance URL": "<PERSON><PERSON> 인스턴스 URL", "Yacy Password": "<PERSON><PERSON> 비밀번호", "Yacy Username": "<PERSON><PERSON> 사용자 이름", "Yesterday": "어제", "You": "당신", "You are currently using a trial license. Please contact support to upgrade your license.": "현재 평가판 라이선스를 사용 중입니다. 라이선스를 업그레이드하려면 지원팀에 문의하세요.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "최대 {{maxCount}}개의 파일과만 동시에 대화할 수 있습니다 ", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "아래 '관리' 버튼으로 메모리를 추가하여 LLM들과의 상호작용을 개인화할 수 있습니다. 이를 통해 더 유용하고 맞춤화된 경험을 제공합니다.", "You cannot upload an empty file.": "빈 파일을 업로드 할 수 없습니다", "You do not have permission to upload files.": "파일을 업로드할 권한이 없습니다.", "You have no archived conversations.": "채팅을 보관한 적이 없습니다.", "You have shared this chat": "이 채팅을 공유했습니다.", "You're a helpful assistant.": "당신은 유용한 어시스턴트입니다.", "You're now logged in.": "로그인되었습니다.", "Your account status is currently pending activation.": "현재 계정은 아직 활성화되지 않았습니다.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "당신의 모든 기여는 곧바로 플러그인 개발자에게 갑니다; Open WebUI는 수수료를 받지 않습니다. 다만, 선택한 후원 플랫폼은 수수료를 가져갈 수 있습니다.", "Youtube": "유튜브", "Youtube Language": "Youtube 언어", "Youtube Proxy URL": "Youtube 프록시 URL"}