"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/auth/login",{

/***/ "./src/pages/auth/login.tsx":
/*!**********************************!*\
  !*** ./src/pages/auth/login.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff!=!lucide-react */ \"__barrel_optimize__?names=Eye,EyeOff!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"./src/components/ui/input.tsx\");\n/* harmony import */ var _config_chatConfig__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/chatConfig */ \"./src/config/chatConfig.ts\");\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/context/UserContext */ \"./src/context/UserContext.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst LoginPage = ()=>{\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"<EMAIL>\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [serverHealth, setServerHealth] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [loginSuccess, setLoginSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { login, authState } = (0,_context_UserContext__WEBPACK_IMPORTED_MODULE_5__.useUserContext)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    // Get deployment URL from configuration\n    const deploymentUrl = (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_4__.getDeploymentUrl)();\n    // Load default credentials from runtime config if available\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if ( true && window.__RUNTIME_CONFIG__) {\n            if (window.__RUNTIME_CONFIG__.NEXT_PUBLIC_R2R_DEFAULT_EMAIL && !window.__RUNTIME_CONFIG__.NEXT_PUBLIC_R2R_DEFAULT_EMAIL.includes(\"__NEXT_PUBLIC_R2R_DEFAULT_EMAIL__\")) {\n                setEmail(window.__RUNTIME_CONFIG__.NEXT_PUBLIC_R2R_DEFAULT_EMAIL);\n            }\n            if (window.__RUNTIME_CONFIG__.NEXT_PUBLIC_R2R_DEFAULT_PASSWORD && !window.__RUNTIME_CONFIG__.NEXT_PUBLIC_R2R_DEFAULT_PASSWORD.includes(\"__NEXT_PUBLIC_R2R_DEFAULT_PASSWORD__\")) {\n                setPassword(window.__RUNTIME_CONFIG__.NEXT_PUBLIC_R2R_DEFAULT_PASSWORD);\n            }\n        }\n    }, []);\n    // Check deployment health\n    const checkDeploymentHealth = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(deploymentUrl, \"/v3/health\"));\n            return response.ok;\n        } catch (error) {\n            console.error(\"Health check failed:\", error);\n            return false;\n        }\n    };\n    // Handle login submission\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(null);\n        try {\n            const result = await login(email, password, deploymentUrl);\n            if (result) {\n                setLoginSuccess(true);\n            }\n        } catch (error) {\n            console.error(\"Login failed:\", error);\n            // Only check server health after a failed login attempt\n            const isServerHealthy = await checkDeploymentHealth();\n            let errorMessage = \"An unknown error occurred\";\n            if (error instanceof Error) {\n                errorMessage = error.message;\n            } else if (typeof error === \"string\") {\n                errorMessage = error;\n            }\n            // Provide appropriate error message based on server health\n            const serverStatusMessage = isServerHealthy ? \"Server is running normally, please check your credentials and try again.\" : \"Unable to communicate with server, please check the API address in the configuration file.\";\n            setError(\"Login failed. \".concat(serverStatusMessage, \"\\n\\nError: \").concat(errorMessage));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Handle successful login redirect\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (loginSuccess && authState.isAuthenticated) {\n            router.push(\"/\");\n        }\n    }, [\n        loginSuccess,\n        authState.isAuthenticated,\n        router\n    ]);\n    const togglePasswordVisibility = ()=>{\n        setShowPassword(!showPassword);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-screen max-h-[100dvh] text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full absolute top-0 left-0 bg-white dark:bg-black\"\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full absolute top-0 left-0 right-0 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed m-10 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"self-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-6 h-6 rounded-full bg-gradient-to-r from-blue-600 to-purple-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bg-transparent min-h-screen w-full flex justify-center font-primary z-50 text-black dark:text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full sm:max-w-md px-10 min-h-screen flex flex-col text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-auto pb-10 w-full dark:text-gray-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                className: \"flex flex-col justify-center\",\n                                onSubmit: handleSubmit,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-medium\",\n                                            children: \"Sign in to RChat\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"email\",\n                                                        className: \"text-sm font-medium text-left mb-1 block\",\n                                                        children: \"Email\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"email\",\n                                                        type: \"email\",\n                                                        className: \"my-0.5 w-full text-sm bg-transparent border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2\",\n                                                        autoComplete: \"email\",\n                                                        name: \"email\",\n                                                        placeholder: \"Enter Your Email\",\n                                                        value: email,\n                                                        onChange: (e)=>setEmail(e.target.value),\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"password\",\n                                                        className: \"text-sm font-medium text-left mb-1 block\",\n                                                        children: \"Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"password\",\n                                                                type: showPassword ? \"text\" : \"password\",\n                                                                className: \"my-0.5 w-full text-sm bg-transparent border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 pr-10\",\n                                                                placeholder: \"Enter Your Password\",\n                                                                autoComplete: \"current-password\",\n                                                                name: \"current-password\",\n                                                                value: password,\n                                                                onChange: (e)=>setPassword(e.target.value),\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: togglePasswordVisibility,\n                                                                className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\",\n                                                                children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__.EyeOff, {\n                                                                    className: \"h-5 w-5\",\n                                                                    \"aria-hidden\": \"true\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 25\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Eye, {\n                                                                    className: \"h-5 w-5\",\n                                                                    \"aria-hidden\": \"true\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"bg-gray-700/5 hover:bg-gray-700/10 dark:bg-gray-100/5 dark:hover:bg-gray-100/10 dark:text-gray-300 dark:hover:text-white transition w-full rounded-full font-medium text-sm py-2.5\",\n                                            type: \"submit\",\n                                            disabled: isLoading,\n                                            children: isLoading ? \"Signing in...\" : \"Sign in\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, undefined),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 dark:text-red-400 text-sm whitespace-pre-line\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LoginPage, \"AlyufcWgaw+rw9150PW5bu5x1DM=\", false, function() {\n    return [\n        _context_UserContext__WEBPACK_IMPORTED_MODULE_5__.useUserContext,\n        next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter\n    ];\n});\n_c = LoginPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LoginPage);\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/auth/login.tsx\n"));

/***/ })

});