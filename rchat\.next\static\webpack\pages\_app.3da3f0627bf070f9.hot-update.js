"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./src/context/UserContext.tsx":
/*!*************************************!*\
  !*** ./src/context/UserContext.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: function() { return /* binding */ UserProvider; },\n/* harmony export */   useUserContext: function() { return /* binding */ useUserContext; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var r2r_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! r2r-js */ \"./node_modules/r2r-js/dist/index.js\");\n/* harmony import */ var r2r_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(r2r_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _config_chatConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../config/chatConfig */ \"./src/config/chatConfig.ts\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst UserProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isAuthenticated: false,\n        user: null,\n        token: null\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [client, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Initialize client and check for existing session\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            try {\n                const deploymentUrl = (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_3__.getDeploymentUrl)();\n                const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(deploymentUrl);\n                setClient(newClient);\n                // Check for stored token (try both rchat and chatfrontend token keys)\n                const storedToken = localStorage.getItem(\"chatAccessToken\") || localStorage.getItem(\"r2r_token\");\n                const storedRefreshToken = localStorage.getItem(\"chatRefreshToken\");\n                const storedUser = localStorage.getItem(\"r2r_user\");\n                if (storedToken && storedUser) {\n                    try {\n                        const user = JSON.parse(storedUser);\n                        // Set up client with stored tokens\n                        if (storedRefreshToken) {\n                            newClient.setTokens(storedToken, storedRefreshToken);\n                        }\n                        setAuthState({\n                            isAuthenticated: true,\n                            user,\n                            token: storedToken\n                        });\n                    } catch (error) {\n                        console.error(\"Error parsing stored user data:\", error);\n                        localStorage.removeItem(\"r2r_token\");\n                        localStorage.removeItem(\"r2r_user\");\n                        localStorage.removeItem(\"chatAccessToken\");\n                        localStorage.removeItem(\"chatRefreshToken\");\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeAuth();\n    }, []);\n    const login = async (email, password, deploymentUrl)=>{\n        try {\n            const apiUrl = deploymentUrl || (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_3__.getDeploymentUrl)();\n            const loginClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(apiUrl);\n            const tokens = await loginClient.users.login({\n                email: email,\n                password: password\n            });\n            if (!tokens.results) {\n                throw new Error(\"Login failed: No results returned\");\n            }\n            // Store tokens like chatfrontend does\n            localStorage.setItem(\"chatAccessToken\", tokens.results.accessToken.token);\n            localStorage.setItem(\"chatRefreshToken\", tokens.results.refreshToken.token);\n            // Set tokens on client\n            loginClient.setTokens(tokens.results.accessToken.token, tokens.results.refreshToken.token);\n            setClient(loginClient);\n            // Get user info\n            const userInfo = await loginClient.users.me();\n            if (!userInfo.results) {\n                throw new Error(\"Failed to get user information\");\n            }\n            // Check user role like chatfrontend does\n            let userRole = \"user\";\n            try {\n                await loginClient.system.settings();\n                userRole = \"admin\";\n            } catch (error) {\n                if (error instanceof Error && \"status\" in error && error.status === 403) {\n                // User doesn't have admin access, keep as \"user\"\n                } else {\n                    console.error(\"Unexpected error when checking user role:\", error);\n                }\n            }\n            const user = {\n                id: userInfo.results.id,\n                email: userInfo.results.email || email,\n                name: userInfo.results.name,\n                role: userRole\n            };\n            // Store auth data\n            localStorage.setItem(\"r2r_token\", tokens.results.accessToken.token);\n            localStorage.setItem(\"r2r_user\", JSON.stringify(user));\n            // Update state\n            setAuthState({\n                isAuthenticated: true,\n                user,\n                token: tokens.results.accessToken.token\n            });\n            return user;\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            // Provide more detailed error information\n            if (error instanceof Error) {\n                if (error.message.includes(\"401\") || error.message.includes(\"Unauthorized\")) {\n                    throw new Error(\"Invalid email or password. Please check your credentials.\");\n                } else if (error.message.includes(\"404\") || error.message.includes(\"Not Found\")) {\n                    throw new Error(\"User not found. Please check your email address.\");\n                } else if (error.message.includes(\"500\") || error.message.includes(\"Internal Server Error\")) {\n                    throw new Error(\"Server error. Please try again later.\");\n                } else if (error.message.includes(\"Network Error\") || error.message.includes(\"fetch\")) {\n                    throw new Error(\"Cannot connect to server. Please check if R2R backend is running.\");\n                }\n            }\n            throw error;\n        }\n    };\n    const logout = ()=>{\n        localStorage.removeItem(\"r2r_token\");\n        localStorage.removeItem(\"r2r_user\");\n        localStorage.removeItem(\"chatAccessToken\");\n        localStorage.removeItem(\"chatRefreshToken\");\n        setAuthState({\n            isAuthenticated: false,\n            user: null,\n            token: null\n        });\n        setClient(null);\n    };\n    const getClient = async ()=>{\n        if (!authState.isAuthenticated || !authState.token) {\n            return null;\n        }\n        if (!client) {\n            const deploymentUrl = (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_3__.getDeploymentUrl)();\n            const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(deploymentUrl);\n            // Set tokens if available\n            const accessToken = localStorage.getItem(\"chatAccessToken\") || localStorage.getItem(\"r2r_token\");\n            const refreshToken = localStorage.getItem(\"chatRefreshToken\");\n            if (accessToken) {\n                newClient.setTokens(accessToken, refreshToken || \"\");\n            }\n            setClient(newClient);\n            return newClient;\n        }\n        return client;\n    };\n    const value = {\n        authState,\n        login,\n        logout,\n        getClient,\n        isLoading\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\context\\\\UserContext.tsx\",\n        lineNumber: 204,\n        columnNumber: 10\n    }, undefined);\n};\n_s(UserProvider, \"MnU6bMppC5JK74JWRxax5UB/SjI=\");\n_c = UserProvider;\nconst useUserContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (context === undefined) {\n        throw new Error(\"useUserContext must be used within a UserProvider\");\n    }\n    return context;\n};\n_s1(useUserContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"UserProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29udGV4dC9Vc2VyQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBeUY7QUFDdEQ7QUFFcUI7QUFVeEQsTUFBTU8sNEJBQWNOLG9EQUFhQSxDQUE4Qk87QUFNeEQsTUFBTUMsZUFBNEM7UUFBQyxFQUFFQyxRQUFRLEVBQUU7O0lBQ3BFLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHVCwrQ0FBUUEsQ0FBWTtRQUNwRFUsaUJBQWlCO1FBQ2pCQyxNQUFNO1FBQ05DLE9BQU87SUFDVDtJQUNBLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHZCwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNlLFFBQVFDLFVBQVUsR0FBR2hCLCtDQUFRQSxDQUFtQjtJQUV2RCxtREFBbUQ7SUFDbkRDLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTWdCLGlCQUFpQjtZQUNyQixJQUFJO2dCQUNGLE1BQU1DLGdCQUFnQmYsb0VBQWdCQTtnQkFDdEMsTUFBTWdCLFlBQVksSUFBSWpCLDZDQUFTQSxDQUFDZ0I7Z0JBQ2hDRixVQUFVRztnQkFFVixzRUFBc0U7Z0JBQ3RFLE1BQU1DLGNBQWNDLGFBQWFDLE9BQU8sQ0FBQyxzQkFBc0JELGFBQWFDLE9BQU8sQ0FBQztnQkFDcEYsTUFBTUMscUJBQXFCRixhQUFhQyxPQUFPLENBQUM7Z0JBQ2hELE1BQU1FLGFBQWFILGFBQWFDLE9BQU8sQ0FBQztnQkFFeEMsSUFBSUYsZUFBZUksWUFBWTtvQkFDN0IsSUFBSTt3QkFDRixNQUFNYixPQUFPYyxLQUFLQyxLQUFLLENBQUNGO3dCQUV4QixtQ0FBbUM7d0JBQ25DLElBQUlELG9CQUFvQjs0QkFDdEJKLFVBQVVRLFNBQVMsQ0FBQ1AsYUFBYUc7d0JBQ25DO3dCQUVBZCxhQUFhOzRCQUNYQyxpQkFBaUI7NEJBQ2pCQzs0QkFDQUMsT0FBT1E7d0JBQ1Q7b0JBQ0YsRUFBRSxPQUFPUSxPQUFPO3dCQUNkQyxRQUFRRCxLQUFLLENBQUMsbUNBQW1DQTt3QkFDakRQLGFBQWFTLFVBQVUsQ0FBQzt3QkFDeEJULGFBQWFTLFVBQVUsQ0FBQzt3QkFDeEJULGFBQWFTLFVBQVUsQ0FBQzt3QkFDeEJULGFBQWFTLFVBQVUsQ0FBQztvQkFDMUI7Z0JBQ0Y7WUFDRixFQUFFLE9BQU9GLE9BQU87Z0JBQ2RDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO1lBQzVDLFNBQVU7Z0JBQ1JkLGFBQWE7WUFDZjtRQUNGO1FBRUFHO0lBQ0YsR0FBRyxFQUFFO0lBRUwsTUFBTWMsUUFBUSxPQUFPQyxPQUFlQyxVQUFrQmY7UUFDcEQsSUFBSTtZQUNGLE1BQU1nQixTQUFTaEIsaUJBQWlCZixvRUFBZ0JBO1lBQ2hELE1BQU1nQyxjQUFjLElBQUlqQyw2Q0FBU0EsQ0FBQ2dDO1lBRWxDLE1BQU1FLFNBQVMsTUFBTUQsWUFBWUUsS0FBSyxDQUFDTixLQUFLLENBQUM7Z0JBQzNDQyxPQUFPQTtnQkFDUEMsVUFBVUE7WUFDWjtZQUVBLElBQUksQ0FBQ0csT0FBT0UsT0FBTyxFQUFFO2dCQUNuQixNQUFNLElBQUlDLE1BQU07WUFDbEI7WUFFQSxzQ0FBc0M7WUFDdENsQixhQUFhbUIsT0FBTyxDQUFDLG1CQUFtQkosT0FBT0UsT0FBTyxDQUFDRyxXQUFXLENBQUM3QixLQUFLO1lBQ3hFUyxhQUFhbUIsT0FBTyxDQUFDLG9CQUFvQkosT0FBT0UsT0FBTyxDQUFDSSxZQUFZLENBQUM5QixLQUFLO1lBRTFFLHVCQUF1QjtZQUN2QnVCLFlBQVlSLFNBQVMsQ0FDbkJTLE9BQU9FLE9BQU8sQ0FBQ0csV0FBVyxDQUFDN0IsS0FBSyxFQUNoQ3dCLE9BQU9FLE9BQU8sQ0FBQ0ksWUFBWSxDQUFDOUIsS0FBSztZQUduQ0ksVUFBVW1CO1lBRVYsZ0JBQWdCO1lBQ2hCLE1BQU1RLFdBQVcsTUFBTVIsWUFBWUUsS0FBSyxDQUFDTyxFQUFFO1lBRTNDLElBQUksQ0FBQ0QsU0FBU0wsT0FBTyxFQUFFO2dCQUNyQixNQUFNLElBQUlDLE1BQU07WUFDbEI7WUFFQSx5Q0FBeUM7WUFDekMsSUFBSU0sV0FBNkI7WUFDakMsSUFBSTtnQkFDRixNQUFNVixZQUFZVyxNQUFNLENBQUNDLFFBQVE7Z0JBQ2pDRixXQUFXO1lBQ2IsRUFBRSxPQUFPakIsT0FBTztnQkFDZCxJQUFJQSxpQkFBaUJXLFNBQVMsWUFBWVgsU0FBUyxNQUFlb0IsTUFBTSxLQUFLLEtBQUs7Z0JBQ2hGLGlEQUFpRDtnQkFDbkQsT0FBTztvQkFDTG5CLFFBQVFELEtBQUssQ0FBQyw2Q0FBNkNBO2dCQUM3RDtZQUNGO1lBRUEsTUFBTWpCLE9BQWE7Z0JBQ2pCc0MsSUFBSU4sU0FBU0wsT0FBTyxDQUFDVyxFQUFFO2dCQUN2QmpCLE9BQU9XLFNBQVNMLE9BQU8sQ0FBQ04sS0FBSyxJQUFJQTtnQkFDakNrQixNQUFNUCxTQUFTTCxPQUFPLENBQUNZLElBQUk7Z0JBQzNCQyxNQUFNTjtZQUNSO1lBRUEsa0JBQWtCO1lBQ2xCeEIsYUFBYW1CLE9BQU8sQ0FBQyxhQUFhSixPQUFPRSxPQUFPLENBQUNHLFdBQVcsQ0FBQzdCLEtBQUs7WUFDbEVTLGFBQWFtQixPQUFPLENBQUMsWUFBWWYsS0FBSzJCLFNBQVMsQ0FBQ3pDO1lBRWhELGVBQWU7WUFDZkYsYUFBYTtnQkFDWEMsaUJBQWlCO2dCQUNqQkM7Z0JBQ0FDLE9BQU93QixPQUFPRSxPQUFPLENBQUNHLFdBQVcsQ0FBQzdCLEtBQUs7WUFDekM7WUFFQSxPQUFPRDtRQUNULEVBQUUsT0FBT2lCLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGdCQUFnQkE7WUFFOUIsMENBQTBDO1lBQzFDLElBQUlBLGlCQUFpQlcsT0FBTztnQkFDMUIsSUFBSVgsTUFBTXlCLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDLFVBQVUxQixNQUFNeUIsT0FBTyxDQUFDQyxRQUFRLENBQUMsaUJBQWlCO29CQUMzRSxNQUFNLElBQUlmLE1BQU07Z0JBQ2xCLE9BQU8sSUFBSVgsTUFBTXlCLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDLFVBQVUxQixNQUFNeUIsT0FBTyxDQUFDQyxRQUFRLENBQUMsY0FBYztvQkFDL0UsTUFBTSxJQUFJZixNQUFNO2dCQUNsQixPQUFPLElBQUlYLE1BQU15QixPQUFPLENBQUNDLFFBQVEsQ0FBQyxVQUFVMUIsTUFBTXlCLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDLDBCQUEwQjtvQkFDM0YsTUFBTSxJQUFJZixNQUFNO2dCQUNsQixPQUFPLElBQUlYLE1BQU15QixPQUFPLENBQUNDLFFBQVEsQ0FBQyxvQkFBb0IxQixNQUFNeUIsT0FBTyxDQUFDQyxRQUFRLENBQUMsVUFBVTtvQkFDckYsTUFBTSxJQUFJZixNQUFNO2dCQUNsQjtZQUNGO1lBRUEsTUFBTVg7UUFDUjtJQUNGO0lBRUEsTUFBTTJCLFNBQVM7UUFDYmxDLGFBQWFTLFVBQVUsQ0FBQztRQUN4QlQsYUFBYVMsVUFBVSxDQUFDO1FBQ3hCVCxhQUFhUyxVQUFVLENBQUM7UUFDeEJULGFBQWFTLFVBQVUsQ0FBQztRQUN4QnJCLGFBQWE7WUFDWEMsaUJBQWlCO1lBQ2pCQyxNQUFNO1lBQ05DLE9BQU87UUFDVDtRQUNBSSxVQUFVO0lBQ1o7SUFFQSxNQUFNd0MsWUFBWTtRQUNoQixJQUFJLENBQUNoRCxVQUFVRSxlQUFlLElBQUksQ0FBQ0YsVUFBVUksS0FBSyxFQUFFO1lBQ2xELE9BQU87UUFDVDtRQUVBLElBQUksQ0FBQ0csUUFBUTtZQUNYLE1BQU1HLGdCQUFnQmYsb0VBQWdCQTtZQUN0QyxNQUFNZ0IsWUFBWSxJQUFJakIsNkNBQVNBLENBQUNnQjtZQUVoQywwQkFBMEI7WUFDMUIsTUFBTXVCLGNBQWNwQixhQUFhQyxPQUFPLENBQUMsc0JBQXNCRCxhQUFhQyxPQUFPLENBQUM7WUFDcEYsTUFBTW9CLGVBQWVyQixhQUFhQyxPQUFPLENBQUM7WUFFMUMsSUFBSW1CLGFBQWE7Z0JBQ2Z0QixVQUFVUSxTQUFTLENBQUNjLGFBQWFDLGdCQUFnQjtZQUNuRDtZQUVBMUIsVUFBVUc7WUFDVixPQUFPQTtRQUNUO1FBRUEsT0FBT0o7SUFDVDtJQUVBLE1BQU0wQyxRQUF5QjtRQUM3QmpEO1FBQ0F1QjtRQUNBd0I7UUFDQUM7UUFDQTNDO0lBQ0Y7SUFFQSxxQkFBTyw4REFBQ1QsWUFBWXNELFFBQVE7UUFBQ0QsT0FBT0E7a0JBQVFsRDs7Ozs7O0FBQzlDLEVBQUU7R0F6TFdEO0tBQUFBO0FBMkxOLE1BQU1xRCxpQkFBaUI7O0lBQzVCLE1BQU1DLFVBQVU3RCxpREFBVUEsQ0FBQ0s7SUFDM0IsSUFBSXdELFlBQVl2RCxXQUFXO1FBQ3pCLE1BQU0sSUFBSWtDLE1BQU07SUFDbEI7SUFDQSxPQUFPcUI7QUFDVCxFQUFFO0lBTldEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb250ZXh0L1VzZXJDb250ZXh0LnRzeD9jZWU3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgdXNlRWZmZWN0LCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyByMnJDbGllbnQgfSBmcm9tICdyMnItanMnO1xuaW1wb3J0IHsgVXNlciwgQXV0aFN0YXRlIH0gZnJvbSAnLi4vdHlwZXMnO1xuaW1wb3J0IHsgZ2V0RGVwbG95bWVudFVybCB9IGZyb20gJy4uL2NvbmZpZy9jaGF0Q29uZmlnJztcblxuaW50ZXJmYWNlIFVzZXJDb250ZXh0VHlwZSB7XG4gIGF1dGhTdGF0ZTogQXV0aFN0YXRlO1xuICBsb2dpbjogKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcsIGRlcGxveW1lbnRVcmw/OiBzdHJpbmcpID0+IFByb21pc2U8VXNlcj47XG4gIGxvZ291dDogKCkgPT4gdm9pZDtcbiAgZ2V0Q2xpZW50OiAoKSA9PiBQcm9taXNlPHIyckNsaWVudCB8IG51bGw+O1xuICBpc0xvYWRpbmc6IGJvb2xlYW47XG59XG5cbmNvbnN0IFVzZXJDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxVc2VyQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XG5cbmludGVyZmFjZSBVc2VyUHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGU7XG59XG5cbmV4cG9ydCBjb25zdCBVc2VyUHJvdmlkZXI6IFJlYWN0LkZDPFVzZXJQcm92aWRlclByb3BzPiA9ICh7IGNoaWxkcmVuIH0pID0+IHtcbiAgY29uc3QgW2F1dGhTdGF0ZSwgc2V0QXV0aFN0YXRlXSA9IHVzZVN0YXRlPEF1dGhTdGF0ZT4oe1xuICAgIGlzQXV0aGVudGljYXRlZDogZmFsc2UsXG4gICAgdXNlcjogbnVsbCxcbiAgICB0b2tlbjogbnVsbCxcbiAgfSk7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2NsaWVudCwgc2V0Q2xpZW50XSA9IHVzZVN0YXRlPHIyckNsaWVudCB8IG51bGw+KG51bGwpO1xuXG4gIC8vIEluaXRpYWxpemUgY2xpZW50IGFuZCBjaGVjayBmb3IgZXhpc3Rpbmcgc2Vzc2lvblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGluaXRpYWxpemVBdXRoID0gYXN5bmMgKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgZGVwbG95bWVudFVybCA9IGdldERlcGxveW1lbnRVcmwoKTtcbiAgICAgICAgY29uc3QgbmV3Q2xpZW50ID0gbmV3IHIyckNsaWVudChkZXBsb3ltZW50VXJsKTtcbiAgICAgICAgc2V0Q2xpZW50KG5ld0NsaWVudCk7XG5cbiAgICAgICAgLy8gQ2hlY2sgZm9yIHN0b3JlZCB0b2tlbiAodHJ5IGJvdGggcmNoYXQgYW5kIGNoYXRmcm9udGVuZCB0b2tlbiBrZXlzKVxuICAgICAgICBjb25zdCBzdG9yZWRUb2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdjaGF0QWNjZXNzVG9rZW4nKSB8fCBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgncjJyX3Rva2VuJyk7XG4gICAgICAgIGNvbnN0IHN0b3JlZFJlZnJlc2hUb2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdjaGF0UmVmcmVzaFRva2VuJyk7XG4gICAgICAgIGNvbnN0IHN0b3JlZFVzZXIgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgncjJyX3VzZXInKTtcblxuICAgICAgICBpZiAoc3RvcmVkVG9rZW4gJiYgc3RvcmVkVXNlcikge1xuICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zdCB1c2VyID0gSlNPTi5wYXJzZShzdG9yZWRVc2VyKTtcblxuICAgICAgICAgICAgLy8gU2V0IHVwIGNsaWVudCB3aXRoIHN0b3JlZCB0b2tlbnNcbiAgICAgICAgICAgIGlmIChzdG9yZWRSZWZyZXNoVG9rZW4pIHtcbiAgICAgICAgICAgICAgbmV3Q2xpZW50LnNldFRva2VucyhzdG9yZWRUb2tlbiwgc3RvcmVkUmVmcmVzaFRva2VuKTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgc2V0QXV0aFN0YXRlKHtcbiAgICAgICAgICAgICAgaXNBdXRoZW50aWNhdGVkOiB0cnVlLFxuICAgICAgICAgICAgICB1c2VyLFxuICAgICAgICAgICAgICB0b2tlbjogc3RvcmVkVG9rZW4sXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcGFyc2luZyBzdG9yZWQgdXNlciBkYXRhOicsIGVycm9yKTtcbiAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdyMnJfdG9rZW4nKTtcbiAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdyMnJfdXNlcicpO1xuICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2NoYXRBY2Nlc3NUb2tlbicpO1xuICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2NoYXRSZWZyZXNoVG9rZW4nKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluaXRpYWxpemluZyBhdXRoOicsIGVycm9yKTtcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIGluaXRpYWxpemVBdXRoKCk7XG4gIH0sIFtdKTtcblxuICBjb25zdCBsb2dpbiA9IGFzeW5jIChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nLCBkZXBsb3ltZW50VXJsPzogc3RyaW5nKTogUHJvbWlzZTxVc2VyPiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGFwaVVybCA9IGRlcGxveW1lbnRVcmwgfHwgZ2V0RGVwbG95bWVudFVybCgpO1xuICAgICAgY29uc3QgbG9naW5DbGllbnQgPSBuZXcgcjJyQ2xpZW50KGFwaVVybCk7XG5cbiAgICAgIGNvbnN0IHRva2VucyA9IGF3YWl0IGxvZ2luQ2xpZW50LnVzZXJzLmxvZ2luKHtcbiAgICAgICAgZW1haWw6IGVtYWlsLFxuICAgICAgICBwYXNzd29yZDogcGFzc3dvcmQsXG4gICAgICB9KTtcblxuICAgICAgaWYgKCF0b2tlbnMucmVzdWx0cykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0xvZ2luIGZhaWxlZDogTm8gcmVzdWx0cyByZXR1cm5lZCcpO1xuICAgICAgfVxuXG4gICAgICAvLyBTdG9yZSB0b2tlbnMgbGlrZSBjaGF0ZnJvbnRlbmQgZG9lc1xuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2NoYXRBY2Nlc3NUb2tlbicsIHRva2Vucy5yZXN1bHRzLmFjY2Vzc1Rva2VuLnRva2VuKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdjaGF0UmVmcmVzaFRva2VuJywgdG9rZW5zLnJlc3VsdHMucmVmcmVzaFRva2VuLnRva2VuKTtcblxuICAgICAgLy8gU2V0IHRva2VucyBvbiBjbGllbnRcbiAgICAgIGxvZ2luQ2xpZW50LnNldFRva2VucyhcbiAgICAgICAgdG9rZW5zLnJlc3VsdHMuYWNjZXNzVG9rZW4udG9rZW4sXG4gICAgICAgIHRva2Vucy5yZXN1bHRzLnJlZnJlc2hUb2tlbi50b2tlbixcbiAgICAgICk7XG5cbiAgICAgIHNldENsaWVudChsb2dpbkNsaWVudCk7XG5cbiAgICAgIC8vIEdldCB1c2VyIGluZm9cbiAgICAgIGNvbnN0IHVzZXJJbmZvID0gYXdhaXQgbG9naW5DbGllbnQudXNlcnMubWUoKTtcblxuICAgICAgaWYgKCF1c2VySW5mby5yZXN1bHRzKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGdldCB1c2VyIGluZm9ybWF0aW9uJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIENoZWNrIHVzZXIgcm9sZSBsaWtlIGNoYXRmcm9udGVuZCBkb2VzXG4gICAgICBsZXQgdXNlclJvbGU6IFwiYWRtaW5cIiB8IFwidXNlclwiID0gXCJ1c2VyXCI7XG4gICAgICB0cnkge1xuICAgICAgICBhd2FpdCBsb2dpbkNsaWVudC5zeXN0ZW0uc2V0dGluZ3MoKTtcbiAgICAgICAgdXNlclJvbGUgPSBcImFkbWluXCI7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBFcnJvciAmJiAnc3RhdHVzJyBpbiBlcnJvciAmJiAoZXJyb3IgYXMgYW55KS5zdGF0dXMgPT09IDQwMykge1xuICAgICAgICAgIC8vIFVzZXIgZG9lc24ndCBoYXZlIGFkbWluIGFjY2Vzcywga2VlcCBhcyBcInVzZXJcIlxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJVbmV4cGVjdGVkIGVycm9yIHdoZW4gY2hlY2tpbmcgdXNlciByb2xlOlwiLCBlcnJvcik7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgY29uc3QgdXNlcjogVXNlciA9IHtcbiAgICAgICAgaWQ6IHVzZXJJbmZvLnJlc3VsdHMuaWQsXG4gICAgICAgIGVtYWlsOiB1c2VySW5mby5yZXN1bHRzLmVtYWlsIHx8IGVtYWlsLFxuICAgICAgICBuYW1lOiB1c2VySW5mby5yZXN1bHRzLm5hbWUsXG4gICAgICAgIHJvbGU6IHVzZXJSb2xlLFxuICAgICAgfTtcblxuICAgICAgLy8gU3RvcmUgYXV0aCBkYXRhXG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgncjJyX3Rva2VuJywgdG9rZW5zLnJlc3VsdHMuYWNjZXNzVG9rZW4udG9rZW4pO1xuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3Iycl91c2VyJywgSlNPTi5zdHJpbmdpZnkodXNlcikpO1xuXG4gICAgICAvLyBVcGRhdGUgc3RhdGVcbiAgICAgIHNldEF1dGhTdGF0ZSh7XG4gICAgICAgIGlzQXV0aGVudGljYXRlZDogdHJ1ZSxcbiAgICAgICAgdXNlcixcbiAgICAgICAgdG9rZW46IHRva2Vucy5yZXN1bHRzLmFjY2Vzc1Rva2VuLnRva2VuLFxuICAgICAgfSk7XG5cbiAgICAgIHJldHVybiB1c2VyO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdMb2dpbiBlcnJvcjonLCBlcnJvcik7XG5cbiAgICAgIC8vIFByb3ZpZGUgbW9yZSBkZXRhaWxlZCBlcnJvciBpbmZvcm1hdGlvblxuICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgRXJyb3IpIHtcbiAgICAgICAgaWYgKGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJzQwMScpIHx8IGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ1VuYXV0aG9yaXplZCcpKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdJbnZhbGlkIGVtYWlsIG9yIHBhc3N3b3JkLiBQbGVhc2UgY2hlY2sgeW91ciBjcmVkZW50aWFscy4nKTtcbiAgICAgICAgfSBlbHNlIGlmIChlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCc0MDQnKSB8fCBlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCdOb3QgRm91bmQnKSkge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignVXNlciBub3QgZm91bmQuIFBsZWFzZSBjaGVjayB5b3VyIGVtYWlsIGFkZHJlc3MuJyk7XG4gICAgICAgIH0gZWxzZSBpZiAoZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnNTAwJykgfHwgZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnSW50ZXJuYWwgU2VydmVyIEVycm9yJykpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1NlcnZlciBlcnJvci4gUGxlYXNlIHRyeSBhZ2FpbiBsYXRlci4nKTtcbiAgICAgICAgfSBlbHNlIGlmIChlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCdOZXR3b3JrIEVycm9yJykgfHwgZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnZmV0Y2gnKSkge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignQ2Fubm90IGNvbm5lY3QgdG8gc2VydmVyLiBQbGVhc2UgY2hlY2sgaWYgUjJSIGJhY2tlbmQgaXMgcnVubmluZy4nKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgbG9nb3V0ID0gKCkgPT4ge1xuICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdyMnJfdG9rZW4nKTtcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgncjJyX3VzZXInKTtcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnY2hhdEFjY2Vzc1Rva2VuJyk7XG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2NoYXRSZWZyZXNoVG9rZW4nKTtcbiAgICBzZXRBdXRoU3RhdGUoe1xuICAgICAgaXNBdXRoZW50aWNhdGVkOiBmYWxzZSxcbiAgICAgIHVzZXI6IG51bGwsXG4gICAgICB0b2tlbjogbnVsbCxcbiAgICB9KTtcbiAgICBzZXRDbGllbnQobnVsbCk7XG4gIH07XG5cbiAgY29uc3QgZ2V0Q2xpZW50ID0gYXN5bmMgKCk6IFByb21pc2U8cjJyQ2xpZW50IHwgbnVsbD4gPT4ge1xuICAgIGlmICghYXV0aFN0YXRlLmlzQXV0aGVudGljYXRlZCB8fCAhYXV0aFN0YXRlLnRva2VuKSB7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG5cbiAgICBpZiAoIWNsaWVudCkge1xuICAgICAgY29uc3QgZGVwbG95bWVudFVybCA9IGdldERlcGxveW1lbnRVcmwoKTtcbiAgICAgIGNvbnN0IG5ld0NsaWVudCA9IG5ldyByMnJDbGllbnQoZGVwbG95bWVudFVybCk7XG5cbiAgICAgIC8vIFNldCB0b2tlbnMgaWYgYXZhaWxhYmxlXG4gICAgICBjb25zdCBhY2Nlc3NUb2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdjaGF0QWNjZXNzVG9rZW4nKSB8fCBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgncjJyX3Rva2VuJyk7XG4gICAgICBjb25zdCByZWZyZXNoVG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnY2hhdFJlZnJlc2hUb2tlbicpO1xuXG4gICAgICBpZiAoYWNjZXNzVG9rZW4pIHtcbiAgICAgICAgbmV3Q2xpZW50LnNldFRva2VucyhhY2Nlc3NUb2tlbiwgcmVmcmVzaFRva2VuIHx8ICcnKTtcbiAgICAgIH1cblxuICAgICAgc2V0Q2xpZW50KG5ld0NsaWVudCk7XG4gICAgICByZXR1cm4gbmV3Q2xpZW50O1xuICAgIH1cblxuICAgIHJldHVybiBjbGllbnQ7XG4gIH07XG5cbiAgY29uc3QgdmFsdWU6IFVzZXJDb250ZXh0VHlwZSA9IHtcbiAgICBhdXRoU3RhdGUsXG4gICAgbG9naW4sXG4gICAgbG9nb3V0LFxuICAgIGdldENsaWVudCxcbiAgICBpc0xvYWRpbmcsXG4gIH07XG5cbiAgcmV0dXJuIDxVc2VyQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17dmFsdWV9PntjaGlsZHJlbn08L1VzZXJDb250ZXh0LlByb3ZpZGVyPjtcbn07XG5cbmV4cG9ydCBjb25zdCB1c2VVc2VyQ29udGV4dCA9ICgpOiBVc2VyQ29udGV4dFR5cGUgPT4ge1xuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChVc2VyQ29udGV4dCk7XG4gIGlmIChjb250ZXh0ID09PSB1bmRlZmluZWQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3VzZVVzZXJDb250ZXh0IG11c3QgYmUgdXNlZCB3aXRoaW4gYSBVc2VyUHJvdmlkZXInKTtcbiAgfVxuICByZXR1cm4gY29udGV4dDtcbn07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwicjJyQ2xpZW50IiwiZ2V0RGVwbG95bWVudFVybCIsIlVzZXJDb250ZXh0IiwidW5kZWZpbmVkIiwiVXNlclByb3ZpZGVyIiwiY2hpbGRyZW4iLCJhdXRoU3RhdGUiLCJzZXRBdXRoU3RhdGUiLCJpc0F1dGhlbnRpY2F0ZWQiLCJ1c2VyIiwidG9rZW4iLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJjbGllbnQiLCJzZXRDbGllbnQiLCJpbml0aWFsaXplQXV0aCIsImRlcGxveW1lbnRVcmwiLCJuZXdDbGllbnQiLCJzdG9yZWRUb2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJzdG9yZWRSZWZyZXNoVG9rZW4iLCJzdG9yZWRVc2VyIiwiSlNPTiIsInBhcnNlIiwic2V0VG9rZW5zIiwiZXJyb3IiLCJjb25zb2xlIiwicmVtb3ZlSXRlbSIsImxvZ2luIiwiZW1haWwiLCJwYXNzd29yZCIsImFwaVVybCIsImxvZ2luQ2xpZW50IiwidG9rZW5zIiwidXNlcnMiLCJyZXN1bHRzIiwiRXJyb3IiLCJzZXRJdGVtIiwiYWNjZXNzVG9rZW4iLCJyZWZyZXNoVG9rZW4iLCJ1c2VySW5mbyIsIm1lIiwidXNlclJvbGUiLCJzeXN0ZW0iLCJzZXR0aW5ncyIsInN0YXR1cyIsImlkIiwibmFtZSIsInJvbGUiLCJzdHJpbmdpZnkiLCJtZXNzYWdlIiwiaW5jbHVkZXMiLCJsb2dvdXQiLCJnZXRDbGllbnQiLCJ2YWx1ZSIsIlByb3ZpZGVyIiwidXNlVXNlckNvbnRleHQiLCJjb250ZXh0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/context/UserContext.tsx\n"));

/***/ })

});