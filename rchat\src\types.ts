/**
 * Type definitions for RChat application
 */

export interface User {
  id: string;
  email: string;
  name?: string;
  role?: string;
  profile_image_url?: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
}

export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface Conversation {
  id: string;
  name?: string;
  messages: Message[];
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface ChatConfig {
  server: ServerConfig;
  app: AppConfig;
  vectorSearch: VectorSearchConfig;
  hybridSearch: HybridSearchConfig;
  graphSearch: GraphSearchConfig;
  ragGeneration: RagGenerationConfig;
}

export interface ServerConfig {
  apiUrl: string;
  port?: number;
  useHttps?: boolean;
  apiVersion?: string;
  timeout?: number;
}

export interface AppConfig {
  appName: string;
  appDescription: string;
  version: string;
  defaultMode: 'rag' | 'rag_agent';
}

export interface VectorSearchConfig {
  enabled: boolean;
  searchLimit: number;
  searchFilters: string;
  indexMeasure: 'cosine_distance' | 'l2_distance' | 'max_inner_product';
  includeMetadatas: boolean;
  probes?: number;
  efSearch?: number;
}

export interface HybridSearchConfig {
  enabled: boolean;
  fullTextWeight?: number;
  semanticWeight?: number;
  fullTextLimit?: number;
  rrfK?: number;
}

export interface GraphSearchConfig {
  enabled: boolean;
  kgSearchLevel?: number | null;
  maxCommunityDescriptionLength: number;
  localSearchLimits: Record<string, number>;
  maxLlmQueries?: number;
}

export interface RagGenerationConfig {
  temperature: number;
  topP: number;
  topK: number;
  maxTokensToSample: number;
  kgTemperature: number;
  kgTopP: number;
  kgTopK: number;
  kgMaxTokensToSample: number;
}

// Runtime config for Docker deployments
declare global {
  interface Window {
    __RUNTIME_CONFIG__?: {
      NEXT_PUBLIC_R2R_DEPLOYMENT_URL?: string;
      NEXT_PUBLIC_R2R_DEFAULT_EMAIL?: string;
      NEXT_PUBLIC_R2R_DEFAULT_PASSWORD?: string;
    };
  }
}
