{"-1 for no limit, or a positive integer for a specific limit": "Чексиз учун -1 ёки маълум чегара учун мусбат бутун сон", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' ёки '-1' муддати тугамаслиги учун.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(ма<PERSON><PERSON><PERSON><PERSON><PERSON>, `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(ма<PERSON><PERSON><PERSON><PERSON><PERSON>, `sh webui.sh --api`)", "(latest)": "(охирги)", "(leave blank for to use commercial endpoint)": "(тижорий сўнгги нуқтадан фойдаланиш учун бўш қолдиринг)", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "{{COUNT}} та мавжуд воситалар", "{{COUNT}} hidden lines": "{{COUNT}} та яширин чизиқ", "{{COUNT}} Replies": "{{COUNT}} та жавоб", "{{user}}'s Chats": "{{user}} нинг чатлари", "{{webUIName}} Backend Required": "{{webUIName}} Баcкенд талаб қилинади", "*Prompt node ID(s) are required for image generation": "*Расм яратиш учун тезкор тугун идентификаторлари талаб қилинади", "A new version (v{{LATEST_VERSION}}) is now available.": "Энди янги версия (v{{LATEST_VERSION}}) мавжуд.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Вазифа модели чатлар ва веб-қидирув сўровлари учун сарлавҳаларни яратиш каби вазифаларни бажаришда ишлатилади", "a user": "фойдаланувчи", "About": "Ҳақида", "Accept autocomplete generation / Jump to prompt variable": "Автоматик тўлдиришни яратишни қабул қилинг / Ўзгарувчига ўтиш", "Access": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Access Control": "Кириш назорати", "Accessible to all users": "Барча фойдаланувчилар учун очиқ", "Account": "Ҳисоб", "Account Activation Pending": "Ҳисобни фаоллаштириш кутилмоқда", "Accurate information": "Аниқ маълумот", "Actions": "Ҳаракатлар", "Activate": "Фаол<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ш", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Чат кир<PERSON><PERSON><PERSON><PERSON> учун “/{{COMMAND}}” териб ушбу буйруқни фаоллаштиринг.", "Active Users": "Фаол фойдаланувчилар", "Add": "Қўшиш", "Add a model ID": "Модел идентификаторини қўшинг", "Add a short description about what this model does": "Ушбу модел нима қилиши ҳақида қисқача тавсиф қўшинг", "Add a tag": "Тег қўшинг", "Add Arena Model": "Арена моделини қўшинг", "Add Connection": "Уланиш қўшиш", "Add Content": "Контент қўшиш", "Add content here": "Бу ерга таркиб қўшинг", "Add Custom Parameter": "Махсус параметр қўшинг", "Add custom prompt": "Махсус таклиф қўшинг", "Add Files": "Файлларни қўшиш", "Add Group": "Гуруҳ қўшиш", "Add Memory": "Хотира қўшиш", "Add Model": "Модел қўшиш", "Add Reaction": "Реакция қўшинг", "Add Tag": "Тег қўшиш", "Add Tags": "Теглар қўшиш", "Add text content": "Матн таркибини қўшинг", "Add User": "Фойдаланувчи қўшиш", "Add User Group": "Фойдаланувчилар гуруҳини қўшиш", "Adjusting these settings will apply changes universally to all users.": "Ушбу созламаларни ўзгартириш барча фойдаланувчиларга универсал тарзда қўлланилади.", "admin": "админ", "Admin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Admin Panel": "Администратор панели", "Admin Settings": "Администратор созламалари", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Администраторлар ҳар доим барча воситалардан фойдаланишлари мумкин; фойдаланувчиларга иш жойида ҳар бир модел учун тайинланган воситалар керак бўлади.", "Advanced Parameters": "Кенгайтирилган параметрлар", "Advanced Params": "Кенгайтирилган параметрлар", "All": "Ҳаммаси", "All Documents": "Барча Ҳужжатлар", "All models deleted successfully": "Барча моделлар муваффақиятли ўчирилди", "Allow Call": "Қўнғироққа рухсат бериш", "Allow Chat Controls": "Чат бошқарувига рухсат беринг", "Allow Chat Delete": "Чатни ўчиришга рухсат беринг", "Allow Chat Deletion": "Чатни ўчиришга рухсат беринг", "Allow Chat Edit": "Чатни таҳрирлашга рухсат беринг", "Allow Chat Export": "Чат экспортига рухсат беринг", "Allow Chat Share": "Чат алм<PERSON><PERSON><PERSON><PERSON>га рухсат беринг", "Allow Chat System Prompt": "", "Allow File Upload": "Файл юклашга рухсат беринг", "Allow Multiple Models in Chat": "Чатда бир нечта моделларга рухсат беринг", "Allow non-local voices": "Маҳаллий бўлмаган овозларга рухсат беринг", "Allow Speech to Text": "Нутқдан матнга рухсат бериш", "Allow Temporary Chat": "Вақтинчалик суҳбатга рухсат беринг", "Allow Text to Speech": "Матнни нутққа айлантиришга рухсат беринг", "Allow User Location": "Фойдаланувчи жойлашувига рухсат бериш", "Allow Voice Interruption in Call": "Қўнғироқда овозли узилишга рухсат беринг", "Allowed Endpoints": "Рухсат этилган охирги нуқталар", "Allowed File Extensions": "Рухсат этилган файл кенгайтмалари", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "Юк<PERSON><PERSON><PERSON> учун рухсат берилган файл кенгайтмалари. Бир нечта кенгайтмаларни вергул билан ажратинг. Барча файл турлари учун бўш қолдиринг.", "Already have an account?": "Ҳисобингиз борми?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "Топ_п га муқобил ва сифат ва хилма-хиллик мувозанатини таъминлашга қаратилган. п параметри токеннинг кўриб чиқилишининг минимал эҳтимолини ифодалайди, бу токеннинг эҳтимолий эҳтимолига нисбатан. Мисол учун, п=0,05 ва энг эҳтимолли токен 0,9 эҳтимолга эга бўлса, қиймати 0,045 дан кам бўлган логитлар филтрланади.", "Always": "Ҳар доим", "Always Collapse Code Blocks": "Ҳар доим код блокларини йиғиш", "Always Expand Details": "Ҳар доим Тафсилотларни кенгайтиринг", "Always Play Notification Sound": "Ҳар доим билдиришнома овозини ижро этиш", "Amazing": "Ажой<PERSON>б", "an assistant": "ёрда<PERSON><PERSON>и", "Analyzed": "Таҳлил қилинган", "Analyzing...": "Таҳлил қилинмоқда...", "and": "ва", "and {{COUNT}} more": "ва яна {{COUNT}} та", "and create a new shared link.": "ва янги умумий ҳавола яратинг.", "Android": "Андроид", "API": "", "API Base URL": "Дастурий Илова Интерфейси(API) bazaviy URL manzili", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "Дасту<PERSON>ий Илова Интерфейси(API) калити", "API Key created.": "Дастурий Илова Интерфейси(API) калити яратилди.", "API Key Endpoint Restrictions": "Дастурий Илова Интерфейси(API) калитининг чекловлари", "API keys": "Дасту<PERSON>ий Илова Интерфейси(API) калитлари", "API Version": "Дастурий Илова Интерфейси(API) версияси", "Application DN": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>(DN)", "Application DN Password": "Ilova<PERSON> no<PERSON><PERSON>(DN) paroli", "applies to all users with the \"user\" role": "\"фойдаланувчи\" ролига эга барча фойдаланувчиларга тегишли", "April": "апрел", "Archive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Archive All Chats": "Барча суҳбатларни архивлаш", "Archived Chats": "Архи<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> чатлар", "archived-chat-export": "архивланган-чат-экспорт", "Are you sure you want to clear all memories? This action cannot be undone.": "Ҳақиқатан ҳам барча хотираларни тозаламоқчимисиз? Бу амални ортга қайтариб бўлмайди.", "Are you sure you want to delete this channel?": "Ҳақиқатан ҳам бу канални ўчириб ташламоқчимисиз?", "Are you sure you want to delete this message?": "Ҳақиқатан ҳам бу хабарни ўчириб ташламоқчимисиз?", "Are you sure you want to unarchive all archived chats?": "Ҳақиқатан ҳам барча архивланган чатларни архивдан чиқармоқчимисиз?", "Are you sure?": "Ишончингиз комилми?", "Arena Models": "Арена моделлари", "Artifacts": "Арте<PERSON>а<PERSON><PERSON><PERSON><PERSON>р", "Ask": "Сўранг", "Ask a question": "Савол беринг", "Assistant": "Ёрдамчи", "Attach file from knowledge": "Били<PERSON>дан файлни бириктиринг", "Attention to detail": "Тафсилотларга эътибор", "Attribute for Mail": "Почта учун атрибут", "Attribute for Username": "Фойдаланувчи номи учун атрибут", "Audio": "Аудио", "August": "август", "Auth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Authenticate": "Аутентификация қилиш", "Authentication": "Аутентификация", "Auto": "Автоматик", "Auto-Copy Response to Clipboard": "Жавобни вақтинчалик хотирага автоматик нусхалаш", "Auto-playback response": "Автоматик ижро жавоби", "Autocomplete Generation": "Автотўлдиришни яратиш", "Autocomplete Generation Input Max Length": "Автоматик тўлдириш ишлаб чиқариш киритиш максимал узунлиги", "Automatic1111": "Автоматик1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 Api Auth String", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 базавий манзил", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 базавий манзил талаб қилинади.", "Available list": "Мавжуд рўйхат", "Available Tools": "Ма<PERSON><PERSON><PERSON>д асбоблар", "available!": "мавжуд!", "Awful": "Даҳшатли", "Azure AI Speech": "Azure AI нутқи", "Azure Region": "Azure минтақаси", "Back": "Орқага", "Bad Response": "Ёмон жавоб", "Banners": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Base Model (From)": "А<PERSON><PERSON><PERSON><PERSON> модел (дан бошлаб)", "before": "олдин", "Being lazy": "Дангаса бўлиш", "Beta": "Бета", "Bing Search V7 Endpoint": "Bing Search V7 Endpoint", "Bing Search V7 Subscription Key": "Bing Search V7 Subscription Key", "Bocha Search API Key": "Bocha Search API Key", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "Чеклан<PERSON>ан жавоблар учун махсус токенларни кучайтириш ёки жазолаш. Йўналтирилган қийматлар -100 ва 100 (шу жумладан) оралиғида маҳкамланади. (Бирламчи: йўқ)", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "Доcлинг ОCР механизми ва тил(лар) кўрсатилиши ёки иккаласи ҳам бўш қолиши керак.", "Brave Search API Key": "Brave Search API Key", "By {{name}}": "Муаллиф: {{name}}", "Bypass Embedding and Retrieval": "Ўрнатиш ва қидиришни четлаб ўтиш", "Bypass Web Loader": "Веб юклагични четлаб ўтиш", "Calendar": "Календар", "Call": "Қўнғироқ қилинг", "Call feature is not supported when using Web STT engine": "Wеб СТТ механизмидан фойдаланилганда қўнғироқ функсияси қўллаб-қувватланмайди", "Camera": "Камера", "Cancel": "Бекор қилиш", "Capabilities": "Имкониятлар", "Capture": "Қўлга олиш", "Capture Audio": "Аудио <PERSON><PERSON><PERSON><PERSON> олиш", "Certificate Path": "Сертификат йўли", "Change Password": "Паролни ўзгартириш", "Channel Name": "Канал номи", "Channels": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Character": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "Автоматик тўлдиришни яратиш учун белгилар чегараси", "Chart new frontiers": "Янги чегараларни белгиланг", "Chat": "Чат", "Chat Background Image": "Чат фон расми", "Chat Bubble UI": "<PERSON><PERSON> B<PERSON>ble Интерфейси", "Chat Controls": "Чат бошқарувлари", "Chat direction": "Чат йўналиши", "Chat Overview": "Чатга умумий нуқтаи ҳисобланади", "Chat Permissions": "Чат рухсатномалари", "Chat Tags Auto-Generation": "Чат тегларини автоматик яратиш", "Chats": "Суҳбатлар", "Check Again": "Яна текширинг", "Check for updates": "Янг<PERSON>ла<PERSON><PERSON>шларни текширинг", "Checking for updates...": "Янгилан<PERSON><PERSON>лар текширилмоқда...", "Choose a model before saving...": "Сақлашдан олдин моделни танланг...", "Chunk Overlap": "Бўлакларнинг бир-бирига ўхшашлиги", "Chunk Size": "Бўлак ҳажми", "Ciphers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Citation": "Иқтибос", "Citations": "Иқтибослар", "Clear memory": "Хотирани тозалаш", "Clear Memory": "Хотирани тозалаш", "click here": "бу ерни босинг", "Click here for filter guides.": "Филтр қўлланмалари учун бу ерни босинг.", "Click here for help.": "Ёрдам учун шу ерни босинг.", "Click here to": "Бу ерга босинг", "Click here to download user import template file.": "Фойдаланувчи импорт шаблон файлини юклаб олиш учун шу ерни босинг.", "Click here to learn more about faster-whisper and see the available models.": "Тезроқ шивирлаш ҳақида кўпроқ маълумот олиш ва мавжуд моделларни кўриш учун шу ерни босинг.", "Click here to see available models.": "Мавжуд моделларни кўриш учун шу ерни босинг.", "Click here to select": "Та<PERSON><PERSON><PERSON><PERSON> учун шу ерни босинг", "Click here to select a csv file.": "cсв файлини танлаш учун шу ерни босинг.", "Click here to select a py file.": "пй файлини танлаш учун шу ерни босинг.", "Click here to upload a workflow.json file.": "Wоркфлоw.жсон файлини юклаш учун шу ерни босинг.", "click here.": "бу ерни босинг.", "Click on the user role button to change a user's role.": "Фойдаланувчи ролини ўзгартириш учун фойдаланувчи роли тугмасини босинг.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Буферга ёзиш рухсати рад этилди. Керакли рухсат бериш учун браузер созламаларини текширинг.", "Clone": "<PERSON>л<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clone Chat": "Чатни клонлаш", "Clone of {{TITLE}}": "{{TITLE}} клони", "Close": "Ёпиш", "Close modal": "", "Close settings modal": "", "Code execution": "Коднинг бажарилиши", "Code Execution": "Коднинг бажарилиши", "Code Execution Engine": "Кодни бажариш механизми", "Code Execution Timeout": "Кодни бажариш вақти тугаши", "Code formatted successfully": "Код муваффақиятли форматланди", "Code Interpreter": "Код таржимони", "Code Interpreter Engine": "Код таржимон механизми", "Code Interpreter Prompt Template": "Код таржимони сўрови шаблони", "Collapse": "Йиқилиш", "Collection": "Тўплам", "Color": "<PERSON><PERSON><PERSON><PERSON>", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI API Key", "ComfyUI Base URL": "ComfyUI базавий манзил", "ComfyUI Base URL is required.": "ComfyUI базавий манзил талаб қилинади.", "ComfyUI Workflow": "ComfyUI иш жараёни", "ComfyUI Workflow Nodes": "ComfyUI иш оқими тугунлари", "Command": "Буйруқ", "Completions": "Туга<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Concurrent Requests": "Бир вақтнинг ўзида сўровлар", "Configure": "Созланг", "Confirm": "Тасдиқланг", "Confirm Password": "Паролни тасдиқланг", "Confirm your action": "Ҳаракатингизни тасдиқланг", "Confirm your new password": "Янги паролингизни тасдиқланг", "Connect to your own OpenAI compatible API endpoints.": "Ўзингизнинг OpenAIга мос келадиган АПИ сўнгги нуқталарига уланинг.", "Connect to your own OpenAPI compatible external tool servers.": "Ўзингизнинг OpenAIга мос келадиган ташқи асбоблар серверларига уланинг.", "Connection failed": "Уланиш амалга ошмади", "Connection successful": "Уланиш муваффақиятли", "Connection Type": "Уланиш тури", "Connections": "Уланишлар", "Connections saved successfully": "Уланишлар муваффақиятли сақланди", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "Фикрлаш моделлари учун мулоҳаза юритиш бўйича ҳаракатларни чеклайди. Фақат фикрлаш ҳаракатларини қўллаб-қувватлайдиган махсус провайдерларнинг фикрлаш моделлари учун қўлланилади.", "Contact Admin for WebUI Access": "WebUIга кириш учун администратор билан боғланинг", "Content": "Таркиб", "Content Extraction Engine": "Контентни ажратиб олиш механизми", "Continue Response": "Жавоб беришни давом эттириш", "Continue with {{provider}}": "{{provider}} билан давом этинг", "Continue with Email": "Электрон почта орқали давом этинг", "Continue with LDAP": "LDAP билан давом этинг", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "ТТS сўровлари учун хабар матни қандай бўлинишини бошқаринг. \"Тиниш белгилари\" жумлаларга, \"параграфлар\" параграфларга бўлинади ва \"йўқ\" хабарни битта қатор сифатида сақлайди.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "Яратилган матндаги токенлар кетма-кетлигини такрорлашни назорат қилиш. Юқори қиймат (масалан, 1,5) такрорлаш учун қаттиқроқ жазоланади, пастроқ қиймат (мас<PERSON>лан, 1,1) эса юмшоқроқ бўлади. 1-да, у ўчирилган.", "Controls": "Бошқарув", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "Чиқаришнинг изчиллиги ва хилма-хиллиги ўртасидаги мувозанатни назорат қилади. Пастроқ қиймат кўпроқ диққат марказида ва изчил матнга олиб келади.", "Copied": "Кўчирилди", "Copied link to clipboard": "Буферга ҳавола нусхаланди", "Copied shared chat URL to clipboard!": "Умумий чат URL манзили вақтинчалик хотирага нусхаланди!", "Copied to clipboard": "Буферга нусхаланди", "Copy": "Нусхалаш", "Copy Formatted Text": "Форматланган матнни нусхалаш", "Copy last code block": "Охирги код блокидан нусха олинг", "Copy last response": "Охирги жавобдан нусха олинг", "Copy Link": "Ҳаволани нусхалаш", "Copy to clipboard": "Буферга нусхалаш", "Copying to clipboard was successful!": "Буферга нусхалаш муваффақиятли бўлди!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "Open WebUI сўровларига рухсат бериш учун CORS провайдер томонидан тўғри созланган бўлиши керак.", "Create": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Create a knowledge base": "Били<PERSON><PERSON>ар базасини яратинг", "Create a model": "Модел яратиш", "Create Account": "Ҳисоб яратиш", "Create Admin Account": "Администратор ҳисобини яратинг", "Create Channel": "Канал яра<PERSON><PERSON>ш", "Create Group": "Гуруҳ яратиш", "Create Knowledge": "<PERSON>илим яратиш", "Create new key": "Ян<PERSON>и калит яратинг", "Create new secret key": "Янги мах<PERSON>ий калит яратинг", "Create Note": "Эслатма яратиш", "Create your first note by clicking on the plus button below.": "Қуйидаги ортиқча тугмасини босиш орқали биринчи қайдингизни яратинг.", "Created at": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Created At": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Created by": "томонидан яратилган", "CSV Import": "CSV импорти", "Ctrl+Enter to Send": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> учун Ctrl+Enter", "Current Model": "<PERSON><PERSON><PERSON><PERSON>", "Current Password": "<PERSON><PERSON><PERSON><PERSON> парол", "Custom": "Ма<PERSON><PERSON><PERSON><PERSON>", "Custom Parameter Name": "Махсус параметр номи", "Custom Parameter Value": "Махсус параметр қиймати", "Danger Zone": "Хавфли зона", "Dark": "Қоронғи", "Database": "Маълумотлар базаси", "Datalab Marker API": "Datalab Marker API", "Datalab Marker API Key required.": "Datalab Marker API Key талаб қилинади.", "December": "де<PERSON><PERSON><PERSON><PERSON>", "Default": "Стандарт", "Default (Open AI)": "Ста<PERSON><PERSON><PERSON><PERSON><PERSON> (Open AI)", "Default (SentenceTransformers)": "Станда<PERSON><PERSON> (SentenceTransformers)", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "Стандарт модел", "Default model updated": "Стандарт модел янгиланди", "Default Models": "Стандарт моделлар", "Default permissions": "Бирламчи рухсатлар", "Default permissions updated successfully": "Бир<PERSON>амчи рухсатлар муваффақиятли янгиланди", "Default Prompt Suggestions": "Стандарт таклифлар", "Default to 389 or 636 if TLS is enabled": "<PERSON>гар TLS ёқилган бўлса, сукут бўйича 389 ёки 636", "Default to ALL": "Барча<PERSON>и учун бирламчи", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "Фокусланган ва тегишли контентни ажратиб олиш учун бирламчи сегментланган қидириш, бу кўп ҳолларда тавсия этилади.", "Default User Role": "Стандарт фойдаланувчи роли", "Delete": "Ўчириш", "Delete a model": "Моделни ўчириш", "Delete All Chats": "Барча суҳбатларни ўчириш", "Delete All Models": "Барча моделларни ўчириш", "Delete chat": "Чатни ўчириш", "Delete Chat": "Чатни ўчириш", "Delete chat?": "Чат ўчирилсинми?", "Delete folder?": "Жилд ўчирилсинми?", "Delete function?": "Функция ўчирилсинми?", "Delete Message": "Хабарни ўчириш", "Delete message?": "Хабар ўчирилсинми?", "Delete note?": "Қайд ўчирилсинми?", "Delete prompt?": "Сўров ўчирилсинми?", "delete this link": "ушбу ҳаволани ўчиринг", "Delete tool?": "Асбоб ўчирилсинми?", "Delete User": "Фойдаланувчини ўчириш", "Deleted {{deleteModelTag}}": "Ўчирилди {{deleteModelTag}}", "Deleted {{name}}": "{{name}} ўчирилди", "Deleted User": "Ўчирилган фойдаланувчи", "Deployment names are required for Azure OpenAI": "Azure OpenAI учун тарқатиш номлари талаб қилинади", "Describe Pictures in Documents": "Ҳужжатлардаги расмларга тавсиф беринг", "Describe your knowledge base and objectives": "Билим базаси ва мақсадларингизни тавсифланг", "Description": "Тавсиф", "Detect Artifacts Automatically": "Артефактларни автоматик аниқлаш", "Dictate": "Диктация қилиш", "Didn't fully follow instructions": "Кўрсатмаларга тўлиқ амал қилмади", "Direct": "Тўғридан-тўғри", "Direct Connections": "Тўғридан-тўғри уланишлар", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "Тўғридан-тўғри уланишлар фойдаланувчиларга ўзларининг OpenAIга мос келувчи API сўнгги нуқталарига уланиш имконини беради.", "Direct Connections settings updated": "Тўғридан-тўғри уланиш созламалари янгиланди", "Direct Tool Servers": "Тўғридан-тўғри асбоблар серверлари", "Disable Image Extraction": "Расм чиқаришни ўчириб қўйинг", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "PDFдан тасвирни ажратиб олишни ўчириб қўйинг. Агар LLM дан фойдаланиш ёқилган бўлса, тасвирларга автоматик сарлавҳа қўйилади. Бирламчи параметрлар False.", "Disabled": "Ўчирилган", "Discover a function": "Функцияни кашф қилиш", "Discover a model": "Моделни кашф қилинг", "Discover a prompt": "Кўрсатмани кашф қилинг", "Discover a tool": "Асбобни кашф қилинг", "Discover how to use Open WebUI and seek support from the community.": "Open WebUIдан қандай фойдаланишни билиб олинг ва ҳамжамиятдан ёрдам сўранг.", "Discover wonders": "Мўъжизаларни кашф этинг", "Discover, download, and explore custom functions": "Махсус функцияларни кашф этинг, юклаб олинг ва ўрганинг", "Discover, download, and explore custom prompts": "Махсус таклифларни кашф қилинг, юклаб олинг ва ўрганинг", "Discover, download, and explore custom tools": "Махсус воситаларни кашф қилинг, юклаб олинг ва ўрганинг", "Discover, download, and explore model presets": "Модел созламаларини кашф этинг, юклаб олинг ва ўрганинг", "Dismissible": "Рад этиш мумкин", "Display": "Дис<PERSON><PERSON>ей", "Display Emoji in Call": "Чақирувда кулгичларни кўрсатиш", "Display the username instead of You in the Chat": "Чатда Сиз ўрнига фойдаланувчи номини кўрсатинг", "Displays citations in the response": "Жавобда иқтибосларни кўрсатади", "Dive into knowledge": "Билимга шўнғинг", "Do not install functions from sources you do not fully trust.": "Тўлиқ ишонмайдиган манбалардан функсияларни ўрнатманг.", "Do not install tools from sources you do not fully trust.": "Ўзингиз ишонмайдиган манбалардан асбобларни ўрнатманг.", "Docling": "Доклинг", "Docling Server URL required.": "До<PERSON><PERSON><PERSON>нг Сервер URL манзили талаб қилинади.", "Document": "Ҳужжат", "Document Intelligence": "Ҳужжат разведкаси", "Document Intelligence endpoint and key required.": "Доcумент Интеллигенcе сўнгги нуқтаси ва калит талаб қилинади.", "Documentation": "Ҳужжатлар", "Documents": "Ҳужжатлар", "does not make any external connections, and your data stays securely on your locally hosted server.": "ҳеч қандай ташқи уланишларни амалга оширмайди ва сизнинг маълумотларингиз маҳаллий серверингизда хавфсиз сақланади.", "Domain Filter List": "Домен филтрлари рўйхати", "Don't have an account?": "Ҳисобингиз йўқми?", "don't install random functions from sources you don't trust.": "ўзингиз ишонмайдиган манбалардан тасодифий функсияларни ўрнатманг.", "don't install random tools from sources you don't trust.": "ўзингиз ишонмайдиган манбалардан тасодифий воситаларни ўрнатманг.", "Don't like the style": "Услуб ёқмайди", "Done": "Бажарилди", "Download": "<PERSON><PERSON><PERSON><PERSON><PERSON> олиш", "Download as SVG": "СВГ сифатида юклаб олинг", "Download canceled": "Юклаб олиш бекор қилинди", "Download Database": "Маълумотлар базасини юклаб олиш", "Drag and drop a file to upload or select a file to view": "Юк<PERSON><PERSON><PERSON> учун файлни судраб ташланг ёки кўриш учун файлни танланг", "Draw": "<PERSON>из<PERSON><PERSON>", "Drop any files here to upload": "<PERSON><PERSON><PERSON><PERSON><PERSON> учун исталган файлни шу ерга ташланг", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "масалан. 30s, 10m. Яроқли вақт бирликлари 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "маса<PERSON>а<PERSON>. \"json\" ёки JSON схемаси", "e.g. 60": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 60", "e.g. A filter to remove profanity from text": "ма<PERSON><PERSON><PERSON><PERSON><PERSON>, Матндан ҳақоратли сўзларни олиб ташлаш учун филтр", "e.g. en": "маса<PERSON>а<PERSON>, en", "e.g. My Filter": "ма<PERSON><PERSON><PERSON><PERSON><PERSON>, Менинг филтрим", "e.g. My Tools": "ма<PERSON><PERSON><PERSON><PERSON><PERSON>, Менинг асбобларим", "e.g. my_filter": "ма<PERSON><PERSON><PERSON><PERSON><PERSON>, my_filter", "e.g. my_tools": "маса<PERSON>а<PERSON>, my_tools", "e.g. pdf, docx, txt": "ма<PERSON><PERSON><PERSON><PERSON><PERSON>, pdf, docx, txt", "e.g. Tools for performing various operations": "масалан. Ҳар хил операцияларни бажариш учун асбоблар", "e.g., 3, 4, 5 (leave blank for default)": "ма<PERSON><PERSON><PERSON><PERSON><PERSON>, 3, 4, 5 (сукут бўйича бўш қолдиринг)", "e.g., audio/wav,audio/mpeg (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "ма<PERSON><PERSON><PERSON><PERSON><PERSON>, en-US,ja-JP (автоматик аниқлаш учун бўш қолдиринг)", "e.g., westus (leave blank for eastus)": "масалан, westus (eastus учун бўш қолдиринг)", "e.g.) en,fr,de": "масалан) en,fr,de", "Edit": "Таҳрирлаш", "Edit Arena Model": "Арена моделини таҳрирлаш", "Edit Channel": "Канални таҳрирлаш", "Edit Connection": "Уланишни таҳрирлаш", "Edit Default Permissions": "Стандарт рухсатларни таҳрирлаш", "Edit Memory": "Хотирани таҳрирлаш", "Edit User": "Фойдаланувчини таҳрирлаш", "Edit User Group": "Фойдаланувчилар гуруҳини таҳрирлаш", "Eject": "Чиқариш", "ElevenLabs": "ЭлевенЛабс", "Email": "Электрон почта", "Embark on adventures": "Саргузаштларга киришинг", "Embedding": "Ўрнатиш", "Embedding Batch Size": "Ўрнатиш тўплами ҳажми", "Embedding Model": "Ўрнатиш модели", "Embedding Model Engine": "Двигател моделини ўрнатиш", "Embedding model set to \"{{embedding_model}}\"": "Ўрнатиш модели “{{embedding_model}}”га ўрнатилди", "Enable API Key": "API калитини ёқиш", "Enable autocomplete generation for chat messages": "<PERSON>ат хаба<PERSON><PERSON><PERSON><PERSON><PERSON> учун автоматик тўлдиришни яратишни ёқинг", "Enable Code Execution": "Код бажари<PERSON>ишини ёқинг", "Enable Code Interpreter": "Код таржимонини ёқинг", "Enable Community Sharing": "Ҳамжамият билан алмашишни ёқинг", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Модел маълумотларини РАМдан алмаштиришнинг олдини олиш учун Хотирани қулфлашни (mlock) ёқинг. Ушбу параметр моделнинг ишлайдиган саҳифалар тўпламини РАМга блоклайди ва улар дискка алмаштирилмаслигини таъминлайди. Бу саҳифа хатоларидан қочиш ва маълумотларга тезкор киришни таъминлаш орқали ишлашни сақлашга ёрдам беради.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Модел маълумотларини юклаш учун Хотира харитасини (mmap) ёқинг. Ушбу параметр тизимга диск файлларини оператив хотирада бўлганидек даволаш орқали РАМ кенгайтмаси сифатида диск хотирасидан фойдаланиш имконини беради. Бу маълумотларга тезроқ кириш имконини бериш орқали модел иш фаолиятини яхшилаши мумкин. Бироқ, у барча тизимлар билан тўғри ишламаслиги ва катта ҳажмдаги диск майдонини истеъмол қилиши мумкин.", "Enable Message Rating": "<PERSON>а<PERSON><PERSON>р рейтингини ёқиш", "Enable Mirostat sampling for controlling perplexity.": "Ажабланишни назорат қилиш учун Миростат намунасини ёқинг.", "Enable New Sign Ups": "Янги рўйхатдан ўтишни ёқинг", "Enabled": "Ёқилган", "Endpoint URL": "Охирги нуқта URL", "Enforce Temporary Chat": "Вақтинчалик суҳбатни жорий қилиш", "Enhance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "CSV файлингиз қуйидаги тартибда 4 та устундан иборатлигига ишонч ҳосил қилинг: Исм, Эл<PERSON>к<PERSON>рон почта, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>.", "Enter {{role}} message here": "Бу ерга {{role}} хабарини киритинг", "Enter a detail about yourself for your LLMs to recall": "ЛЛМлар эслаб қолишлари учун ўзингиз ҳақингизда маълумот киритинг", "Enter a title for the pending user info overlay. Leave empty for default.": "Кутил<PERSON><PERSON>тган фойдаланувчи маълумотлари учун сарлавҳа киритинг. Сукут бўйича бўш қолдиринг.", "Enter a watermark for the response. Leave empty for none.": "Жавоб учун мойбўёқли белгини киритинг. Ҳеч ким учун бўш қолдиринг.", "Enter api auth string (e.g. username:password)": "API auth сатрини киритинг (ма<PERSON><PERSON><PERSON><PERSON><PERSON>, фойдаланувчи номи: парол)", "Enter Application DN": "Илова DN ни киритинг", "Enter Application DN Password": "Илова DN паролини киритинг", "Enter Bing Search V7 Endpoint": "Bing Search V7 охирги нуқтасини киритинг", "Enter Bing Search V7 Subscription Key": "Bing Search V7 обуна калитини киритинг", "Enter BM25 Weight": "БМ25 вазнини киритинг", "Enter Bocha Search API Key": "Bocha Search API Key калитини киритинг", "Enter Brave Search API Key": "Brave Search API Key калитини киритинг", "Enter certificate path": "Сертификат йўлини киритинг", "Enter CFG Scale (e.g. 7.0)": "CFG шкаласини киритинг (мас<PERSON><PERSON><PERSON><PERSON>, 7.0)", "Enter Chunk Overlap": "Chunk Overlap киритинг", "Enter Chunk Size": "Chunk ҳажмини киритинг", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "Вергул билан ажратилган \"token:bias_value\" жуфтларини киритинг (мисол: 5432:100, 413:-100)", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "Кути<PERSON><PERSON><PERSON><PERSON>ган фойдаланувчи маълумотлари қопламаси учун таркибни киритинг. Сукут бўйича бўш қолдиринг.", "Enter Datalab Marker API Key": "Datalab Marker API Key калитини киритинг", "Enter description": "Тавсифни киритинг", "Enter Docling OCR Engine": "Docling OCR механизмини киритинг", "Enter Docling OCR Language(s)": "Docling OCR тил(лар)ини киритинг", "Enter Docling Server URL": "Docling Сервер URL манзилини киритинг", "Enter Document Intelligence Endpoint": "Document Intelligence Эндпоинт-ни киритинг", "Enter Document Intelligence Key": "Ҳужжатнинг разведка калитини киритинг", "Enter domains separated by commas (e.g., example.com,site.org)": "Доменларни вергул билан ажратинг (масалан, example.com,site.org)", "Enter Exa API Key": "Exa АПИ калитини киритинг", "Enter External Document Loader API Key": "Ташқи ҳужжат юкловчи АПИ калитини киритинг", "Enter External Document Loader URL": "Ташқи ҳужжат юкловчи УРЛ манзилини киритинг", "Enter External Web Loader API Key": "Ташқи Wеб Лоадер АПИ калитини киритинг", "Enter External Web Loader URL": "Ташқи Wеб Лоадер УРЛ манзилини киритинг", "Enter External Web Search API Key": "Ташқи веб-қидирув АПИ калитини киритинг", "Enter External Web Search URL": "Ташқи веб-қидирув УРЛ манзилини киритинг", "Enter Firecrawl API Base URL": "Firecrawl АПИ базаси УРЛ манзилини киритинг", "Enter Firecrawl API Key": "Firecrawl АПИ калитини киритинг", "Enter Github Raw URL": "Github Raw УРЛ манзилини киритинг", "Enter Google PSE API Key": "Google PSE АПИ калитини киритинг", "Enter Google PSE Engine Id": "Google PSE Энгине идентификаторини киритинг", "Enter Image Size (e.g. 512x512)": "Расм ҳажмини киритинг (масалан, 512х512)", "Enter Jina API Key": "Jina АПИ калитини киритинг", "Enter Jupyter Password": "Jupiter паролини киритинг", "Enter Jupyter Token": "Jupiter токенини киритинг", "Enter Jupyter URL": "Jupiter УРЛ манзилини киритинг", "Enter Kagi Search API Key": "Kagi Search АПИ калитини киритинг", "Enter Key Behavior": "А<PERSON><PERSON><PERSON><PERSON> хатти-ҳаракатни киритинг", "Enter language codes": "<PERSON>ил код<PERSON>а<PERSON>ини киритинг", "Enter Mistral API Key": "Mistral АПИ калитини киритинг", "Enter Model ID": "Модел идентификаторини киритинг", "Enter model tag (e.g. {{modelTag}})": "Модел тегини киритинг (ма<PERSON><PERSON><PERSON><PERSON><PERSON>, {{моделТаг}})", "Enter Mojeek Search API Key": "Mojeek Search АПИ калитини киритинг", "Enter name": "Исмни киритинг", "Enter New Password": "Янги паролни киритинг", "Enter Number of Steps (e.g. 50)": "Қадамлар сонини киритинг (ма<PERSON><PERSON><PERSON><PERSON><PERSON>, 50)", "Enter Perplexity API Key": "Perplexity АПИ калитини киритинг", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "Perplexity WebSocket УРЛ манзилини киритинг", "Enter proxy URL (e.g. **************************:port)": "Прокси-сервернинг УРЛ манзилини киритинг (масала<PERSON>, ҳттпс://усер:пассwорд@ҳост:порт)", "Enter reasoning effort": "Фик<PERSON><PERSON><PERSON><PERSON> ҳаракатини киритинг", "Enter Sampler (e.g. Euler a)": "Самплерни киритинг (мас<PERSON>лан, Euler a)", "Enter Scheduler (e.g. Karras)": "Режалаштирувчини киритинг (масалан, Karras)", "Enter Score": "Бални киритинг", "Enter SearchApi API Key": "SearchApi АПИ калитини киритинг", "Enter SearchApi Engine": "SearchApi тизимига киринг", "Enter Searxng Query URL": "Searxng сўрови УРЛ манзилини киритинг", "Enter Seed": "Seed<PERSON>а киринг", "Enter SerpApi API Key": "SerpApi АПИ калитини киритинг", "Enter SerpApi Engine": "SerpApi двигателига киринг", "Enter Serper API Key": "Serper АПИ калитини киритинг", "Enter Serply API Key": "Serply АПИ калитини киритинг", "Enter Serpstack API Key": "Serpstack АПИ калитини киритинг", "Enter server host": "Сервер хостига киринг", "Enter server label": "Сервер ёрлиғини киритинг", "Enter server port": "Сервер портини киритинг", "Enter Sougou Search API sID": "<PERSON>ugou Сеарч АПИ сИД ни киритинг", "Enter Sougou Search API SK": "Sougou Сеарч АПИ СК ни киритинг", "Enter stop sequence": "Тўхташ кетма-кетлигини киритинг", "Enter system prompt": "Тизим сўровини киритинг", "Enter system prompt here": "Бу эрда тизим сўровини киритинг", "Enter Tavily API Key": "Тавилй АПИ калитини киритинг", "Enter Tavily Extract Depth": "Тавилй экстракти чуқурлигини киритинг", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "WебУИ нинг умумий УРЛ манзилини киритинг. Бу УРЛ билдиришномаларда ҳаволалар яратиш учун ишлатилади.", "Enter the URL of the function to import": "Импорт қилинадиган функсиянинг УРЛ манзилини киритинг", "Enter the URL to import": "Импорт қилиш учун УРЛ манзилини киритинг", "Enter Tika Server URL": "Тика Сервер УРЛ манзилини киритинг", "Enter timeout in seconds": "Вақт тугашини сонияларда киритинг", "Enter to Send": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> учун киринг", "Enter Top K": "Топ К.га киринг", "Enter Top K Reranker": "Топ К Реранкер-га киринг", "Enter URL (e.g. http://127.0.0.1:7860/)": "УРЛ манзилини киритинг (масалан, ҳттп://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "УРЛ манзилини киритинг (масалан, ҳттп://лоcалҳост:11434)", "Enter Yacy Password": "<PERSON><PERSON> паролини киритинг", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "Yacy УРЛ манзилини киритинг (мас<PERSON><PERSON><PERSON><PERSON>, ҳттп://яcй.эхампле.cом:8090)", "Enter Yacy Username": "<PERSON><PERSON> фойдаланувчи номини киритинг", "Enter your current password": "<PERSON><PERSON><PERSON><PERSON> паролингизни киритинг", "Enter Your Email": "Электрон почтангизни киритинг", "Enter Your Full Name": "Тўлиқ исмингизни киритинг", "Enter your message": "Хабарингизни киритинг", "Enter your name": "Исмингизни киритинг", "Enter Your Name": "Исмингизни киритинг", "Enter your new password": "Янги паролингизни киритинг", "Enter Your Password": "Паролингизни киритинг", "Enter Your Role": "Ролингизни киритинг", "Enter Your Username": "Фойдаланувчи номингизни киритинг", "Enter your webhook URL": "Вебҳук УРЛ манзилингизни киритинг", "Error": "<PERSON>ато", "ERROR": "ХАТО", "Error accessing Google Drive: {{error}}": "Гоогле Дриве-га киришда хатолик юз берди: {{error}}", "Error accessing media devices.": "Медиа қурилмаларига киришда хатолик юз берди.", "Error starting recording.": "Ёзишни бошлашда хатолик юз берди.", "Error unloading model: {{error}}": "Моделни юклашда хатолик юз берди: {{error}}", "Error uploading file: {{error}}": "Файлни юклашда хатолик юз берди: {{error}}", "Evaluations": "Баҳолар", "Exa API Key": "Exa АПИ калити", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Мисол: (&(обжеcтCласс=инетОргПерсон)(уид=%с))", "Example: ALL": "Мисол: АЛЛ", "Example: mail": "Мисол: почта", "Example: ou=users,dc=foo,dc=example": "Мисол: оу=усерс,дc=фоо,дc=мисол", "Example: sAMAccountName or uid or userPrincipalName": "Мисол: сАМАccоунтНаме ёки уид ёки усерПринcипалНаме", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "Литсензиянгиздаги ўринлар сонидан ошиб кетди. Ўриндиқлар сонини кўпайтириш учун қўллаб-қувватлаш хизматига мурожаат қилинг.", "Exclude": "<PERSON>е<PERSON><PERSON><PERSON><PERSON>", "Execute code for analysis": "Таҳлил қилиш учун кодни бажаринг", "Executing **{{NAME}}**...": "**{{NAME}}** бажарилмоқда...", "Expand": "К<PERSON>н<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Experimental": "Экспериментал", "Explain": "Ту<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Explore the cosmos": "Космосни ўрганинг", "Export": "Экспорт", "Export All Archived Chats": "Барча архивланган суҳбатларни экспорт қилиш", "Export All Chats (All Users)": "Барча суҳбатларни экспорт қилиш (барча фойдаланувчилар)", "Export chat (.json)": "Чатни экспорт қилиш (.жсон)", "Export Chats": "Чатларни экспорт қилиш", "Export Config to JSON File": "Конфигурацияни ЖСОН файлига экспорт қилинг", "Export Functions": "Экспорт функциялари", "Export Models": "Экспорт моделлари", "Export Presets": "Олдиндан созламаларни экспорт қилиш", "Export Prompt Suggestions": "Экспорт бўйича таклифлар", "Export Prompts": "Экспорт таклифлари", "Export to CSV": "CSV га экспорт қилиш", "Export Tools": "Экспорт воситалари", "External": "Ташқи", "External Document Loader URL required.": "Ташқи ҳужжат юкловчи УРЛ манзили талаб қилинади.", "External Task Model": "Ташқи вазифа модели", "External Web Loader API Key": "Ташқи Wеб Лоадер АПИ калити", "External Web Loader URL": "Ташқи веб юкловчи УРЛ манзили", "External Web Search API Key": "Ташқи веб-қидирув АПИ калити", "External Web Search URL": "Ташқи веб-қидирув УРЛ манзили", "Failed to add file.": "Файл қўшиб бўлмади.", "Failed to connect to {{URL}} OpenAPI tool server": "{{УРЛ}} ОпенАПИ асбоб серверига уланиб бўлмади", "Failed to copy link": "Ҳаволани нусхалаб бўлмади", "Failed to create API Key.": "АПИ калитини яратиб бўлмади.", "Failed to delete note": "Қайдни ўчириб бўлмади", "Failed to fetch models": "Моделларни олиб бўлмади", "Failed to load file content.": "Файл таркибини юклаб бўлмади.", "Failed to read clipboard contents": "Буфер таркибини ўқиб бўлмади", "Failed to save connections": "Уланишлар сақланмади", "Failed to save models configuration": "Модел<PERSON>а<PERSON> конфигурацияси сақланмади", "Failed to update settings": "Созламаларни янгилаб бўлмади", "Failed to upload file.": "Файл юкланмади.", "Features": "Хусусиятлари", "Features Permissions": "Хусусия<PERSON><PERSON><PERSON><PERSON> Рух<PERSON>атлар", "February": "Феврал", "Feedback Details": "", "Feedback History": "Фикр-мулоҳаза тарихи", "Feedbacks": "Фикр-мулоҳазалар", "Feel free to add specific details": "Муа<PERSON>ян тафсилотларни қўшишингиз мумкин", "File": "<PERSON>а<PERSON><PERSON>", "File added successfully.": "Файл муваффақиятли қўшилди.", "File content updated successfully.": "Файл мазмуни муваффақиятли янгиланди.", "File Mode": "Файл режими", "File not found.": "Файл топилмади.", "File removed successfully.": "Файл муваффақиятли олиб ташланди.", "File size should not exceed {{maxSize}} MB.": "Файл ҳажми {{махСизе}} МБ дан ошмаслиги керак.", "File Upload": "Файл юклаш", "File uploaded successfully": "Файл муваффақиятли юкланди", "Files": "<PERSON>ай<PERSON><PERSON><PERSON>р", "Filter is now globally disabled": "Филтр энди бутун дунё бўйлаб ўчириб қўйилган", "Filter is now globally enabled": "Филтр энди глобал миқёсда ёқилган", "Filters": "<PERSON>ил<PERSON><PERSON><PERSON><PERSON><PERSON>", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Бармоқ изи алдаш аниқланди: аватар сифатида бош ҳарфлардан фойдаланиш имконсиз. Стандарт профил расми.", "Firecrawl API Base URL": "Firecrawl АПИ асосий УРЛ манзили", "Firecrawl API Key": "Firecrawl АПИ калити", "Fluidly stream large external response chunks": "Катта ташқи жавоб бўлакларини оқизиш", "Focus chat input": "Чат киритишга фокуслаш", "Folder deleted successfully": "Жилд муваффақиятли ўчирилди", "Folder name cannot be empty.": "Жилд номи бўш бўлиши мумкин эмас.", "Folder name updated successfully": "Жилд номи муваффақиятли янгиланди", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "Кўрсатмаларга мукаммал амал қилди", "Force OCR": "ОCРни мажбурлаш", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "ПДФ-нинг барча саҳифаларида ОCРни мажбурлаш. Агар ПДФ-файлларингизда яхши матн бўлса, бу ёмон натижаларга олиб келиши мумкин. Бирламчи параметрлар Фалсе.", "Forge new paths": "Янги йўлларни очинг", "Form": "<PERSON><PERSON><PERSON><PERSON>", "Format your variables using brackets like this:": "Ўзгарувчиларни қуйидаги каби қавслар ёрдамида форматланг:", "Forwards system user session credentials to authenticate": "Аутентификация қилиш учун тизим фойдаланувчиси сеанси ҳисоб маълумотларини йўналтиради", "Full Context Mode": "Тўлиқ контекст режими", "Function": "Функция", "Function Calling": "Функцияни чақириш", "Function created successfully": "Функция муваффақиятли яратилди", "Function deleted successfully": "Функция муваффақиятли ўчирилди", "Function Description": "Функция тавсифи", "Function ID": "Функция идентификатори", "Function imported successfully": "Функция муваффақиятли импорт қилинди", "Function is now globally disabled": "Функция энди бутун дунё бўйлаб ўчирилган", "Function is now globally enabled": "Функция энди глобал миқёсда ёқилган", "Function Name": "Функция номи", "Function updated successfully": "Функция муваффақиятли янгиланди", "Functions": "Функсиялар", "Functions allow arbitrary code execution.": "Функциялар ўзбошимчалик билан кодни бажаришга имкон беради.", "Functions imported successfully": "Функциялар муваффақиятли импорт қилинди", "Gemini": "Gemini", "Gemini API Config": "Gemini АПИ конфигурацияси", "Gemini API Key is required.": "Gemini АПИ калити талаб қилинади.", "General": "Умумий", "Generate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Generate an image": "Тасвир яра<PERSON>иш", "Generate Image": "Расм яратиш", "Generate prompt pair": "Тезкор жуфтликни яратинг", "Generating search query": "Қидирув сўрови яратилмоқда", "Generating...": "Яратилмоқда...", "Get started": "<PERSON>о<PERSON><PERSON><PERSON><PERSON>", "Get started with {{WEBUI_NAME}}": "{{WEBUI_NAME}} билан бошланг", "Global": "<PERSON>л<PERSON><PERSON><PERSON><PERSON>", "Good Response": "Ях<PERSON>и жавоб", "Google Drive": "Гоогле Дриве", "Google PSE API Key": "Гоогле ПСЕ АПИ калити", "Google PSE Engine Id": "Гоогле ПСЕ Энгине идентификатори", "Group created successfully": "Гуруҳ муваффақиятли яратилди", "Group deleted successfully": "Гуруҳ муваффақиятли ўчирилди", "Group Description": "Гуруҳ тавсифи", "Group Name": "Гуруҳ номи", "Group updated successfully": "Гуруҳ муваффақиятли янгиланди", "Groups": "Гуруҳлар", "Haptic Feedback": "Ҳаптик фикр-мулоҳазалар", "Hello, {{name}}": "Салом, {{name}}", "Help": "Ёрдам", "Help us create the best community leaderboard by sharing your feedback history!": "Фикр-мулоҳазаларингиз тарихини баҳам кўриш орқали энг яхши ҳамжамият етакчилари жадвалини яратишга ёрдам беринг!", "Hex Color": "Олти бурчакли ранг", "Hex Color - Leave empty for default color": "Ҳех Cолор - стандарт ранг учун бўш қолдиринг", "Hide": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Hide from Sidebar": "", "Hide Model": "Моделни яшириш", "High Contrast Mode": "Юқори контраст режими", "Home": "Уй", "Host": "Хо<PERSON>т", "How can I help you today?": "Бугун сизга қандай ёрдам бера оламан?", "How would you rate this response?": "Бу жавобни қандай баҳолайсиз?", "HTML": "ҲТМЛ", "Hybrid Search": "Гибрид қидирув", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Мен ўқиганимни тан оламан ва ўз ҳаракатларимнинг оқибатларини тушунаман. Мен ўзбошимчалик билан кодни бажариш билан боғлиқ хавфлардан хабардорман ва манбанинг ишончлилигини тасдиқладим.", "ID": "ИД", "iframe Sandbox Allow Forms": "ифраме Сандбох рухсат шакллари", "iframe Sandbox Allow Same Origin": "ифраме Сандбох бир хил келиб чиқишига рухсат беради", "Ignite curiosity": "Қизиқувчанликни ёқинг", "Image": "Расм", "Image Compression": "Тасвирни сиқиш", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "Тасвир яра<PERSON>иш", "Image Generation (Experimental)": "Тасвир яра<PERSON><PERSON><PERSON> (эксперимента<PERSON>)", "Image Generation Engine": "Тасвир яратиш механизми", "Image Max Compression Size": "Тасвирнинг максимал сиқиш ҳажми", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "Тасвир сўровини яратиш", "Image Prompt Generation Prompt": "Тасвир сўровини яратиш таклифи", "Image Settings": "Расм созламалари", "Images": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Import": "Импорт", "Import Chats": "Чатларни импорт қилиш", "Import Config from JSON File": "ЖСОН файлидан конфигурацияни импорт қилинг", "Import From Link": "Ҳаволадан импорт қилиш", "Import Functions": "Импорт функциялари", "Import Models": "Импорт моделлари", "Import Notes": "Эслатмаларни импорт қилиш", "Import Presets": "Олдиндан созламаларни импорт қилиш", "Import Prompt Suggestions": "Импорт бўйича таклифлар", "Import Prompts": "Импорт кўрсатмалари", "Import Tools": "Импорт воситалари", "Include": "Ўз ичига олади", "Include `--api-auth` flag when running stable-diffusion-webui": "Стабил-диффусион-wебуи ишлаётганда ъ--api-аутҳъ байроғини қўшинг", "Include `--api` flag when running stable-diffusion-webui": "Стабил-диффусион-wебуи ишлатилаётганда ъ--апиъ байроғини қўшинг", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "Алгоритм яратилган матндан олинган фикр-мулоҳазага қанчалик тез жавоб беришига таъсир қилади. Пастроқ ўрганиш тезлиги созлашнинг секинлашишига олиб келади, юқори ўрганиш тезлиги эса алгоритмни янада сезгир қилади.", "Info": "Маълумот", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "Комплекс қайта ишлаш учун бутун таркибни контекст сифатида киритинг, бу мураккаб сўровлар учун тавсия этилади.", "Input commands": "Кир<PERSON>ш буйруқлари", "Install from Github URL": "Гитҳуб УРЛ манзилидан ўрнатинг", "Instant Auto-Send After Voice Transcription": "Овозли транскрипсиядан кейин дарҳол автоматик юбориш", "Integration": "Интеграция", "Interface": "Интерфейс", "Invalid file content": "Файл мазмуни нотўғри", "Invalid file format.": "Файл формати нотўғри.", "Invalid JSON file": "ЖСОН файли яроқсиз", "Invalid Tag": "Тег нотўғри", "is typing...": "ёзмоқда...", "January": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Jina API Key": "Жина АПИ калити", "join our Discord for help.": "ёрдам учун Дисcордимизга қўшилинг.", "JSON": "ЖСОН", "JSON Preview": "ЖСОН кўриб чиқиш", "July": "июл", "June": "июн", "Jupyter Auth": "Жу<PERSON>йтер Аутҳ", "Jupyter URL": "Жу<PERSON>йтер УРЛ", "JWT Expiration": "ЖWТ муддати", "JWT Token": "ЖWТ токени", "Kagi Search API Key": "Каги қидирув АПИ калити", "Keep in Sidebar": "", "Key": "Калит", "Keyboard shortcuts": "Клавиатура ёрлиқлари", "Knowledge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Knowledge Access": "Били<PERSON><PERSON>а кириш", "Knowledge created successfully.": "Билим муваффақиятли яратилди.", "Knowledge deleted successfully.": "Маълумотлар муваффақиятли ўчирилди.", "Knowledge Public Sharing": "Билимларни оммавий алмашиш", "Knowledge reset successfully.": "Маълумотлар қайта тикланди.", "Knowledge updated successfully": "Билим муваффақиятли янгиланди", "Kokoro.js (Browser)": "Кокор<PERSON>.жс (браузе<PERSON>)", "Kokoro.js Dtype": "Кокоро.жс Д тури", "Label": "Ёрлиқ", "Landing Page Mode": "Очилиш саҳифаси режими", "Language": "Тил", "Language Locales": "Маҳаллий тиллар", "Languages": "<PERSON>и<PERSON><PERSON><PERSON><PERSON>", "Last Active": "Охирги фаол", "Last Modified": "Охирги таҳрирланган", "Last reply": "Охирги жавоб", "LDAP": "LDAP", "LDAP server updated": "LDAP сервери янгиланди", "Leaderboard": "Пешқадамлар жадвали", "Learn more about OpenAPI tool servers.": "ОпенАПИ воситаси серверлари ҳақида кўпроқ билиб олинг.", "Leave empty for no compression": "", "Leave empty for unlimited": "Чексиз учун бўш қолдиринг", "Leave empty to include all models from \"{{url}}\" endpoint": "“{{url}}” сўнгги нуқтасидаги барча моделларни киритиш учун бўш қолдиринг", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "“{{url}}/api/тагс” сўнгги нуқтасидаги барча моделларни киритиш учун бўш қолдиринг", "Leave empty to include all models from \"{{url}}/models\" endpoint": "“{{url}}/моделс” сўнгги нуқтасидаги барча моделларни киритиш учун бўш қолдиринг", "Leave empty to include all models or select specific models": "Барча моделларни киритиш ёки муайян моделларни танлаш учун бўш қолдиринг", "Leave empty to use the default prompt, or enter a custom prompt": "Стандарт таклифдан фойдаланиш учун бўш қолдиринг ёки махсус таклифни киритинг", "Leave model field empty to use the default model.": "Стандарт моделдан фойдаланиш учун модел майдонини бўш қолдиринг.", "License": "Литсензия", "Light": "<PERSON>ур", "Listening...": "Тингланмоқда...", "Llama.cpp": "Ллама.cпп", "LLMs can make mistakes. Verify important information.": "ЛЛ<PERSON><PERSON>ар хато қилишлари мумкин. Муҳим маълумотларни тасдиқланг.", "Loader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Loading Kokoro.js...": "Кокоро.жс юкланмоқда...", "Local": "Маҳаллий", "Local Task Model": "Маҳаллий вазифа модели", "Location access not allowed": "Жо<PERSON><PERSON><PERSON>шувга рухсат берилмаган", "Lost": "Йўқотилган", "LTR": "ЛТР", "Made by Open WebUI Community": "Опен WебУИ ҳамжамияти томонидан яратилган", "Make password visible in the user interface": "", "Make sure to enclose them with": "Уларни ўраб қўйганингизга ишонч ҳосил қилинг", "Make sure to export a workflow.json file as API format from ComfyUI.": "Wоркфлоw.жсон файлини CомфюИъдан АПИ формати сифатида экспорт қилганингизга ишонч ҳосил қилинг.", "Manage": "Бошқариш", "Manage Direct Connections": "Тўғридан-тўғри уланишларни бошқариш", "Manage Models": "Моделларни бошқариш", "Manage Ollama": "Олламани бошқаринг", "Manage Ollama API Connections": "Ollama АПИ уланишларини бошқаринг", "Manage OpenAI API Connections": "OpenAI АПИ уланишларини бошқаринг", "Manage Pipelines": "Қувурларни бошқариш", "Manage Tool Servers": "Асбоб серверларини бошқариш", "March": "Ма<PERSON><PERSON>", "Markdown": "Маркдоwн", "Max Speakers": "Максимал динамиклар", "Max Upload Count": "Максимал юклаш сони", "Max Upload Size": "Максимал юклаш ҳажми", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Бир вақтнинг ўзида максимал 3 та моделни юклаб олиш мумкин. Кейинроқ қайта уриниб кўринг.", "May": "май", "Memories accessible by LLMs will be shown here.": "ЛЛМлар кириши мумкин бўлган хотиралар бу эрда кўрсатилади.", "Memory": "Хотира", "Memory added successfully": "Хотира муваффақиятли қўшилди", "Memory cleared successfully": "Хотира муваффақиятли тозаланди", "Memory deleted successfully": "Хотира муваффақиятли ўчирилди", "Memory updated successfully": "Хотира муваффақиятли янгиланди", "Merge Responses": "Жавобларни бирлаштириш", "Merged Response": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> жавоб", "Message rating should be enabled to use this feature": "Бу функсиядан фойдалан<PERSON><PERSON> учун хабарлар рейтинги ёқилиши керак", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Ҳаволани яратганингиздан кейин юборган хабарларингиз улашилмайди. УРЛ манзили бўлган фойдаланувчилар умумий чатни кўришлари мумкин бўлади.", "Microsoft OneDrive": "Microsoft ОнеДриве", "Microsoft OneDrive (personal)": "Microsoft ОнеДриве (шахсий)", "Microsoft OneDrive (work/school)": "Microsoft ОнеДриве (иш/мактаб)", "Mistral OCR": "Mistral ОCР", "Mistral OCR API Key required.": "Mistral ОCР АПИ калити талаб қилинади.", "Model": "<PERSON>о<PERSON><PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "“{{modelName}}” модели юклаб олинди.", "Model '{{modelTag}}' is already in queue for downloading.": "“{{modelTag}}” модели аллақачон юклаб олиш учун навбатда турибди.", "Model {{modelId}} not found": "{{modelId}} модели топилмади", "Model {{modelName}} is not vision capable": "{{modelName}} модели кўриш қобилиятига эга эмас", "Model {{name}} is now {{status}}": "{{name}} модели энди {{status}}", "Model {{name}} is now hidden": "{{name}} модели энди яширин", "Model {{name}} is now visible": "{{name}} модели энди кўринади", "Model accepts file inputs": "Модел файл киришларини қабул қилади", "Model accepts image inputs": "Модел расм киритишни қабул қилади", "Model can execute code and perform calculations": "Модел кодни бажариши ва ҳисоб-китобларни амалга ошириши мумкин", "Model can generate images based on text prompts": "Модел матн таклифлари асосида тасвирларни яратиши мумкин", "Model can search the web for information": "Модел маълумот учун Интернетда қидириши мумкин", "Model created successfully!": "Модел муваффақиятли яратилди!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Модел файл тизими йўли аниқланди. Янги<PERSON><PERSON><PERSON> учун модел қисқа номи талаб қилинади, давом эттириб бўлмайди.", "Model Filtering": "Моделни филтрлаш", "Model ID": "Модел ИД", "Model IDs": "Модел идентификаторлари", "Model Name": "Модел номи", "Model not selected": "Модел танлан<PERSON>аган", "Model Params": "Модел параметрлари", "Model Permissions": "Модел рухсатномалари", "Model unloaded successfully": "Модел муваффақиятли юклаб олинди", "Model updated successfully": "Модел муваффақиятли янгиланди", "Model(s) do not support file upload": "<PERSON>о<PERSON><PERSON><PERSON>(лар) файлни юклашни қўллаб-қувватламайди", "Modelfile Content": "Модел файли таркиби", "Models": "Моделл<PERSON>р", "Models Access": "Моделлар<PERSON>а кириш", "Models configuration saved successfully": "Моделлар конфигурацияси муваффақиятли сақланди", "Models Public Sharing": "Моделларни оммавий алмашиш", "Mojeek Search API Key": "Можеэк қидирув АПИ калити", "more": "Кўпроқ", "More": "Кўпроқ", "My Notes": "Менинг эслатмаларим", "Name": "Исм", "Name your knowledge base": "Билимлар базасини номланг", "Native": "Маҳаллий", "New Chat": "<PERSON>н<PERSON>и чат", "New Folder": "Ян<PERSON>и жилд", "New Function": "Янги функсия", "New Note": "Янги эслатма", "New Password": "Янги парол", "New Tool": "Янги восита", "new-channel": "янги канал", "Next message": "", "No chats found for this user.": "Бу фойдаланувчи учун ҳеч қандай чат топилмади.", "No chats found.": "Ҳеч қандай чат топилмади.", "No content": "Контент йўқ", "No content found": "Ҳеч қандай контент топилмади", "No content found in file.": "Файлда контент топилмади.", "No content to speak": "Гапира<PERSON><PERSON><PERSON>ан таркиб йўқ", "No distance available": "Масофа мавжуд эмас", "No feedbacks found": "Ҳеч қандай фикр топилмади", "No file selected": "Ҳеч қандай файл танланмаган", "No groups with access, add a group to grant access": "Рухсат берилган гуруҳлар йўқ, рухсат бериш учун гуруҳ қўшинг", "No HTML, CSS, or JavaScript content found.": "HTML, CСС ёки ЖаваСcрипт контенти топилмади.", "No inference engine with management support found": "Бошқарув ёрдами билан хулоса чиқариш механизми топилмади", "No knowledge found": "Ҳеч қандай билим топилмади", "No memories to clear": "Тоза<PERSON><PERSON><PERSON> учун хотиралар йўқ", "No model IDs": "Модел идентификаторлари йўқ", "No models found": "Ҳеч қандай модел топилмади", "No models selected": "Ҳеч қандай модел танланмаган", "No Notes": "Қайдлар йўқ", "No results found": "Ҳеч қандай натижа топилмади", "No search query generated": "Ҳеч қандай қидирув сўрови яратилмади", "No source available": "Манба мавжуд эмас", "No users were found.": "Ҳеч қандай фойдаланувчи топилмади.", "No valves to update": "Ян<PERSON><PERSON><PERSON><PERSON><PERSON> учун клапанлар йўқ", "None": "Йўқ", "Not factually correct": "Аслида тўғри эмас", "Not helpful": "Фойдали эмас", "Note deleted successfully": "Эслатма муваффақиятли ўчирилди", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Эслатма: Агар сиз минимал балл қўйсангиз, қидирув фақат минимал баллдан каттароқ ёки унга тенг баллга эга ҳужжатларни қайтаради.", "Notes": "Эслат<PERSON>алар", "Notification Sound": "Билдиришнома овози", "Notification Webhook": "Билдиришнома веб-ҳук", "Notifications": "Бил<PERSON><PERSON><PERSON><PERSON><PERSON>номалар", "November": "ноябр", "OAuth ID": "ОАутҳ ИД", "October": "октябр", "Off": "Ўчирилган", "Okay, Let's Go!": "Майли, кетайлик!", "OLED Dark": "ОЛЕД қоронғи", "Ollama": "Ollama", "Ollama API": "Ollama АПИ", "Ollama API settings updated": "Ollama АПИ созламалари янгиланди", "Ollama Version": "Ollama версияси", "On": "Ёниқ", "OneDrive": "ОнеДриве", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "Фақат ҳарф-рақамли белгилар ва дефисларга рухсат берилади", "Only alphanumeric characters and hyphens are allowed in the command string.": "Буйруқлар қаторида фақат ҳарф-рақамли белгилар ва дефисларга рухсат берилади.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Фақат тўпламларни таҳрирлаш мумкин, ҳужжатларни таҳрирлаш/қўшиш учун янги билимлар базасини яратинг.", "Only markdown files are allowed": "Фақат маркдоwн файлларига рухсат берилади", "Only select users and groups with permission can access": "Фақат рухсати бор танланган фойдаланувчилар ва гуруҳларга кириш мумкин", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Вой! УРЛ нотўғри кўринади. Илтимос, икки марта текширинг ва қайта уриниб кўринг.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Вой! Ҳали ҳам файллар юкланмоқда. Илтимос, юклаш тугашини кутинг.", "Oops! There was an error in the previous response.": "Вой! Аввалги жавобда хатолик юз берди.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Вой! Сиз қўллаб-қувватланмайдиган усулдан фойдаланмоқдасиз (фақат фронтенд). Илтимос, WебУИ-га баcкенд орқали хизмат кўрсатинг.", "Open file": "Файлни очиш", "Open in full screen": "Тўлиқ экранда очинг", "Open modal to configure connection": "", "Open new chat": "Янги чат очинг", "Open WebUI can use tools provided by any OpenAPI server.": "Опен WебУИ ҳар қандай ОпенАПИ сервери томонидан тақдим этилган воситалардан фойдаланиши мумкин.", "Open WebUI uses faster-whisper internally.": "Опен WебУИ ички тез шивирлашдан фойдаланади.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Опен WебУИ СпеэчТ5 ва CМУ Арcтиc динамик ўрнатишларидан фойдаланади.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Очиқ WебУИ версияси (в{{ОПЕН_WЕБУИ_ВЕРСИОН}}) талаб қилинган версиядан (в{{РЕҚУИРЕД_ВЕРСИОН}}) пастроқ", "OpenAI": "OpenAI", "OpenAI API": "OpenAI АПИ", "OpenAI API Config": "OpenAI АПИ конфигурацияси", "OpenAI API Key is required.": "OpenAI АПИ калити талаб қилинади.", "OpenAI API settings updated": "OpenAI АПИ созламалари янгиланди", "OpenAI URL/Key required.": "OpenAI УРЛ/Калит талаб қилинади.", "openapi.json URL or Path": "openapi.json УРЛ ёки йўл", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "ёки", "Organize your users": "Фойдаланувчиларингизни тартибга солинг", "Other": "Бошқа", "OUTPUT": "Чиқиш", "Output format": "Чиқиш формати", "Output Format": "Чиқиш формати", "Overview": "Умумий кўриниш", "page": "саҳифа", "Paginate": "Саҳифалаш", "Parameters": "Пара<PERSON><PERSON>т<PERSON><PERSON><PERSON>р", "Password": "Парол", "Paste Large Text as File": "Катта матнни файл сифатида жойлаштиринг", "PDF document (.pdf)": "ПДФ ҳужжат (.pdf)", "PDF Extract Images (OCR)": "ПДФ экстракти расмлари (OCR)", "pending": "кутилмоқда", "Pending": "", "Pending User Overlay Content": "Кути<PERSON><PERSON><PERSON>тган фойдаланувчи Оверлай контенти", "Pending User Overlay Title": "Кутил<PERSON><PERSON><PERSON>ган фойдаланувчи сарлавҳаси", "Permission denied when accessing media devices": "Медиа қурилмаларга киришда рухсат рад этилди", "Permission denied when accessing microphone": "Микрофонга киришда рухсат берилмади", "Permission denied when accessing microphone: {{error}}": "Микрофонга киришда рухсат рад этилди: {{error}}", "Permissions": "<PERSON>у<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Perplexity API Key": "Қийинчилик АПИ калити", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "<PERSON><PERSON><PERSON>", "Pinned": "Қадалган", "Pioneer insights": "Пионер тушунчалари", "Pipeline deleted successfully": "Қувур ўчирилди", "Pipeline downloaded successfully": "Қувур юклаб олинди", "Pipelines": "Қувурлар", "Pipelines Not Detected": "Қувурлар аниқланмади", "Pipelines Valves": "Қувур қувурлари клапанлари", "Plain text (.md)": "Од<PERSON><PERSON> матн (.md)", "Plain text (.txt)": "Од<PERSON><PERSON> матн (.txt)", "Playground": "Ўйин майдончаси", "Playwright Timeout (ms)": "Драматург таймоути (ms)", "Playwright WebSocket URL": "Драма<PERSON><PERSON><PERSON><PERSON> WебСоcкет УРЛ манзили", "Please carefully review the following warnings:": "Қуйидаги огоҳлантиришларни диққат билан кўриб чиқинг:", "Please do not close the settings page while loading the model.": "Моделни юклашда созламалар саҳифасини ёпманг.", "Please enter a prompt": "Илтимо<PERSON>, таклиф киритинг", "Please enter a valid path": "Яроқли йўлни киритинг", "Please enter a valid URL": "Яроқли УРЛ манзилини киритинг", "Please fill in all fields.": "Илтимос, барча майдонларни тўлдиринг.", "Please select a model first.": "Илтимо<PERSON>, аввал моделни танланг.", "Please select a model.": "Илтимос, моделни танланг.", "Please select a reason": "Сабабини танланг", "Port": "Порт", "Positive attitude": "Ижобий муносабат", "Prefix ID": "Префикс идентификатори", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Префикс идентификатори модел идентификаторларига префикс қўшиш орқали бошқа уланишлар билан зиддиятларни олдини олиш учун ишлатилади - ўчириш учун бўш қолдиринг.", "Prevent file creation": "", "Preview": "Кўриб чиқиш", "Previous 30 days": "Олдинги 30 кун", "Previous 7 days": "Олдинги 7 кун", "Previous message": "", "Private": "Шахсий", "Profile Image": "Профил расми", "Prompt": "Тезкор", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Тезкор (м<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, менга Рим империяси ҳақида қизиқарли фактни айтиб беринг)", "Prompt Autocompletion": "Тезкор автоматик якунлаш", "Prompt Content": "Тезкор таркиб", "Prompt created successfully": "Сўров муваффақиятли яратилди", "Prompt suggestions": "Тезкор таклифлар", "Prompt updated successfully": "Сўров муваффақиятли янгиланди", "Prompts": "Кўрсатмалар", "Prompts Access": "Киришни таклиф қилади", "Prompts Public Sharing": "Умумий алмашишни таклиф қилади", "Public": "Оммавий", "Pull \"{{searchValue}}\" from Ollama.com": "Ollama.cом сайтидан “{{сеарчВалуе}}”ни тортинг", "Pull a model from Ollama.com": "Ollama.cом дан моделни тортинг", "Query Generation Prompt": "Сўровни яратиш таклифи", "RAG Template": "РАГ шаблони", "Rating": "<PERSON>ей<PERSON>инг", "Re-rank models by topic similarity": "Моделларни мавзу ўхшашлиги бўйича қайта тартибланг", "Read": "Ўқинг", "Read Aloud": "Овоз чиқариб ўқинг", "Reason": "", "Reasoning Effort": "Мулоҳаза юритиш ҳаракатлари", "Record": "Ёзиб олиш", "Record voice": "Овозни ёзиб олинг", "Redirecting you to Open WebUI Community": "Сизни Опен WебУИ ҳамжамиятига йўналтирмоқда", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "Бемаъни нарсаларни яратиш эҳтимолини камайтиради. Юқори қиймат (мас<PERSON><PERSON><PERSON><PERSON>, 100) турли хил жавоблар беради, пастроқ қиймат (мас<PERSON><PERSON><PERSON><PERSON>, 10) эса консерватив бўлади.", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Ўзингизни \"Фойдаланувчи\" деб кўрсатинг (масала<PERSON>, \"Фойдаланувчи испан тилини ўрганмоқда\")", "References from": "Маълумотномалар", "Refused when it shouldn't have": "Бўлмаслиги керак бўлганда рад этилди", "Regenerate": "Қайта тиклаш", "Reindex": "Қайта индекс", "Reindex Knowledge Base Vectors": "Реиндех билимлар базаси векторлари", "Release Notes": "Чиқариш эслатмалари", "Releases": "Релизлар", "Relevance": "Мувофиқлик", "Relevance Threshold": "Мувофиқлик чегараси", "Remove": "Ўчириш", "Remove {{MODELID}} from list.": "", "Remove Model": "Моделни олиб ташлаш", "Remove this tag from list": "", "Rename": "Номини ўзгартириш", "Reorder Models": "Моделларни қайта тартиблаш", "Reply in Thread": "Мавзуда жавоб беринг", "Reranking Engine": "Двигателни қайта тартиблаш", "Reranking Model": "Қайта тартиблаш модели", "Reset": "Қайта тиклаш", "Reset All Models": "Барча моделларни қайта ўрнатиш", "Reset Upload Directory": "Ю<PERSON><PERSON><PERSON><PERSON> каталогини тиклаш", "Reset Vector Storage/Knowledge": "Вектор хотираси/билимини қайта ўрнатиш", "Reset view": "Кўринишни тиклаш", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Жавоб билдиришномаларини фаоллаштириб бўлмайди, чунки веб-сайт рухсатномалари рад этилган. Керакли рухсат бериш учун браузер созламаларига ташриф буюринг.", "Response splitting": "Жавобни ажратиш", "Response Watermark": "Жавоб сув белгиси", "Result": "Натижа", "Retrieval": "Қидирув", "Retrieval Query Generation": "Қидирув сўровларини яратиш", "Rich Text Input for Chat": "Чат учун бой матн киритиш", "RK": "RK", "Role": "Рол", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "Ишга тушириш", "Running": "Ишл<PERSON>ётган", "Save": "Сақлаш", "Save & Create": "Сақлаш ва яратиш", "Save & Update": "Сақлаш ва янгилаш", "Save As Copy": "Нусха сифатида сақлаш", "Save Tag": "Тегни сақланг", "Saved": "Сақланган", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Чат журналларини бевосита браузерингиз хотирасига сақлаш энди қўллаб-қувватланмайди. Қуйидаги тугмани босиш орқали суҳбат журналларингизни юклаб олинг ва ўчиринг. Хавотир олманг, сиз чат журналларини баcкенд орқали осонгина қайта импорт қилишингиз мумкин", "Scroll On Branch Change": "Филиални ўзгартириш бўйича айлантиринг", "Search": "Қидирув", "Search a model": "Моделни қидиринг", "Search Base": "Қидирув базаси", "Search Chats": "Чатларни қидириш", "Search Collection": "Тўпламни қидириш", "Search Filters": "Қидирув филтрлари", "search for tags": "тегларни қидиринг", "Search Functions": "Қидирув функсиялари", "Search Knowledge": "Били<PERSON>ларни қидириш", "Search Models": "Моделларни қидириш", "Search options": "Қидирув вариантлари", "Search Prompts": "Қидирув кўрсатмалари", "Search Result Count": "Қидирув натижалари сони", "Search the internet": "Интернетда қидиринг", "Search Tools": "Қидирув воситалари", "SearchApi API Key": "SearchApi АПИ калити", "SearchApi Engine": "SearchApi механизми", "Searched {{count}} sites": "{{count}} та сайт қидирилди", "Searching \"{{searchQuery}}\"": "“{{searchQuery}}” қидирилмоқда", "Searching Knowledge for \"{{searchQuery}}\"": "“{{searchQuery}}” бўйича маълумотлар қидирилмоқда", "Searching the web...": "Интернетда қидирилмоқда...", "Searxng Query URL": "Searxng сўрови УРЛ", "See readme.md for instructions": "Кўрсатмалар учун readme.md га қаранг", "See what's new": "Нима янгиликлар борлигини кўринг", "Seed": "Дастлабки маълумот", "Select a base model": "Асосий моделни танланг", "Select a engine": "Двигателни танланг", "Select a function": "Функцияни танланг", "Select a group": "Гуруҳни танланг", "Select a model": "Моделни танланг", "Select a pipeline": "Қувурни танланг", "Select a pipeline url": "Қувур линиясининг УРЛ манзилини танланг", "Select a tool": "Асбобни танланг", "Select an auth method": "Аутентификация усулини танланг", "Select an Ollama instance": "Оллама версиясини танланг", "Select Engine": "ДИшловчи тизимни танланг ", "Select Knowledge": "Билим-ни тан<PERSON><PERSON><PERSON><PERSON>", "Select only one model to call": "Қўнғироқ қилиш учун фақат битта моделни танланг", "Selected model(s) do not support image inputs": "Тан<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> модел(лар) тасвир киритишни қўллаб-қувватламайди", "Semantic distance to query": "Сўров учун семантик масофа", "Send": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Send a Message": "<PERSON><PERSON><PERSON><PERSON><PERSON> юб<PERSON><PERSON><PERSON><PERSON>", "Send message": "<PERSON><PERSON><PERSON><PERSON><PERSON> юб<PERSON><PERSON><PERSON><PERSON>", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Сўровда ъстреам_оптионс: { инcлуде_усаге: труе }ъ юборади.\nҚўллаб-қувватланадиган провайдерлар ўрнатилганда жавобда токен фойдаланиш маълумотларини қайтаради.", "September": "сентябр", "SerpApi API Key": "SerpApi АПИ калити", "SerpApi Engine": "SerpApi двигатели", "Serper API Key": "Serper АПИ калити", "Serply API Key": "Serply АПИ калити", "Serpstack API Key": "Serpstack АПИ калити", "Server connection verified": "Сервер уланиши тасдиқланди", "Set as default": "Стандарт сифатида ўрнатинг", "Set CFG Scale": "CFG шкаласини ўрнатинг", "Set Default Model": "Стандарт моделни ўрнатинг", "Set embedding model": "Ўрнатиш моделини ўрнатинг", "Set embedding model (e.g. {{model}})": "Ўрнатиш моделини ўрнатиш (ма<PERSON><PERSON><PERSON><PERSON><PERSON>, {{модел}})", "Set Image Size": "Расм ҳажмини ўрнатинг", "Set reranking model (e.g. {{model}})": "Қайта тартиблаш моделини ўрнатиш (маса<PERSON>а<PERSON>, {{модел}})", "Set Sampler": "Намуна олувчини созлаш", "Set Scheduler": "Режалаштирувчини ўрнатиш", "Set Steps": "Қадамларни ўрнатиш", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "ГПУ-га юкланадиган қатламлар сонини белгиланг. Ушбу қийматни ошириш ГПУ тезлаштириш учун оптималлаштирилган моделлар учун иш фаолиятини сезиларли даражада яхшилаши мумкин, лекин кўпроқ қувват ва ГПУ ресурсларини истеъмол қилиши мумкин.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Ҳисоблаш учун ишлатиладиган ишчи иплар сонини белгиланг. Ушбу параметр кирувчи сўровларни бир вақтнинг ўзида қайта ишлаш учун қанча мавзу ишлатилишини назорат қилади. Ушбу қийматни ошириш юқори бир вақтда иш юкида ишлашни яхшилаши мумкин, лекин кўпроқ CПУ ресурсларини истеъмол қилиши мумкин.", "Set Voice": "Овозни созлаш", "Set whisper model": "Пичи<PERSON><PERSON><PERSON><PERSON> моделини ўрнатинг", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Ҳеч бўлмаганда бир марта пайдо бўлган токенларга нисбатан текис чизиқ ўрнатади. Юқори қиймат (масалан, 1,5) такрорлаш учун қаттиқроқ жазоланади, пастроқ қиймат (мас<PERSON><PERSON><PERSON><PERSON>, 0,9) эса юмшоқроқ бўлади. 0 бўлса, у ўчирилган.", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Қанча марта пайдо бўлганига қараб, такрорланишларни жазолаш учун токенларга нисбатан масштабни ўрнатади. Юқори қиймат (мас<PERSON>лан, 1,5) такрорлаш учун қаттиқроқ жазоланади, пастроқ қиймат (масалан, 0,9) эса юмшоқроқ бўлади. 0 бўлса, у ўчирилган.", "Sets how far back for the model to look back to prevent repetition.": "Такрорлан<PERSON>шнинг олдини олиш учун моделнинг орқага қарашини белгилайди.", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "Насл қилиш учун тасодифий сонлар уруғини ўрнатади. Буни маълум бир рақамга ўрнатиш, моделни бир хил сўров учун бир хил матн яратишга мажбур қилади.", "Sets the size of the context window used to generate the next token.": "Кейинги токенни яратиш учун фойдаланилади<PERSON>ан контекст ойнасининг ҳажмини белгилайди.", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Фойдалан<PERSON>ш учун тўхташ кетма-кетликларини ўрнатади. Ушбу нақшга дуч келганда, ЛЛМ матн яратишни тўхтатади ва қайтиб келади. Модел файлида бир нечта алоҳида тўхташ параметрларини белгилаш орқали бир нечта тўхташ нақшлари ўрнатилиши мумкин.", "Settings": "Созламалар", "Settings saved successfully!": "Созламалар муваффақиятли сақланди!", "Share": "<PERSON>л<PERSON><PERSON><PERSON><PERSON>", "Share Chat": "Чат<PERSON>и улашиш", "Share to Open WebUI Community": "Опен WебУИ ҳамжамиятига улашинг", "Sharing Permissions": "Рухсатларни алмашиш", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "Кўрсатиш", "Show \"What's New\" modal on login": "Кири<PERSON>да \"Янги нарсалар\" модалини кўрсатинг", "Show Admin Details in Account Pending Overlay": "Ҳисоб кутилаётган қатламда администратор маълумотларини кўрсатиш", "Show All": "Ҳаммасини кўрсатиш", "Show Less": "Камроқ кўрсатиш", "Show Model": "Моделни кўрсатиш", "Show shortcuts": "Ёрлиқларни кўрсатиш", "Show your support!": "Қўллаб-қувватланг!", "Showcased creativity": "Кўрсатилган ижодкорлик", "Sign in": "тизимга кириш", "Sign in to {{WEBUI_NAME}}": "{{WEBUI_NAME}} ҳисобига киринг", "Sign in to {{WEBUI_NAME}} with LDAP": "LDAP ёрдамида {{WEBUI_NAME}} сайтига киринг", "Sign Out": "Тизимдан чиқиш", "Sign up": "Рўйхатдан ўтиш", "Sign up to {{WEBUI_NAME}}": "{{WEBUI_NAME}} тармоғида рўйхатдан ўтинг", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "Жад<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ш<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, инлине математика ва тартибни аниқлашни яхшилаш учун ЛЛМ ёрдамида аниқликни сезиларли даражада яхшилайди. Кечикиш вақтини оширади. Стандартлар рост.", "Signing in to {{WEBUI_NAME}}": "{{WEBUI_NAME}} ҳисобига кириш", "sk-1234": "ск-1234", "Skip Cache": "Кешни ўтказиб юбориш", "Skip the cache and re-run the inference. Defaults to False.": "Кешни ўтказиб юборинг ва хулосани қайта ишга туширинг. Бирламчи параметрлар Фалсе.", "Sougou Search API sID": "Sougou Сеарч АПИ сИД", "Sougou Search API SK": "Sougou Сеарч АПИ СК", "Source": "Манба", "Speech Playback Speed": "Нутқни ижро этиш тезлиги", "Speech recognition error: {{error}}": "Нутқни аниқлашда хатолик: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "Нутқдан матнга восита", "Stop": "СТОП", "Stop Generating": "", "Stop Sequence": "Кетма-кетликни тўхтатиш", "Stream Chat Response": "Chat жавобини юбориш", "Strip Existing OCR": "<PERSON>а<PERSON><PERSON><PERSON>д OCRни олиб ташлаш", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "PDFдан мавжуд OCR матнини олиб ташланг ва OCRни қайта ишлатинг. Agar Force OCR ёқилган бўлса, эътиборга олинмайди. Стандарт қиймат: False.", "STT Model": "СТТ модели", "STT Settings": "СТТ созламалари", "Stylized PDF Export": "Услубий PDF экспорти", "Subtitle (e.g. about the Roman Empire)": "Субтитр (ма<PERSON><PERSON><PERSON><PERSON><PERSON>, Рим империяси ҳақида)", "Success": "Муваффақият", "Successfully updated.": "Муваффақиятли янгиланди.", "Suggested": "Тавсия этилган", "Support": "Қўллаб-қувватлаш", "Support this plugin:": "Ушбу плагинни қўллаб-қувватланг:", "Supported MIME Types": "", "Sync directory": "Син<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> каталоги", "System": "Тизим", "System Instructions": "Тизим кўрсатмалари", "System Prompt": "Тизим сўрови", "Tags": "Теглар", "Tags Generation": "Теглар яратиш", "Tags Generation Prompt": "Теглар яратиш таклифи", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "Чиқишдан камроқ эҳтимолий токенларнинг таъсирини камайтириш учун қуйруқсиз намуна олиш қўлланилади. Юқори қиймат (масалан, 2.0) таъсирни кўпроқ камайтиради, 1.0 қиймати эса бу созламани ўчириб қўяди.", "Talk to model": "Модел билан гапла<PERSON>инг", "Tap to interrupt": "Тўхтатиш учун босинг", "Task Model": "Вазифа модели", "Tasks": "Вази<PERSON><PERSON><PERSON><PERSON>р", "Tavily API Key": "Tavily АПИ калити", "Tavily Extract Depth": "Tavily экстракти чуқурлиги", "Tell us more:": "Бизга кўпроқ маълумот беринг:", "Temperature": "Ҳарорат", "Temporary Chat": "Вақтинчалик суҳбат", "Text Splitter": "Матн ажратувчи", "Text-to-Speech": "", "Text-to-Speech Engine": "Матнни нутққа айлантириш механизми", "Thanks for your feedback!": "Фикр-мулоҳазангиз учун ташаккур!", "The Application Account DN you bind with for search": "Қидириш учун сиз боғланган илова ҳисоби ДН", "The base to search for users": "Фойдаланувчиларни қидириш учун асос", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "Тўплам ҳажми бир вақтнинг ўзида нечта матн сўровлари биргаликда қайта ишланишини аниқлайди. Каттароқ партия ҳажми моделнинг ишлаши ва тезлигини ошириши мумкин, лекин у ҳам кўпроқ хотира талаб қилади.", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Ушбу плагин ортидаги ишлаб чиқувчилар жамиятнинг эҳтиросли кўнгиллиларидир. Агар сиз ушбу плагинни фойдали деб билсангиз, унинг ривожланишига ҳисса қўшинг.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Баҳолаш пешқадами Эло рейтинг тизимига асосланади ва реал вақт режимида янгиланади.", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "Кир<PERSON>ш аудиосининг тили. Кириш тилини ISO-639-1 (масала<PERSON>, en) форматида тақдим этиш аниқлик ва кечикишни яхшилайди. Тилни автоматик аниқлаш учун бўш қолдиринг.", "The LDAP attribute that maps to the mail that users use to sign in.": "LDAP атрибути фойдаланувчиларнинг тизимга киришда фойдаланадиган почта манзилига мос келади.", "The LDAP attribute that maps to the username that users use to sign in.": "Фойдаланувчилар тизимга кириш учун фойдаланадиган фойдаланувчи номига мос келадиган LDAP атрибути.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Пешқадамлар жадвали ҳозирда бета-версияда ва биз алгоритмни такомиллаштириш жараёнида рейтинг ҳисоб-китобларини созлашимиз мумкин.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Файлнинг максимал ҳажми МБ. Агар файл ҳажми ушбу чегарадан ошса, файл юкланмайди.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Чатда бир вақтнинг ўзида ишлатилиши мумкин бўлган максимал файллар сони. Агар файллар сони ушбу чегарадан ошса, файллар юкланмайди.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "Матн учун чиқиш формати. \"жсон\", \"маркдоwн\" ёки \"ҳтмл\" бўлиши мумкин. Бирламчи \"маркдоwн\" учун.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Бал 0,0 (0%) ва 1,0 (100%) оралиғида бўлиши керак.", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "Моделдаги ҳарорат. Ҳароратни ошириш моделни янада ижодий жавоб беришга мажбур қилади.", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "Мавзу", "Thinking...": "Ўйлаш...", "This action cannot be undone. Do you wish to continue?": "Бу амални ортга қайтариб бўлмайди. Давом этишни хоҳлайсизми?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "Бу канал {{cреатедАт}} да яратилган. Бу {{чаннелНаме}} каналининг бошланиши.", "This chat won't appear in history and your messages will not be saved.": "", "This chat won’t appear in history and your messages will not be saved.": "Бу чат тарихда кўринмайди ва хабарларингиз сақланмайди.", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Бу сизнинг қимматли суҳбатларингиз маълумотлар базасига хавфсиз тарзда сақланишини таъминлайди. Раҳмат!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Бу экспериментал хусусият бўлиб, у кутилганидек ишламаслиги ва исталган вақтда ўзгариши мумкин.", "This model is not publicly available. Please select another model.": "Ушбу модел ҳамма учун очиқ эмас. Илтимос, бошқа моделни танланг.", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "Ушбу параметр контекстни янгилашда қанча токенлар сақланишини назорат қилади. М<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, агар 2 га ўрнатилган бўлса, суҳбат контекстининг охирги 2 та белгиси сақланиб қолади. Контекстни сақлаш суҳбатнинг узлуксизлигини сақлашга ёрдам беради, лекин бу янги мавзуларга жавоб бериш қобилиятини камайтириши мумкин.", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "Ушбу параметр модел жавобида яратиши мумкин бўлган токенларнинг максимал сонини белгилайди. Ушбу чегарани ошириш моделга узоқроқ жавобларни тақдим этиш имконини беради, бироқ у фойдасиз ёки аҳамиятсиз контент яратилиш эҳтимолини ҳам ошириши мумкин.", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Ушбу параметр тўпламдаги барча мавжуд файлларни ўчиради ва уларни янги юкланган файллар билан алмаштиради.", "This response was generated by \"{{model}}\"": "Бу жавоб \"{{модел}}\" томонидан яратилган", "This will delete": "Бу ўчирилади", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "Бу <стронг>{{NAME}}</стронг> ва <стронг>барча мазмунини</стронг> ўчириб ташлайди.", "This will delete all models including custom models": "Бу барча моделларни, шу жумладан махсус моделларни ўчириб ташлайди", "This will delete all models including custom models and cannot be undone.": "Бу барча моделларни, жу<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, махсус моделларни ҳам ўчириб ташлайди ва уларни ортга қайтариб бўлмайди.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Бу билимлар базасини қайта тиклайди ва барча файлларни синхронлаштиради. Давом этишни хоҳлайсизми?", "Thorough explanation": "Тўлиқ тушунтириш", "Thought for {{DURATION}}": "{{DURATION}} учун фикр", "Thought for {{DURATION}} seconds": "{{DURATION}} сония ўйладим", "Tika": "Тика", "Tika Server URL required.": "Тика Сервер УРЛ манзили талаб қилинади.", "Tiktoken": "Тиктокен", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Маслаҳат: Ҳар бир алмашти<PERSON><PERSON><PERSON><PERSON>ан кейин чат киритишидаги ёрлиқ тугмасини босиб кетма-кет бир нечта ўзгарувчи слотларни янгиланг.", "Title": "Сарлавҳа", "Title (e.g. Tell me a fun fact)": "Сарлавҳа (ма<PERSON><PERSON><PERSON><PERSON><PERSON>, менга қизиқарли фактни айтинг)", "Title Auto-Generation": "Сарлавҳани автоматик яратиш", "Title cannot be an empty string.": "Сарлавҳа бўш қатор бўлиши мумкин эмас.", "Title Generation": "Сарлавҳа яратиш", "Title Generation Prompt": "Сарлавҳа яратиш таклифи", "TLS": "ТЛС", "To access the available model names for downloading,": "Юклаб оли<PERSON> учун мавжуд модел номларига кириш учун,", "To access the GGUF models available for downloading,": "Юклаб олиш мумкин бўлган ГГУФ моделларига кириш учун,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "WебУИ-га кири<PERSON> учун администратор билан боғланинг. Администраторлар фойдаланувчи ҳолатини Администратор панелидан бошқариши мумкин.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Бу ерда билимлар базасини бириктириш учун аввал уларни “Билим” иш майдонига қўшинг.", "To learn more about available endpoints, visit our documentation.": "Мавжуд сўнгги нуқталар ҳақида кўпроқ билиш учун ҳужжатларимизга ташриф буюринг.", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Махфийлигингизни ҳимоя қилиш учун фикр-мулоҳазаларингиздан фақат рейтинглар, модел идентификаторлари, теглар ва мета-маълумотлар баҳам кўрилади — чат журналларингиз шахсий бўлиб қолади ва уларга киритилмайди.", "To select actions here, add them to the \"Functions\" workspace first.": "Бу ерда амалларни танлаш учун аввал уларни “Функсиялар” иш майдонига қўшинг.", "To select filters here, add them to the \"Functions\" workspace first.": "Бу ерда филтрларни танлаш учун аввал уларни “Функсиялар” иш майдонига қўшинг.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Бу ерда асбоблар тўпламини танлаш учун аввал уларни “Асбоблар” иш майдонига қўшинг.", "Toast notifications for new updates": "Янги янгилан<PERSON><PERSON>лар ҳақида билдиришномалар", "Today": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Toggle search": "Қидирувни алмаштириш", "Toggle settings": "Созламаларни алмаштириш", "Toggle sidebar": "Ён панелни алмаштириш", "Toggle whether current connection is active.": "", "Token": "Токен", "Too verbose": "Жуда батафсил", "Tool created successfully": "Асбоб муваффақиятли яратилди", "Tool deleted successfully": "Асбоб муваффақиятли ўчирилди", "Tool Description": "Асбоб тавсифи", "Tool ID": "Асбоб идентификатори", "Tool imported successfully": "Асбоб муваффақиятли импорт қилинди", "Tool Name": "Асбоб номи", "Tool Servers": "Асбоб серверлари", "Tool updated successfully": "Асбоб муваффақиятли янгиланди", "Tools": "Асбоблар", "Tools Access": "Асбобларга кириш", "Tools are a function calling system with arbitrary code execution": "Асбоблар - бу ўзбошимчалик билан код бажарилиши билан функсияларни чақириш тизими", "Tools Function Calling Prompt": "Асбоблар функсияси чақируви", "Tools have a function calling system that allows arbitrary code execution.": "Асбоблар ўзбошимчалик билан кодни бажаришга имкон берувчи функсияларни чақириш тизимига эга.", "Tools Public Sharing": "Умумий алмашиш воситалари", "Top K": "Юқори К", "Top K Reranker": "Топ К Реранкер", "Transformers": "Трансформаторлар", "Trouble accessing Ollama?": "Олламага киришда муаммо борми?", "Trust Proxy Environment": "Ишончли прокси муҳити", "TTS Model": "ТТС модели", "TTS Settings": "ТТС созламалари", "TTS Voice": "ТТС овози", "Type": "Тури", "Type Hugging Face Resolve (Download) URL": "Hugging Face Resolve (юклаб олиш) URL манзилини киритинг", "Uh-oh! There was an issue with the response.": "Диққат! Жавобда муаммо юз берди.", "UI": "Фойдаланувчи интерфейси", "Unarchive All": "Ҳаммасини архивдан чиқариш", "Unarchive All Archived Chats": "Барча архивланган суҳбатларни архивдан чиқариш", "Unarchive Chat": "Чатни архивдан чиқариш", "Unloads {{FROM_NOW}}": "{{FROM_NOW}} юклайди", "Unlock mysteries": "Сир<PERSON>а<PERSON><PERSON>и очинг", "Unpin": "<PERSON><PERSON><PERSON><PERSON>", "Unravel secrets": "Сир<PERSON>а<PERSON><PERSON>и очинг", "Untagged": "Бел<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ган", "Untitled": "Сарлавҳасиз", "Update": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Update and Copy Link": "Ҳаволани янгилаш ва нусхалаш", "Update for the latest features and improvements.": "Энг янги хусусиятлар ва яхшиланишлар учун янгиланг.", "Update password": "Паролни янгиланг", "Updated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Updated at": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Updated At": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "Кенгайтир<PERSON><PERSON><PERSON><PERSON><PERSON> имкония<PERSON><PERSON><PERSON><PERSON>, ж<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, махсус мавзулаштириш ва брендлаш ҳамда махсус ёрдам учун литсензияланган режага янгиланг.", "Upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Upload a GGUF model": "GGUF моделини юкланг", "Upload Audio": "Аудио юклаш", "Upload directory": "<PERSON><PERSON><PERSON>а<PERSON> о<PERSON><PERSON><PERSON> каталоги", "Upload files": "Файллар<PERSON><PERSON> юклаш", "Upload Files": "Файллар<PERSON><PERSON> юклаш", "Upload Pipeline": "Қувур линиясини юклаш", "Upload Progress": "<PERSON><PERSON><PERSON><PERSON><PERSON> жараёни", "URL": "УРЛ", "URL Mode": "УРЛ режими", "Usage": "Фойда<PERSON><PERSON><PERSON><PERSON>ш", "Use '#' in the prompt input to load and include your knowledge.": "Ўз билимларингизни юклаш ва киритиш учун сўровномада \"#\" дан фойдаланинг.", "Use Gravatar": "Граватар-дан фойдаланинг", "Use groups to group your users and assign permissions.": "Фойдаланувчиларингизни гуруҳлаш ва рухсатларни белгилаш учун гуруҳлардан фойдаланинг.", "Use Initials": "Бош ҳарфлардан фойдаланинг", "Use LLM": "ЛЛМ дан фойдаланинг", "Use no proxy to fetch page contents.": "Саҳифа мазмунини олиш учун прокси-сервердан фойдаланманг.", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "Саҳифа мазмунини олиш учун http_proxy ва https_proxy муҳит ўзгарувчилари томонидан белгиланган прокси-сервердан фойдаланинг.", "user": "фойдаланувчи", "User": "Фойдаланувчи", "User location successfully retrieved.": "Фойдаланувчи жойлашуви муваффақиятли олинди.", "User Webhooks": "Фойдаланувчи веб-ҳуклари", "Username": "Фойдаланувчи номи", "Users": "Фойдаланувчилар", "Using the default arena model with all models. Click the plus button to add custom models.": "Барча моделлар билан стандарт арена моделидан фойдаланиш. Махсус моделларни қўшиш учун ортиқча тугмасини босинг.", "Utilize": "Фойда<PERSON><PERSON><PERSON><PERSON>ш", "Valid time units:": "Яроқли вақт бирликлари:", "Valves": "Ёқиш/Ўчириш параметрлари", "Valves updated": "Ёқиш/Ўчириш параметрлари янгиланди", "Valves updated successfully": "Ёқиш/Ўчириш параметрлари муваффақиятли янгиланди", "variable": "ўзгарувчи ", "variable to have them replaced with clipboard content.": "уларни буфер контенти билан алмаштириш учун ўзгарувчи.", "Verify Connection": "Уланишни текширинг", "Verify SSL Certificate": "SSL сертификатини текширинг", "Version": "Версия", "Version {{selectedVersion}} of {{totalVersions}}": "{{selectedVersion}} версиясининг {{totalVersions}} версияси", "View Replies": "Жавобларни кўриш", "View Result from **{{NAME}}**": "**{{NAME}}** натижасини кўриш", "Visibility": "Кўриниш", "Vision": "Ви<PERSON><PERSON>н", "Voice": "Овоз", "Voice Input": "Овозли киритиш", "Voice mode": "Овоз режими", "Warning": "Огоҳлантириш", "Warning:": "Огоҳлантириш:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Огоҳлантириш: Буни ёқиш фойдаланувчиларга серверга ихтиёрий кодни юклаш имконини беради.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Огоҳлантириш: Агар сиз ўрнатиш моделингизни янгиласангиз ёки ўзгартирсангиз, барча ҳужжатларни қайта импорт қилишингиз керак бўлади.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Огоҳлантириш: Ж<PERSON><PERSON><PERSON>тер ижроси ўзбошимчалик билан код бажарилишини таъминлайди, бу хавфсизликка жиддий хавф туғдиради - жуда эҳтиёткорлик билан давом этинг.", "Web": "<PERSON>е<PERSON>", "Web API": "Web API", "Web Loader Engine": "Web Loader Engine", "Web Search": "Веб-қидирув", "Web Search Engine": "Веб қидирув тизими", "Web Search in Chat": "Чатда веб-қидирув", "Web Search Query Generation": "Веб-қидирув сўровларини яратиш", "Webhook URL": "Webhook УРЛ манзили", "WebUI Settings": "WebUI созламалари", "WebUI URL": "WebUI УРЛ", "WebUI will make requests to \"{{url}}\"": "WebUI “{{url}}” манзилига сўров юборади", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI “{{url}}/api/chat” манзилига сўров юборади", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI \"{{url}}/chat/completions\" манзилига сўров юборади", "Weight of BM25 Retrieval": "BM25 олишнинг оғирлиги", "What are you trying to achieve?": "Нимага эришмоқчисиз?", "What are you working on?": "Нима устида ишлаяпсиз?", "What's New in": "", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "Ёқилганда, модел реал вақт режимида ҳар бир чат хабарига жавоб беради ва фойдаланувчи хабар юбориши биланоқ жавоб ҳосил қилади. Ушбу режим жонли чат иловалари учун фойдалидир, лекин секинроқ ускунанинг ишлашига таъсир қилиши мумкин.", "wherever you are": "қаерда бўлсангиз ҳам", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "Чиқишни саҳифалаш керакми. Ҳар бир саҳифа горизонтал қоида ва саҳифа рақами билан ажратилади. Бирламчи параметрлар Фалсе.", "Whisper (Local)": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (маҳаллий)", "Why?": "Нега?", "Widescreen Mode": "Кенг экран режими", "Won": "Ғалаба қозонди", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "top-k билан бирга ишлайди. Юқори қиймат (ма<PERSON><PERSON><PERSON><PERSON><PERSON>, 0,95) матннинг хилма-хиллигига олиб келади, пастроқ қиймат эса (ма<PERSON><PERSON><PERSON><PERSON><PERSON>, 0,5) кўпроқ диққатли ва консерватив матнни яратади.", "Workspace": "Иш майдони", "Workspace Permissions": "Иш майдони рухсатномалари", "Write": "Ёзинг", "Write a prompt suggestion (e.g. Who are you?)": "Тезкор таклиф ёзинг (мас<PERSON><PERSON><PERSON><PERSON>, сиз кимсиз?)", "Write a summary in 50 words that summarizes [topic or keyword].": "[мавзу ёки калит сўзни] умумлаштирувчи 50 та сўздан иборат хулоса ёзинг.", "Write something...": "Бирор нарса ёзинг ...", "Yacy Instance URL": "<PERSON><PERSON>а<PERSON><PERSON>е УРЛ", "Yacy Password": "<PERSON><PERSON> парол", "Yacy Username": "<PERSON><PERSON> фойдаланувчи номи", "Yesterday": "Кеча", "You": "Сиз", "You are currently using a trial license. Please contact support to upgrade your license.": "Сиз ҳозирда синов литсензиясидан фойдаланмоқдасиз. Литсензиянгизни янгилаш учун қўллаб-қувватлаш хизматига мурожаат қилинг.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Сиз бир вақтнинг ўзида кўпи билан {{махCоунт}} та файл(лар) билан суҳбатлашишингиз мумкин.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Қуйидаги “Бошқариш” тугмаси орқали хотиралар қўшиш орқали ЛЛМлар билан ўзаро алоқаларингизни шахсийлаштиришингиз мумкин, бу уларни янада фойдалироқ ва сизга мослаштиради.", "You cannot upload an empty file.": "Сиз бўш файлни юклай олмайсиз.", "You do not have permission to upload files.": "Сизда файлларни юклаш учун рухсат йўқ.", "You have no archived conversations.": "Сизда архивланган суҳбатлар йўқ.", "You have shared this chat": "Сиз бу чатни баҳам кўрдингиз", "You're a helpful assistant.": "Сиз фойдали ёрдамчисиз.", "You're now logged in.": "Сиз энди тизимга кирдингиз.", "Your account status is currently pending activation.": "Ҳисобингиз ҳолати ҳозирда фаоллаштиришни кутмоқда.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Сизнинг барча ҳиссангиз тўғридан-тўғри плагин ишлаб чиқарувчисига ўтади; Open WebUI ҳеч қандай фоизни олмайди. Бироқ, танланган молиялаштириш платформаси ўз тўловларига эга бўлиши мумкин.", "Youtube": "Youtube", "Youtube Language": "Youtube тили", "Youtube Proxy URL": "Youtube прокси УРЛ"}