import React from 'react';
import { User, LogOut, Settings } from 'lucide-react';
import { useUserContext } from '@/context/UserContext';
import { useRouter } from 'next/router';

interface ChatHeaderProps {
  title?: string;
  onSettingsClick?: () => void;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({ 
  title = "RChat", 
  onSettingsClick 
}) => {
  const { authState, logout } = useUserContext();
  const router = useRouter();

  const handleLogout = () => {
    logout();
    router.push('/auth/login');
  };

  const handleProfileClick = () => {
    // Navigate to profile/account page when implemented
    console.log('Profile clicked');
  };

  return (
    <header className="fixed top-0 left-0 right-0 h-16 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 z-50">
      <div className="flex items-center justify-between h-full px-4">
        {/* Left side - Logo and title */}
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center">
            <span className="text-white font-bold text-sm">R</span>
          </div>
          <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
            {title}
          </h1>
        </div>

        {/* Right side - User menu */}
        <div className="flex items-center space-x-2">
          {onSettingsClick && (
            <button
              onClick={onSettingsClick}
              className="p-2 rounded-lg text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              title="Settings"
            >
              <Settings className="w-5 h-5" />
            </button>
          )}

          {authState.user && (
            <div className="flex items-center space-x-2">
              <button
                onClick={handleProfileClick}
                className="flex items-center space-x-2 p-2 rounded-lg text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                title="Profile"
              >
                <User className="w-5 h-5" />
                <span className="text-sm font-medium hidden sm:block">
                  {authState.user.name || authState.user.email}
                </span>
              </button>

              <button
                onClick={handleLogout}
                className="p-2 rounded-lg text-gray-600 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                title="Logout"
              >
                <LogOut className="w-5 h-5" />
              </button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default ChatHeader;
