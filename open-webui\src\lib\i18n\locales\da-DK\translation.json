{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' eller '-1' for ingen udløb", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(f.eks. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(f.eks. `sh webui.sh --api`)", "(latest)": "(seneste)", "(leave blank for to use commercial endpoint)": "", "{{ models }}": "{{ modeller }}", "{{COUNT}} Available Tools": "{{COUNT}} Tilgængelige værktøjer", "{{COUNT}} hidden lines": "{{COUNT}} sk<PERSON><PERSON><PERSON> linjer", "{{COUNT}} Replies": "{{COUNT}} svar", "{{user}}'s Chats": "{{user}}s chats", "{{webUIName}} Backend Required": "{{webUIName}} Backend kræves", "*Prompt node ID(s) are required for image generation": "*Prompt node ID(s) er påkrævet for at kunne generere billeder", "A new version (v{{LATEST_VERSION}}) is now available.": "En ny version (v{{LATEST_VERSION}}) er nu tilgængelig.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "En 'task model' bliver brugt til at opgaver såsom at generere overskrifter til chats eller internetsøgninger", "a user": "en bruger", "About": "Information", "Accept autocomplete generation / Jump to prompt variable": "Accepter generede autofuldførsel / Spring til prompt variabel", "Access": "<PERSON><PERSON><PERSON>", "Access Control": "Adgangskontrol", "Accessible to all users": "Tilgængelig for alle brugere", "Account": "Profil", "Account Activation Pending": "Aktivering af profil afventer", "Accurate information": "Profilinformation", "Actions": "<PERSON><PERSON>", "Activate": "Aktiver", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Aktiver denne kommando ved at skrive \"/{{COMMAND}}\" til chat input.", "Active Users": "Aktive brugere", "Add": "Tilføj", "Add a model ID": "Tilføj et model-ID", "Add a short description about what this model does": "En kort beskrivelse af hvad denne model gør", "Add a tag": "Tilføj et tag", "Add Arena Model": "Tilføj Arena Model", "Add Connection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Add Content": "<PERSON><PERSON><PERSON><PERSON><PERSON> indhold", "Add content here": "<PERSON><PERSON><PERSON><PERSON><PERSON> in<PERSON>old her", "Add Custom Parameter": "", "Add custom prompt": "Til<PERSON><PERSON><PERSON> en special-prompt", "Add Files": "<PERSON><PERSON><PERSON><PERSON><PERSON> filer", "Add Group": "Tilføj gruppe", "Add Memory": "<PERSON><PERSON><PERSON><PERSON><PERSON> h<PERSON>", "Add Model": "Tilføj model", "Add Reaction": "Til<PERSON>øj reaktion", "Add Tag": "<PERSON><PERSON><PERSON><PERSON><PERSON> tag", "Add Tags": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", "Add text content": "Tilføj tekst", "Add User": "<PERSON><PERSON><PERSON><PERSON><PERSON> bruger", "Add User Group": "Tilføj Brugergruppe", "Adjusting these settings will apply changes universally to all users.": "Ændringer af disse indstillinger har konsekvenser for alle brugere.", "admin": "administrator", "Admin": "Administrator", "Admin Panel": "Administrationspanel", "Admin Settings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Administratorer har adgang til alle værktøjer altid; brugere skal tilføjes værktøjer pr. model i hvert workspace.", "Advanced Parameters": "<PERSON><PERSON><PERSON>", "Advanced Params": "<PERSON><PERSON><PERSON>", "All": "Alle", "All Documents": "Alle dokumenter", "All models deleted successfully": "Alle modeller slettet uden fejl", "Allow Call": "<PERSON><PERSON> kald", "Allow Chat Controls": "<PERSON><PERSON> kontrol af chats", "Allow Chat Delete": "<PERSON><PERSON> sletning af chats", "Allow Chat Deletion": "<PERSON><PERSON> sletning af chats", "Allow Chat Edit": "<PERSON><PERSON> redigering af chats", "Allow Chat Export": "Tillad eksport af chats", "Allow Chat Share": "<PERSON><PERSON> deling af chats", "Allow Chat System Prompt": "", "Allow File Upload": "Tillad upload af fil", "Allow Multiple Models in Chat": "<PERSON><PERSON> flere modeller i chats", "Allow non-local voices": "Tillad ikke-lokale stemmer", "Allow Speech to Text": "Tillad tale til tekst", "Allow Temporary Chat": "<PERSON><PERSON> midlertidig chat", "Allow Text to Speech": "<PERSON>ad tekst til tale", "Allow User Location": "Tillad bruger-lokation", "Allow Voice Interruption in Call": "Tillad afbrydelser i stemme i opkald", "Allowed Endpoints": "Tilladte endpoints", "Allowed File Extensions": "Tilladte filtypenavne", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "Filtypenavne tilladte til upload. Adskil flere filtypenavne med komma. Lad den være tom for alle filtypenavne", "Already have an account?": "Har du allerede en profil?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "<PERSON><PERSON>", "Always Collapse Code Blocks": "Altid kollapsere kodeblokke", "Always Expand Details": "<PERSON><PERSON> u<PERSON>", "Always Play Notification Sound": "Afspil altid notifikationslyde", "Amazing": "Fantastisk", "an assistant": "en assistent", "Analyzed": "Analyseret", "Analyzing...": "Analyserer...", "and": "og", "and {{COUNT}} more": "og {{COUNT}} mere", "and create a new shared link.": "og lav et nyt link til deling", "Android": "Android", "API": "", "API Base URL": "API Base URL", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "API nøgle", "API Key created.": "API nøgle lavet", "API Key Endpoint Restrictions": "API nøgler endpoint forbehold", "API keys": "API nøgler", "API Version": "", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "gælder for alle brugere med \"bruger\" rolle", "April": "April", "Archive": "Arkiv", "Archive All Chats": "<PERSON><PERSON> alle chats", "Archived Chats": "Arkiverede chats", "archived-chat-export": "arkiveret-chat-eksport", "Are you sure you want to clear all memories? This action cannot be undone.": "Er du sikker på du vil rydde hele hukommelsen? Dette kan ikke gøres om.", "Are you sure you want to delete this channel?": "Er du sikker på du vil slette denne kanal?", "Are you sure you want to delete this message?": "Er du sikker på du vil slette denne besked?", "Are you sure you want to unarchive all archived chats?": "Er du sikker på du vil fjerne alle arkiverede chats?", "Are you sure?": "<PERSON>r du sikker?", "Arena Models": "Arena Modeller", "Artifacts": "Artifakter", "Ask": "<PERSON><PERSON><PERSON><PERSON>", "Ask a question": "Stil et spørgsmål", "Assistant": "Assistent", "Attach file from knowledge": "Vedhæft fil fra viden", "Attention to detail": "Detajleorientering", "Attribute for Mail": "Attribut for Mail", "Attribute for Username": "Attribut for brugernavn", "Audio": "Lyd", "August": "august", "Auth": "<PERSON><PERSON>", "Authenticate": "Autentificer", "Authentication": "Autentikation", "Auto": "Auto", "Auto-Copy Response to Clipboard": "Automatisk kopiering af svar til udklipsholder", "Auto-playback response": "Automatisk afspil svar", "Autocomplete Generation": "Genere automatisk fuldførsel", "Autocomplete Generation Input Max Length": "<PERSON><PERSON><PERSON><PERSON> længde for genereret autofuldførsel", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 Api Auth String", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 Base URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 Base URL er påkrævet.", "Available list": "Tilgængeli<PERSON> lister", "Available Tools": "Tilgængelige værktøj", "available!": "tilgængelig!", "Awful": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Azure AI Speech": "Azure AI Speech", "Azure Region": "Azure Region", "Back": "Tilbage", "Bad Response": "Problem i response", "Banners": "<PERSON><PERSON>", "Base Model (From)": "Base Model (Fra)", "before": "<PERSON>ør", "Being lazy": "At være doven", "Beta": "Beta", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Bocha Search API Key": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "Brave Search API nøgle", "By {{name}}": "Af {{name}}", "Bypass Embedding and Retrieval": "", "Bypass Web Loader": "", "Calendar": "<PERSON><PERSON><PERSON>", "Call": "Opkald", "Call feature is not supported when using Web STT engine": "Opkaldsfunktion er ikke understøttet for Web STT engine", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Capabilities": "<PERSON><PERSON><PERSON>", "Capture": "<PERSON> <PERSON>e eller screendump", "Capture Audio": "Optag lyd", "Certificate Path": "<PERSON>rt<PERSON><PERSON><PERSON> sti", "Change Password": "Skift password", "Channel Name": "Kanalnavn", "Channels": "<PERSON><PERSON><PERSON>", "Character": "<PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "Karaktergrænse for genereret autofuldførsel", "Chart new frontiers": "", "Chat": "Cha<PERSON>", "Chat Background Image": "<PERSON><PERSON>", "Chat Bubble UI": "Chat Bubble UI", "Chat Controls": "<PERSON><PERSON><PERSON>", "Chat direction": "Chat retning", "Chat Overview": "Chat overblik", "Chat Permissions": "<PERSON><PERSON>", "Chat Tags Auto-Generation": "Chat tags automatisk generering", "Chats": "Chats", "Check Again": "Tjek igen", "Check for updates": "<PERSON><PERSON><PERSON> efter op<PERSON>", "Checking for updates...": "<PERSON><PERSON><PERSON> efter op<PERSON>", "Choose a model before saving...": "Vælg en model før du gemmer", "Chunk Overlap": "Chunk overlap", "Chunk Size": "Chunk størrelse", "Ciphers": "Ciphers", "Citation": "Citat", "Citations": "Citater", "Clear memory": "Slet hukommelse", "Clear Memory": "Slet hukommelse", "click here": "klik her", "Click here for filter guides.": "<PERSON>lik her for filter guider", "Click here for help.": "Klik her for hjælp", "Click here to": "Klik her for at", "Click here to download user import template file.": "<PERSON>lik her for at downloade bruger import template fil.", "Click here to learn more about faster-whisper and see the available models.": "Klik her for at lære mere om faster-whisper og se tilgængelige modeller.", "Click here to see available models.": "Klik her for at se tilgængelige modeller.", "Click here to select": "Klik her for at vælge", "Click here to select a csv file.": "Klik her for at vælge en csv fil", "Click here to select a py file.": "Klik her for at vælge en py fil", "Click here to upload a workflow.json file.": "Klik her for at uploade en workflow.json fil", "click here.": "klik her.", "Click on the user role button to change a user's role.": "<PERSON><PERSON> på bruger ikonet for at ændre brugerens rolle.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Skriveadgang til udklipsholderen ikke tilladt. Tjek venligst indstillingerne i din browser for at give adgang.", "Clone": "Klon", "Clone Chat": "Klon chat", "Clone of {{TITLE}}": "Klon af {{TITLE}}", "Close": "Luk", "Close modal": "", "Close settings modal": "Luk dialogboks med indstillinger", "Code execution": "<PERSON><PERSON> kø<PERSON>l", "Code Execution": "<PERSON><PERSON> kø<PERSON>l", "Code Execution Engine": "Kode kørsel engine", "Code Execution Timeout": "<PERSON><PERSON> kø<PERSON> timeout", "Code formatted successfully": "Kode formateret korrekt", "Code Interpreter": "<PERSON><PERSON> interpreter", "Code Interpreter Engine": "Kode interpreter engine", "Code Interpreter Prompt Template": "Kode interpreter prompt template", "Collapse": "<PERSON><PERSON><PERSON>", "Collection": "<PERSON><PERSON>", "Color": "<PERSON><PERSON>", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI API Key", "ComfyUI Base URL": "ComfyUI Base URL", "ComfyUI Base URL is required.": "ComfyUI Base URL er påkrævet.", "ComfyUI Workflow": "ComfyUI Workflow", "ComfyUI Workflow Nodes": "ComfyUI Workflow Nodes", "Command": "Kommando", "Completions": "Completions", "Concurrent Requests": "Concurrent requests", "Configure": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Confirm": "Bekræft", "Confirm Password": "Bekræft password", "Confirm your action": "Bekræft din handling", "Confirm your new password": "Bekræft dit nye password", "Connect to your own OpenAI compatible API endpoints.": "Opret forbindelse til din egen OpenAI kompatible API endpoints.", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "<PERSON><PERSON><PERSON><PERSON> misly<PERSON>es", "Connection successful": "Forbindelse l<PERSON>", "Connection Type": "", "Connections": "For<PERSON><PERSON><PERSON>", "Connections saved successfully": "Forbindelser gemt", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "Kontakt din administrator for adgang til WebUI", "Content": "Indhold", "Content Extraction Engine": "", "Continue Response": "Fortsæt svar", "Continue with {{provider}}": "Fortsæt med {{provider}}", "Continue with Email": "Fortsæt med Email", "Continue with LDAP": "Fortsæt med LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Kontroller hvordan beskedens tekst bliver splittet til TTS requests. 'Punctuation' (tegnsætning) splitter i sætninger, 'paragraphs' splitter i paragraffer, og 'none' beholder beskeden som en samlet streng.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "<PERSON><PERSON><PERSON><PERSON>", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Copied": "<PERSON><PERSON><PERSON>", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "Link til deling kopieret til udklipsholder", "Copied to clipboard": "<PERSON><PERSON><PERSON> til udklipsholder", "Copy": "<PERSON><PERSON><PERSON>", "Copy Formatted Text": "Kopier formateret tekst", "Copy last code block": "<PERSON><PERSON><PERSON> seneste kode", "Copy last response": "<PERSON><PERSON><PERSON> senester svar", "Copy Link": "Kopier link", "Copy to clipboard": "Ko<PERSON>r til udklipsholder", "Copying to clipboard was successful!": "<PERSON><PERSON><PERSON> til udklipsholder!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "", "Create": "<PERSON><PERSON>", "Create a knowledge base": "Opret en videnbase", "Create a model": "Lav en model", "Create Account": "<PERSON><PERSON> profil", "Create Admin Account": "Opret administrator profil", "Create Channel": "<PERSON><PERSON> kanal", "Create Group": "<PERSON>ret gruppe", "Create Knowledge": "<PERSON><PERSON>", "Create new key": "Opret en ny nøgle", "Create new secret key": "Opret en ny hemmelig nøgle", "Create Note": "Opret note", "Create your first note by clicking on the plus button below.": "<PERSON><PERSON> din første note ved at klikke på plus knappen nedenfor.", "Created at": "Oprettet", "Created At": "Oprettet", "Created by": "Oprettet af", "CSV Import": "Importer CSV", "Ctrl+Enter to Send": "Ctrl+Enter til at sende", "Current Model": "Nuværende model", "Current Password": "Nuv<PERSON><PERSON>de password", "Custom": "Custom", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "Danger Zone", "Dark": "<PERSON><PERSON><PERSON>", "Database": "Database", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "December": "december", "Default": "Standard", "Default (Open AI)": "Standard (Open AI)", "Default (SentenceTransformers)": "Standard (SentenceTransformers)", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "Standard model", "Default model updated": "Standard model opdateret", "Default Models": "Standard modeller", "Default permissions": "Standard tilladelser", "Default permissions updated successfully": "Standard tilladelser opdateret", "Default Prompt Suggestions": "Standardforslag til prompt", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "Standard til ALLE", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "Brugers rolle som standard", "Delete": "Slet", "Delete a model": "Slet en model", "Delete All Chats": "Slet alle chats", "Delete All Models": "<PERSON>let alle modeller", "Delete chat": "Slet chat", "Delete Chat": "Slet chat", "Delete chat?": "Slet chat?", "Delete folder?": "Slet mappe?", "Delete function?": "Slet funktion?", "Delete Message": "Slet besked", "Delete message?": "Slet besked?", "Delete note?": "Slet note?", "Delete prompt?": "Slet prompt?", "delete this link": "slet dette link", "Delete tool?": "Slet værktøj?", "Delete User": "Slet bruger", "Deleted {{deleteModelTag}}": "Slettede {{deleteModelTag}}", "Deleted {{name}}": "Slettede {{name}}", "Deleted User": "<PERSON><PERSON><PERSON> bruger", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "Beskriv din videnbase og mål", "Description": "Beskrivelse", "Detect Artifacts Automatically": "Genkend artifakter automatisk", "Dictate": "", "Didn't fully follow instructions": "Fulgte ikke instruktioner", "Direct": "Direkte", "Direct Connections": "<PERSON><PERSON><PERSON> for<PERSON>", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "Direkte forbindelser tillader brugere at oprette forbindelse til deres egen OpenAI kompatible API endpoints.", "Direct Connections settings updated": "Direkte forbindelser indstillinger opdateret", "Direct Tool Servers": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "Inaktiv", "Discover a function": "Find en funktion", "Discover a model": "Find en model", "Discover a prompt": "Find en prompt", "Discover a tool": "Find et værktøj", "Discover how to use Open WebUI and seek support from the community.": "", "Discover wonders": "", "Discover, download, and explore custom functions": "Find, download og udforsk unikke funktioner", "Discover, download, and explore custom prompts": "Find, download og udforsk unikke prompts", "Discover, download, and explore custom tools": "Find, download og udforsk unikke værktøjer", "Discover, download, and explore model presets": "Find, download og udforsk modelindstillinger", "Dismissible": "<PERSON><PERSON> af<PERSON>s", "Display": "Vis", "Display Emoji in Call": "Vis emoji i chat", "Display the username instead of You in the Chat": "Vis brugernavn i stedet for Dig i chatten", "Displays citations in the response": "Vis citat i svaret", "Dive into knowledge": "<PERSON>s<PERSON>g viden", "Do not install functions from sources you do not fully trust.": "Lad være med at installere funktioner fra kilder, som du ikke stoler på.", "Do not install tools from sources you do not fully trust.": "Lad være med at installere værktøjer fra kilder, som du ikke stoler på.", "Docling": "", "Docling Server URL required.": "", "Document": "Dokument", "Document Intelligence": "", "Document Intelligence endpoint and key required.": "", "Documentation": "Dokumentation", "Documents": "Do<PERSON><PERSON><PERSON>", "does not make any external connections, and your data stays securely on your locally hosted server.": "laver ikke eksterne kald, og din data bliver sikkert på din egen lokalt hostede server.", "Domain Filter List": "<PERSON><PERSON><PERSON>e", "Don't have an account?": "Har du ikke en profil?", "don't install random functions from sources you don't trust.": "lad være med at installere tilfældige funktioner fra kilder, som du ikke stoler på.", "don't install random tools from sources you don't trust.": "lad være med at installere tilfældige værktøjer fra kilder, som du ikke stoler på.", "Don't like the style": "Kan du ikke lide stilen", "Done": "<PERSON><PERSON><PERSON><PERSON>", "Download": "Download", "Download as SVG": "Download som SVG", "Download canceled": "Download afbrudt", "Download Database": "Download database", "Drag and drop a file to upload or select a file to view": "<PERSON>r<PERSON><PERSON> og slip en fil for at uploade eller vælg en fil for at se", "Draw": "Tegn", "Drop any files here to upload": "Drop nogen filer her for at uploade", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "f.eks. '30s', '10m'. <PERSON><PERSON><PERSON> værdier er 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "f.eks. \"j<PERSON>\" eller en JSON schema", "e.g. 60": "f.eks. 60", "e.g. A filter to remove profanity from text": "", "e.g. en": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "e.g.) en,fr,de": "", "Edit": "<PERSON><PERSON>", "Edit Arena Model": "Rediger Arena Model", "Edit Channel": "Rediger kanal", "Edit Connection": "<PERSON><PERSON> forbindelse", "Edit Default Permissions": "Rediger standard tilladelser", "Edit Memory": "<PERSON><PERSON> hukommelse", "Edit User": "<PERSON>iger bruger", "Edit User Group": "Rediger brugergruppe", "Eject": "", "ElevenLabs": "ElevenLabs", "Email": "Email", "Embark on adventures": "Udforsk eventyr", "Embedding": "Embedding", "Embedding Batch Size": "Embedding <PERSON><PERSON> stø<PERSON>", "Embedding Model": "Embedding Model", "Embedding Model Engine": "Embedding Model engine", "Embedding model set to \"{{embedding_model}}\"": "Embedding model sat til \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Code Execution": "", "Enable Code Interpreter": "", "Enable Community Sharing": "Aktiver deling til Community", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "Aktiver rating af besked", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "Aktiver nye signups", "Enabled": "Aktiveret", "Endpoint URL": "", "Enforce Temporary Chat": "", "Enhance": "<PERSON><PERSON><PERSON>", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "<PERSON><PERSON><PERSON> for at din CSV-fil indeholder 4 kolonner i denne rækkefølge: Name, Email, Password, Role.", "Enter {{role}} message here": "Indtast {{role}} besked her", "Enter a detail about yourself for your LLMs to recall": "Indtast en detalje om dig selv, som dine LLMs kan huske", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "Indtast api-godkendelsesstreng (f.eks. brugernavn:adgangskode)", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter BM25 Weight": "", "Enter Bocha Search API Key": "", "Enter Brave Search API Key": "Indtast Brave Search API-nøgle", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "Indtast CFG-skala (f.eks. 7.0)", "Enter Chunk Overlap": "Indtast overlapning af tekststykker", "Enter Chunk Size": "Indtast størrelse af tekststykker", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter Datalab Marker API Key": "", "Enter description": "Indtast beskrivelse", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "", "Enter Document Intelligence Key": "", "Enter domains separated by commas (e.g., example.com,site.org)": "", "Enter Exa API Key": "", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter Github Raw URL": "Indtast Github Raw URL", "Enter Google PSE API Key": "Indtast Google PSE API-nøgle", "Enter Google PSE Engine Id": "Indtast Google PSE Engine ID", "Enter Image Size (e.g. 512x512)": "Indtast billedstørrelse (f.eks. 512x512)", "Enter Jina API Key": "", "Enter Jupyter Password": "", "Enter Jupyter Token": "", "Enter Jupyter URL": "", "Enter Kagi Search API Key": "", "Enter Key Behavior": "", "Enter language codes": "Indtast sprogkoder", "Enter Mistral API Key": "", "Enter Model ID": "Indtast model-ID", "Enter model tag (e.g. {{modelTag}})": "Indtast modelmærke (f.eks. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "Indtast antal trin (f.eks. 50)", "Enter Perplexity API Key": "", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "", "Enter reasoning effort": "", "Enter Sampler (e.g. Euler a)": "Indtast sampler (f.eks. Euler a)", "Enter Scheduler (e.g. Karras)": "Indtast scheduler (f.eks<PERSON>)", "Enter Score": "Indtast score", "Enter SearchApi API Key": "Indtast SearchApi API-nøgle", "Enter SearchApi Engine": "Indtast SearchApi-engine", "Enter Searxng Query URL": "Indtast Searxng-forespørgsels-URL", "Enter Seed": "Indtast seed", "Enter SerpApi API Key": "Indtast SerpApi API-nøgle", "Enter SerpApi Engine": "Indtast SerpApi-engine", "Enter Serper API Key": "Indtast Serper API-nøgle", "Enter Serply API Key": "Indtast Serply API-nøgle", "Enter Serpstack API Key": "Indtast Serpstack API-nøgle", "Enter server host": "Indtast server-host", "Enter server label": "Indtast server-label", "Enter server port": "Indtast server-port", "Enter Sougou Search API sID": "Indtast Sougou Search API sID", "Enter Sougou Search API SK": "Indtast Sougou Search API SK", "Enter stop sequence": "Indtast stopsekvens", "Enter system prompt": "Indtast systemprompt", "Enter system prompt here": "Indtast systemprompt her", "Enter Tavily API Key": "Indtast Tavily API-nøgle", "Enter Tavily Extract Depth": "Indtast Tavily Extract Depth", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "Indtast Tika Server URL", "Enter timeout in seconds": "Indtast timeout i sekunder", "Enter to Send": "Indtast for at sende", "Enter Top K": "Indtast Top K", "Enter Top K Reranker": "Indtast Top K Reranker", "Enter URL (e.g. http://127.0.0.1:7860/)": "Indtast URL (f.eks. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Indtast URL (f.eks. http://localhost:11434)", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your current password": "", "Enter Your Email": "Indtast din e-mail", "Enter Your Full Name": "Indtast dit fulde navn", "Enter your message": "Indtast din besked", "Enter your name": "Indtast dit navn", "Enter Your Name": "Indtast dit navn", "Enter your new password": "Indtast din nye adgangskode", "Enter Your Password": "Indtast din adgangskode", "Enter Your Role": "Indtast din rolle", "Enter Your Username": "Indtast dit brugernavn", "Enter your webhook URL": "Indtast din webhook URL", "Error": "<PERSON><PERSON><PERSON>", "ERROR": "FEJL", "Error accessing Google Drive: {{error}}": "<PERSON><PERSON><PERSON> ved adgang til Google Drive: {{error}}", "Error accessing media devices.": "<PERSON><PERSON>l ved adgang til medieenheder.", "Error starting recording.": "<PERSON><PERSON><PERSON> ved start af optagelse.", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "Fejl ved upload af fil: {{error}}", "Evaluations": "<PERSON><PERSON><PERSON><PERSON>", "Exa API Key": "Exa API-nøgle", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Eksempel: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Eksempel: ALL", "Example: mail": "Eksempel: mail", "Example: ou=users,dc=foo,dc=example": "Eksempel: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Eksempel: sAMAccountName eller uid eller userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "Du har overskredet antallet af pladser i din licens. Kontakt support for at øge antallet af pladser.", "Exclude": "Ekskluder", "Execute code for analysis": "", "Executing **{{NAME}}**...": "<PERSON><PERSON><PERSON> **{{NAME}}**...", "Expand": "<PERSON><PERSON><PERSON>", "Experimental": "Eksperimentel", "Explain": "<PERSON><PERSON>", "Explore the cosmos": "", "Export": "Eksportér", "Export All Archived Chats": "Eksportér alle arkiverede chats", "Export All Chats (All Users)": "Eksportér alle chats (alle brugere)", "Export chat (.json)": "Eksportér chat (.json)", "Export Chats": "Eksportér chats", "Export Config to JSON File": "Eksportér konfiguration til JSON-fil", "Export Functions": "Eksportér funk<PERSON>er", "Export Models": "Eksportér modeller", "Export Presets": "Eksportér indstillinger", "Export Prompt Suggestions": "", "Export Prompts": "Eksportér prompts", "Export to CSV": "Eksportér til CSV", "Export Tools": "Eksportér værktøjer", "External": "Ekstern", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "Ekstern Web Loader API-nøgle", "External Web Loader URL": "Ekstern Web Loader URL", "External Web Search API Key": "Ekstern Web Search API-nøgle", "External Web Search URL": "Ekstern Web Search URL", "Failed to add file.": "<PERSON><PERSON> ikke tilføje fil.", "Failed to connect to {{URL}} OpenAPI tool server": "<PERSON><PERSON> ikke forbinde til {{URL}} OpenAPI tool server", "Failed to copy link": "", "Failed to create API Key.": "Kunne ikke oprette API-nøgle.", "Failed to delete note": "<PERSON><PERSON> ikke slette note", "Failed to fetch models": "<PERSON><PERSON> ikke hente modeller", "Failed to load file content.": "<PERSON>nne ikke indlæse filindhold.", "Failed to read clipboard contents": "Kunne ikke læse indholdet af udklipsholderen", "Failed to save connections": "<PERSON><PERSON> ikke gemme forbindelser", "Failed to save models configuration": "<PERSON><PERSON> ikke gemme modeller konfiguration", "Failed to update settings": "Kunne ikke opdatere indstillinger", "Failed to upload file.": "<PERSON><PERSON> ikke uploade fil.", "Features": "Features", "Features Permissions": "", "February": "<PERSON><PERSON><PERSON>", "Feedback Details": "", "Feedback History": "", "Feedbacks": "<PERSON><PERSON><PERSON>", "Feel free to add specific details": "Du er velkommen til at tilføje specifikke detaljer", "File": "Fil", "File added successfully.": "<PERSON><PERSON> tilfø<PERSON>.", "File content updated successfully.": "Filens indhold er opdateret.", "File Mode": "Filtilstand", "File not found.": "Filen blev ikke fundet.", "File removed successfully.": "<PERSON><PERSON> fjernet.", "File size should not exceed {{maxSize}} MB.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> må ikke overstige {{maxSize}} MB.", "File Upload": "", "File uploaded successfully": "Fil uploadet.", "Files": "Filer", "Filter is now globally disabled": "Filter er nu globalt deaktiveret", "Filter is now globally enabled": "Filter er nu globalt aktiveret", "Filters": "Filtre", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Fingeraftryksspoofing registreret: Kan ikke bruge initialer som avatar. Bruger standard profilbillede.", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Fluidly stream large external response chunks": "Stream store eksterne svar chunks flydende", "Focus chat input": "Fokuser på chatinput", "Folder deleted successfully": "<PERSON><PERSON> f<PERSON>.", "Folder name cannot be empty.": "Mappenavn kan ikke være tom.", "Folder name updated successfully": "Mappenavn opdateret.", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "Fulgte instruktionerne perfekt", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "", "Form": "Formular", "Format your variables using brackets like this:": "Formater dine variable ved hjælp af klammer som dette:", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "", "Function": "", "Function Calling": "", "Function created successfully": "Funktion oprettet.", "Function deleted successfully": "<PERSON><PERSON> slettet.", "Function Description": "", "Function ID": "", "Function imported successfully": "", "Function is now globally disabled": "Funktionen er nu globalt deaktiveret", "Function is now globally enabled": "Funktionen er nu globalt aktiveret", "Function Name": "", "Function updated successfully": "Funktion opdateret.", "Functions": "<PERSON><PERSON><PERSON>", "Functions allow arbitrary code execution.": "Funktioner tillader kørsel af vilkårlig kode.", "Functions imported successfully": "Funktioner importeret.", "Gemini": "", "Gemini API Config": "", "Gemini API Key is required.": "", "General": "Generelt", "Generate": "<PERSON><PERSON>", "Generate an image": "Generer et billede", "Generate Image": "<PERSON><PERSON>", "Generate prompt pair": "Generer prompt par", "Generating search query": "Genererer søgeforespørgsel", "Generating...": "Genererer...", "Get started": "Kom i gang", "Get started with {{WEBUI_NAME}}": "Kom i gang med {{WEBUI_NAME}}", "Global": "Global", "Good Response": "<PERSON><PERSON> svar", "Google Drive": "Google Drive", "Google PSE API Key": "Google PSE API-nøgle", "Google PSE Engine Id": "Google PSE Engine-ID", "Group created successfully": "Gruppe oprettet.", "Group deleted successfully": "Gruppe slettet.", "Group Description": "Gruppe beskrivelse", "Group Name": "Gruppenavn", "Group updated successfully": "Gruppe opdateret.", "Groups": "Grupper", "Haptic Feedback": "Haptisk feedback", "Hello, {{name}}": "Hej {{name}}", "Help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "<PERSON><PERSON><PERSON><PERSON><PERSON> os med at oprette det bedste community-lederboard ved at dele din feedback-historik!", "Hex Color": "Hex farve", "Hex Color - Leave empty for default color": "Hex farve - Lad stå tomt for standard farve", "Hide": "Skjul", "Hide from Sidebar": "", "Hide Model": "Skjul model", "High Contrast Mode": "", "Home": "<PERSON><PERSON><PERSON>", "Host": "<PERSON><PERSON><PERSON>", "How can I help you today?": "<PERSON><PERSON><PERSON> kan jeg hjæ<PERSON>pe dig i dag?", "How would you rate this response?": "<PERSON><PERSON><PERSON> vurderer du dette svar?", "HTML": "", "Hybrid Search": "Hybrid søgning", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Jeg an<PERSON>, at jeg har læst og forstået konsekvenserne af min handling. Jeg er opmærksom på de risici, der er forbundet med at udføre vilkårlig kode, og jeg har verificeret kildens troværdighed.", "ID": "", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "", "Image": "<PERSON><PERSON>", "Image Compression": "Billedkomprimering", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "Billedgenerering", "Image Generation (Experimental)": "Billedgenerering (eksperimentel)", "Image Generation Engine": "Billedgenereringsengine", "Image Max Compression Size": "", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "Billedpromptgenerering", "Image Prompt Generation Prompt": "Billedpromptgenerering prompt", "Image Settings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Images": "<PERSON><PERSON><PERSON>", "Import": "", "Import Chats": "Importer chats", "Import Config from JSON File": "Importer konfiguration fra JSON-fil", "Import From Link": "", "Import Functions": "Importer funktioner", "Import Models": "Importer modeller", "Import Notes": "Importer noter", "Import Presets": "Importer Presets", "Import Prompt Suggestions": "", "Import Prompts": "Importer prompts", "Import Tools": "Importer værktøjer", "Include": "<PERSON><PERSON><PERSON><PERSON>", "Include `--api-auth` flag when running stable-diffusion-webui": "Inkluder `--api-auth` flag, når du kører stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Inkluder `--api` flag, når du kører stable-diffusion-webui", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "Info", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input commands": "Inputkommandoer", "Install from Github URL": "Installer fra Github URL", "Instant Auto-Send After Voice Transcription": "Øjeblikkelig automatisk afsendelse efter stemmetransskription", "Integration": "Integration", "Interface": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Invalid file content": "<PERSON><PERSON><PERSON><PERSON><PERSON> filindhold", "Invalid file format.": "Ugyldigt filformat.", "Invalid JSON file": "", "Invalid Tag": "Ugyldigt tag", "is typing...": "er i gang med at skrive...", "January": "<PERSON><PERSON><PERSON>", "Jina API Key": "Jina API-nøgle", "join our Discord for help.": "tilslut dig vores Discord for at få hjælp.", "JSON": "JSON", "JSON Preview": "JSON-forhåndsvisning", "July": "<PERSON><PERSON>", "June": "<PERSON><PERSON>", "Jupyter Auth": "", "Jupyter URL": "", "JWT Expiration": "JWT-udløb", "JWT Token": "JWT-token", "Kagi Search API Key": "", "Keep in Sidebar": "", "Key": "<PERSON><PERSON><PERSON>", "Keyboard shortcuts": "Tastaturgenveje", "Knowledge": "Viden", "Knowledge Access": "Videnadgang", "Knowledge created successfully.": "Viden oprettet.", "Knowledge deleted successfully.": "<PERSON><PERSON> slettet.", "Knowledge Public Sharing": "<PERSON><PERSON> off<PERSON>g deling", "Knowledge reset successfully.": "Viden nulstillet.", "Knowledge updated successfully": "Viden opdateret.", "Kokoro.js (Browser)": "Kokoro.js (Browser)", "Kokoro.js Dtype": "", "Label": "", "Landing Page Mode": "Landing Page-tilstand", "Language": "Sp<PERSON>", "Language Locales": "", "Languages": "", "Last Active": "Sidst aktiv", "Last Modified": "<PERSON><PERSON> ændret", "Last reply": "<PERSON><PERSON> svar", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "Lad stå tomt for ubegrænset", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "Lad stå tomt for at bruge standardprompten, eller indtast en brugerdefineret prompt", "Leave model field empty to use the default model.": "", "License": "Licens", "Light": "Lys", "Listening...": "Lyt<PERSON>...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "LLM'er kan lave fejl. Bekræft vigtige oplysninger.", "Loader": "Loader", "Loading Kokoro.js...": "Indlæser Kokoro.js...", "Local": "<PERSON><PERSON>", "Local Task Model": "", "Location access not allowed": "Adgang til placering ikke tilladt", "Lost": "<PERSON><PERSON>", "LTR": "LTR", "Made by Open WebUI Community": "Lavet af OpenWebUI Community", "Make password visible in the user interface": "", "Make sure to enclose them with": "<PERSON><PERSON><PERSON> for at omslutte dem med", "Make sure to export a workflow.json file as API format from ComfyUI.": "<PERSON><PERSON><PERSON> for at eksportere en workflow.json-fil som API-format fra ComfyUI.", "Manage": "Administrer", "Manage Direct Connections": "Administrer direkte forbindelser", "Manage Models": "Administrer modeller", "Manage Ollama": "Administ<PERSON>", "Manage Ollama API Connections": "Administrer Ollama API forbindelser", "Manage OpenAI API Connections": "Administrer OpenAI API forbindelser", "Manage Pipelines": "Administrer pipelines", "Manage Tool Servers": "", "March": "Marts", "Markdown": "", "Max Speakers": "", "Max Upload Count": "Maks. uploadantal", "Max Upload Size": "Maks. uploadstørrelse", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Højst 3 modeller kan downloades samtidigt. Prøv igen senere.", "May": "Maj", "Memories accessible by LLMs will be shown here.": "<PERSON><PERSON>, der er tilgængelige for LLM'er, vises her.", "Memory": "Hukommelse", "Memory added successfully": "Hukommelse tilføjet.", "Memory cleared successfully": "Hukommelse ryddet.", "Memory deleted successfully": "Hu<PERSON><PERSON><PERSON><PERSON> slettet.", "Memory updated successfully": "Hukommelse opdateret.", "Merge Responses": "<PERSON><PERSON> svar", "Merged Response": "<PERSON><PERSON><PERSON> svar", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "<PERSON><PERSON><PERSON>, du sender efter at have oprettet dit link, deles ikke. Brugere med URL'en vil kunne se den delte chat.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "Model '{{modelName}}' er blevet downloadet.", "Model '{{modelTag}}' is already in queue for downloading.": "Model '{{modelTag}}' er allerede i kø til download.", "Model {{modelId}} not found": "Model {{modelId}} ikke fundet", "Model {{modelName}} is not vision capable": "Model {{modelName}} under<PERSON><PERSON><PERSON> ik<PERSON>er", "Model {{name}} is now {{status}}": "Model {{name}} er nu {{status}}", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts file inputs": "", "Model accepts image inputs": "Model accepterer billedinput", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "Model oprettet!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Model filsystemsti registreret. Modelkortnavn er påkrævet til opdatering, kan ikke fortsætte.", "Model Filtering": "", "Model ID": "Model-ID", "Model IDs": "Model-ID'er", "Model Name": "Modelnavn", "Model not selected": "Model ikke valgt", "Model Params": "Modelparametre", "Model Permissions": "", "Model unloaded successfully": "", "Model updated successfully": "Model opdateret.", "Model(s) do not support file upload": "", "Modelfile Content": "Modelfilindhold", "Models": "Modeller", "Models Access": "", "Models configuration saved successfully": "", "Models Public Sharing": "", "Mojeek Search API Key": "", "more": "mere", "More": "<PERSON><PERSON>", "My Notes": "Mine noter", "Name": "Navn", "Name your knowledge base": "Nanvgiv din videnbase", "Native": "", "New Chat": "Ny chat", "New Folder": "Ny mappe", "New Function": "", "New Note": "Ny note", "New Password": "Ny adgangskode", "New Tool": "", "new-channel": "", "Next message": "<PERSON><PERSON><PERSON> besked", "No chats found for this user.": "", "No chats found.": "", "No content": "Intet indhold", "No content found": "Intet indhold fundet", "No content found in file.": "Intet indhold fundet i fil.", "No content to speak": "Intet indhold at tale", "No distance available": "", "No feedbacks found": "Ingen feedback fundet", "No file selected": "Ingen fil valgt", "No groups with access, add a group to grant access": "Ingen grupper med adgang, tilføj en gruppe for at give adgang", "No HTML, CSS, or JavaScript content found.": "Intet HTML-, CSS- eller JavaScript-indhold fundet.", "No inference engine with management support found": "Ingen inference-engine med støtte til administration fundet", "No knowledge found": "Ingen viden fundet", "No memories to clear": "Ingen hukommelser at ryde", "No model IDs": "Ingen model-ID'er", "No models found": "Ingen modeller fundet", "No models selected": "Ingen modeller valgt", "No Notes": "Ingen noter", "No results found": "Ingen resultater fundet", "No search query generated": "Ingen søgeforespørgsel genereret", "No source available": "<PERSON>gen kilde <PERSON>", "No users were found.": "Ingen brugere blev fundet.", "No valves to update": "Ingen ventiler at opdatere", "None": "Ingen", "Not factually correct": "<PERSON>kke faktu<PERSON>t korrekt", "Not helpful": "Ikke hjælpsom", "Note deleted successfully": "Note slettet", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Bemærk: <PERSON><PERSON> du angiver en minimumscore, returnerer søgningen kun dokumenter med en score, der er større end eller lig med minimumscoren.", "Notes": "Noter", "Notification Sound": "Notifikationslyd", "Notification Webhook": "", "Notifications": "Notifikationer", "November": "November", "OAuth ID": "OAuth-ID", "October": "Oktober", "Off": "<PERSON>a", "Okay, Let's Go!": "Okay, lad os gå!", "OLED Dark": "OLED Mørk", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "", "Ollama Version": "Ollama-version", "On": "Til", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "Kun alfanumeriske tegn og bindestreger er tilladt", "Only alphanumeric characters and hyphens are allowed in the command string.": "Kun alfanumeriske tegn og bindestreger er tilladt i kommandostrengen.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "<PERSON>n samlinger kan redigeres, opret en ny vidensbase for at redigere/tilføje dokumenter.", "Only markdown files are allowed": "Kun markdown-filer er tilladt", "Only select users and groups with permission can access": "Kun valgte brugere og grupper med tilladelse kan tilgå", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Ups! URL'en ser ud til at være ugyldig. Tjek den igen, og prøv igen.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Ups! Der er filer, der stadig uploades. Vent, til uploaden er færdig.", "Oops! There was an error in the previous response.": "Ups! Der var en fejl i det tidligere svar.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Ups! Du bruger en metode, der ikke understøttes (kun frontend). Kør WebUI fra backend.", "Open file": "Åbn fil", "Open in full screen": "Åbn i fuld skærm", "Open modal to configure connection": "", "Open new chat": "Åbn ny chat", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "Open WebUI bruger faster-whisper internt.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Open WebUI-version (v{{OPEN_WEBUI_VERSION}}) er lavere end den krævede version (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API-konfiguration", "OpenAI API Key is required.": "OpenAI API-nøgle er påkrævet.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "OpenAI URL/nøgle påkrævet.", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "eller", "Organize your users": "", "Other": "And<PERSON>", "OUTPUT": "OUTPUT", "Output format": "Outputformat", "Output Format": "", "Overview": "Oversigt", "page": "side", "Paginate": "", "Parameters": "", "Password": "Adgangskode", "Paste Large Text as File": "Indsæt store tekster som fil", "PDF document (.pdf)": "PDF-dokument (.pdf)", "PDF Extract Images (OCR)": "Udtræk billeder fra PDF (OCR)", "pending": "a<PERSON><PERSON><PERSON>", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "Tilladelse nægtet ved adgang til medieenheder", "Permission denied when accessing microphone": "Tilladelse nægtet ved adgang til mikrofon", "Permission denied when accessing microphone: {{error}}": "Tilladelse nægtet ved adgang til mikrofon: {{error}}", "Permissions": "<PERSON><PERSON><PERSON><PERSON>", "Perplexity API Key": "", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "Personalisering", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "Fastgør", "Pinned": "Fastgjort", "Pioneer insights": "", "Pipeline deleted successfully": "Pipeline slettet.", "Pipeline downloaded successfully": "Pipeline downloadet.", "Pipelines": "Pipelines", "Pipelines Not Detected": "Pipelines ikke registreret", "Pipelines Valves": "Pipelines-ventiler", "Plain text (.md)": "<PERSON><PERSON><PERSON><PERSON> (.md)", "Plain text (.txt)": "<PERSON><PERSON><PERSON><PERSON> te<PERSON> (.txt)", "Playground": "Legeplads", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "Gennemgå omhyggeligt følge<PERSON> advar<PERSON>:", "Please do not close the settings page while loading the model.": "Luk ik<PERSON> in<PERSON>, mens modellen indlæses.", "Please enter a prompt": "Indtast en prompt", "Please enter a valid path": "Indtast en gyldig sti", "Please enter a valid URL": "Indtast en gyldig URL", "Please fill in all fields.": "<PERSON><PERSON><PERSON><PERSON> alle felter.", "Please select a model first.": "<PERSON><PERSON><PERSON><PERSON> en model først.", "Please select a model.": "<PERSON><PERSON><PERSON><PERSON> en model.", "Please select a reason": "Væ<PERSON>g en årsag", "Port": "Port", "Positive attitude": "Positiv holdning", "Prefix ID": "Prefix ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Prefix ID bruges til at undgå konflikter med andre forbindel<PERSON> ved at tilføje et prefix til model-ID'erne - lad være tom for at deaktivere", "Prevent file creation": "", "Preview": "", "Previous 30 days": "Seneste 30 dage", "Previous 7 days": "Seneste 7 dage", "Previous message": "<PERSON><PERSON><PERSON> besked", "Private": "Privat", "Profile Image": "<PERSON><PERSON><PERSON><PERSON>", "Prompt": "Prompt", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (f.eks. <PERSON> mig en sjov fakta om Romerriget)", "Prompt Autocompletion": "", "Prompt Content": "Prompt<PERSON><PERSON>", "Prompt created successfully": "Prompt oprettet", "Prompt suggestions": "Promptforslag", "Prompt updated successfully": "Prompt opdateret", "Prompts": "Prompts", "Prompts Access": "Prompts adgang", "Prompts Public Sharing": "Prompts off<PERSON><PERSON><PERSON> deling", "Public": "Offentlig", "Pull \"{{searchValue}}\" from Ollama.com": "Hent \"{{searchValue}}\" fra Ollama.com", "Pull a model from Ollama.com": "Hent en model fra Ollama.com", "Query Generation Prompt": "", "RAG Template": "RAG-skabelon", "Rating": "Rating", "Re-rank models by topic similarity": "", "Read": "<PERSON><PERSON><PERSON>", "Read Aloud": "<PERSON><PERSON><PERSON>", "Reason": "", "Reasoning Effort": "", "Record": "Optag", "Record voice": "Optag stemme", "Redirecting you to Open WebUI Community": "Omdirigerer dig til OpenWebUI Community", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "<PERSON><PERSON><PERSON> til dig selv som \"Bruger\" (f.eks. \"Bruger lærer spansk\")", "References from": "", "Refused when it shouldn't have": "<PERSON><PERSON><PERSON>, n<PERSON><PERSON> den ikke burde have været det", "Regenerate": "<PERSON><PERSON><PERSON>", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "Udgivelsesnoter", "Releases": "", "Relevance": "", "Relevance Threshold": "", "Remove": "<PERSON><PERSON><PERSON>", "Remove {{MODELID}} from list.": "", "Remove Model": "Fjern model", "Remove this tag from list": "", "Rename": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reorder Models": "<PERSON><PERSON><PERSON> modeller", "Reply in Thread": "<PERSON><PERSON> i tråd", "Reranking Engine": "", "Reranking Model": "Omarrangeringsmodel", "Reset": "Nulstil", "Reset All Models": "Nulstil alle modeller", "Reset Upload Directory": "Nulstil uploadmappe", "Reset Vector Storage/Knowledge": "", "Reset view": "Nulstil visning", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Svarnotifikationer kan ikke aktiveres, da webstedets tilladelser er blevet nægtet. <PERSON><PERSON><PERSON>g dine browserindstillinger for at give den nødvendige adgang.", "Response splitting": "Svaropdeling", "Response Watermark": "", "Result": "Resultat", "Retrieval": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "<PERSON><PERSON>", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "<PERSON><PERSON><PERSON>", "Running": "<PERSON><PERSON><PERSON>", "Save": "Gem", "Save & Create": "Gem og opret", "Save & Update": "Gem og opdater", "Save As Copy": "Gem som kopi", "Save Tag": "Gem tag", "Saved": "Gemt", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Lagring af chatlogs direkte i din browsers lager understøttes ikke længere. Download og slet dine chatlogs ved at klikke på knappen nedenfor. Du kan nemt importere dine chatlogs til backend igennem", "Scroll On Branch Change": "", "Search": "<PERSON><PERSON><PERSON>", "Search a model": "<PERSON><PERSON><PERSON> efter en model", "Search Base": "", "Search Chats": "<PERSON>øg i chats", "Search Collection": "Søg i samling", "Search Filters": "Søg i filtre", "search for tags": "", "Search Functions": "Søg i funktioner", "Search Knowledge": "Søg i viden", "Search Models": "Søg i modeller", "Search options": "", "Search Prompts": "<PERSON><PERSON>g i prompts", "Search Result Count": "Antal søgeresultater", "Search the internet": "Søg internettet", "Search Tools": "Søg i værktøjer", "SearchApi API Key": "SearchApi API-nøgle", "SearchApi Engine": "SearchApi-engine", "Searched {{count}} sites": "Søgte {{count}} sider", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON> efter \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON> i viden efter \"{{searchQuery}}\"", "Searching the web...": "S<PERSON>ger på internettet...", "Searxng Query URL": "Searxng forespørgsels-URL", "See readme.md for instructions": "Se readme.md for instruktioner", "See what's new": "Se, hvad der er nyt", "Seed": "Seed", "Select a base model": "Vælg en basemodel", "Select a engine": "Vælg en engine", "Select a function": "<PERSON>æ<PERSON><PERSON> en funktion", "Select a group": "Vælg en gruppe", "Select a model": "Vælg en model", "Select a pipeline": "Vælg en pipeline", "Select a pipeline url": "Vælg en pipeline-URL", "Select a tool": "Vælg et værktøj", "Select an auth method": "", "Select an Ollama instance": "", "Select Engine": "Vælg engine", "Select Knowledge": "<PERSON><PERSON><PERSON><PERSON> viden", "Select only one model to call": "<PERSON><PERSON><PERSON><PERSON> kun én model at kalde", "Selected model(s) do not support image inputs": "Valgte model(ler) understøtter ikke billed<PERSON>put", "Semantic distance to query": "", "Send": "Send", "Send a Message": "Send en besked", "Send message": "Send besked", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Sender `stream_options: { include_usage: true }` i forespørgslen.\nUnderstøttede udbydere vil returnere tokenforbrugsinformation i svaret, når det er indstillet.", "September": "September", "SerpApi API Key": "", "SerpApi Engine": "", "Serper API Key": "Serper API-nøgle", "Serply API Key": "Serply API-nøgle", "Serpstack API Key": "Serpstack API-nøgle", "Server connection verified": "Serverforbindelse bekræftet", "Set as default": "Indstil som standard", "Set CFG Scale": "Indstil CFG-skala", "Set Default Model": "Indstil standardmodel", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Indstil indlejringsmodel (f.eks. {{model}})", "Set Image Size": "<PERSON><PERSON><PERSON>", "Set reranking model (e.g. {{model}})": "Indstil omarrangeringsmodel (f.eks. {{model}})", "Set Sampler": "Indstil sampler", "Set Scheduler": "Indstil scheduler", "Set Steps": "Indstil trin", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "Indstil stemme", "Set whisper model": "", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "<PERSON><PERSON><PERSON><PERSON>", "Settings saved successfully!": "Indstillinger gemt!", "Share": "Del", "Share Chat": "Del chat", "Share to Open WebUI Community": "Del til OpenWebUI Community", "Sharing Permissions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "Vis", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "Vis <PERSON><PERSON><PERSON><PERSON> i overlay for ventende konto", "Show All": "", "Show Less": "", "Show Model": "Vis model", "Show shortcuts": "<PERSON>is genveje", "Show your support!": "Vis din støtte!", "Showcased creativity": "Udstillet kreativitet", "Sign in": "Log ind", "Sign in to {{WEBUI_NAME}}": "Log ind på {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "Log ind på {{WEBUI_NAME}} med LDAP", "Sign Out": "Log ud", "Sign up": "Tilmeld dig", "Sign up to {{WEBUI_NAME}}": "Tilmeld dig {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "", "Signing in to {{WEBUI_NAME}}": "Logger ind på {{WEBUI_NAME}}", "sk-1234": "sk-1234", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "<PERSON><PERSON>", "Speech Playback Speed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Speech recognition error: {{error}}": "Talegenkendelsesfejl: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "Tale-til-tekst-engine", "Stop": "Stop", "Stop Generating": "", "Stop Sequence": "Stopsekvens", "Stream Chat Response": "Stream chatsvar", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "STT-model", "STT Settings": "STT-indstillinger", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "Under<PERSON>l (f.eks. om Romerriget)", "Success": "Succes", "Successfully updated.": "Opdateret.", "Suggested": "Foreslået", "Support": "Support", "Support this plugin:": "<PERSON><PERSON><PERSON> dette plugin:", "Supported MIME Types": "", "Sync directory": "Synkroniser mappe", "System": "System", "System Instructions": "", "System Prompt": "Systemprompt", "Tags": "", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "Tal til model", "Tap to interrupt": "Tryk for at afbryde", "Task Model": "", "Tasks": "", "Tavily API Key": "Tavily API-nøgle", "Tavily Extract Depth": "", "Tell us more:": "<PERSON><PERSON><PERSON> os mere:", "Temperature": "Temperatur", "Temporary Chat": "<PERSON><PERSON><PERSON>dig chat", "Text Splitter": "", "Text-to-Speech": "", "Text-to-Speech Engine": "Tekst-til-tale-engine", "Thanks for your feedback!": "Tak for din feedback!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Udviklerne bag dette plugin er passionerede frivillige fra fællesskabet. Hvis du finder dette plugin nyttigt, kan du overveje at bidrage til dets udvikling.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Den maksimale filstørrelse i MB. Hvis filstørrelsen overstiger denne græ<PERSON>, uploades filen ikke.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Det maksimale antal filer, der kan bruges på én gang i chatten. Hvis antallet af filer overstiger denne græ<PERSON>, uploades filerne ikke.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Scoren skal være en værdi mellem 0,0 (0%) og 1,0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "<PERSON><PERSON>", "Thinking...": "Tæ<PERSON><PERSON>...", "This action cannot be undone. Do you wish to continue?": "<PERSON><PERSON> handling kan ikke fortrydes. Vil du fortsætte?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "Denne kanal blev oprettet den {{createdAt}}. Dette er det helt første i kanalen {{channelName}}.", "This chat won't appear in history and your messages will not be saved.": "Denne chat vil ikke vises i historikken, og dine beskeder vil ikke blive gemt.", "This chat won’t appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "<PERSON><PERSON> si<PERSON>, at dine værdifulde samtaler gemmes sikkert i din backend-database. Tak!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Dette er en eksperimentel funktion, den fungerer muligvis ikke som forventet og kan ændres når som helst.", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "<PERSON>ne indstilling sletter alle eksisterende filer i samlingen og erstatter dem med nyligt uploadede filer.", "This response was generated by \"{{model}}\"": "<PERSON><PERSON> svar blev genereret af \"{{model}}\"", "This will delete": "<PERSON><PERSON> vil slette", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "<PERSON><PERSON> vil slette <strong>{{NAME}}</strong> og <strong>alt dens indhold</strong>.", "This will delete all models including custom models": "<PERSON><PERSON> vil slette alle modeller, inklusive brugerdefinerede modeller", "This will delete all models including custom models and cannot be undone.": "<PERSON><PERSON> vil slette alle modeller, inklusive brugerdefinerede modeller og kan ikke fortrydes.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Dette vil nulstille vidensbasen og synkronisere alle filer. Vil du fortsætte?", "Thorough explanation": "<PERSON><PERSON><PERSON><PERSON> forklaring", "Thought for {{DURATION}}": "Tanker for {{DURATION}}", "Thought for {{DURATION}} seconds": "Tanker for {{DURATION}} sekunder", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Tika-server-URL påkrævet.", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Tip: <PERSON><PERSON><PERSON> flere variabelpladser fortløbende ved at trykke på tabulatortasten i chatinput efter hver udskiftning.", "Title": "Titel", "Title (e.g. Tell me a fun fact)": "Titel (f.eks. <PERSON> mig en sjov kendsgerning)", "Title Auto-Generation": "Automatisk titelgenerering", "Title cannot be an empty string.": "Titel kan ikke være en tom streng.", "Title Generation": "Titel Generation", "Title Generation Prompt": "Prompt til titelge<PERSON>ering", "TLS": "TLS", "To access the available model names for downloading,": "For at få adgang til de tilgængelige modelnavne til download,", "To access the GGUF models available for downloading,": "For at få adgang til de GGUF-modeller, der er tilgængelige til download,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "For at få adgang til WebUI skal du kontakte administratoren. Administratorer kan administrere brugerstatus fra administrationspanelet.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "For at vedhæfte vidensbase her skal du først tilføje dem til \"Viden\"-arbejdsområdet.", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "For at vælge handlinger her skal du først tilføje dem til \"Funktioner\"-arbejdsområdet.", "To select filters here, add them to the \"Functions\" workspace first.": "For at vælge filtre her skal du først tilføje dem til \"Funktioner\"-arbejdsområdet.", "To select toolkits here, add them to the \"Tools\" workspace first.": "For at vælge værktøjssæt her skal du først tilføje dem til \"Værktøjer\"-arbejdsområdet.", "Toast notifications for new updates": "", "Today": "I dag", "Toggle search": "", "Toggle settings": "<PERSON><PERSON> indstillinger", "Toggle sidebar": "Skift sidebjælke", "Toggle whether current connection is active.": "", "Token": "", "Too verbose": "For ordrigt", "Tool created successfully": "Værktøj oprettet.", "Tool deleted successfully": "<PERSON>ærk<PERSON><PERSON><PERSON> s<PERSON>t.", "Tool Description": "Værktøjsbeskrivelse", "Tool ID": "Værktøj-ID", "Tool imported successfully": "Værktøj importeret.", "Tool Name": "Værktøjsnavn", "Tool Servers": "Værktøjsserverer", "Tool updated successfully": "Værktøj opdateret.", "Tools": "<PERSON><PERSON><PERSON>tøjer", "Tools Access": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Tools are a function calling system with arbitrary code execution": "Værktøjer er et funktionkaldssystem med vilkårlig kodeudførelse", "Tools Function Calling Prompt": "Værktøjs Funktionkaldprompt", "Tools have a function calling system that allows arbitrary code execution.": "Værktøjer har et funktionkaldssystem, der tillader vilkårlig kodeudførelse.", "Tools Public Sharing": "Værktøjer Offentlig Deling", "Top K": "Top K", "Top K Reranker": "", "Transformers": "Transformers", "Trouble accessing Ollama?": "<PERSON>er med at få adgang til Ollama?", "Trust Proxy Environment": "Stol på Proxymiljø", "TTS Model": "TTS-model", "TTS Settings": "TTS-inds<PERSON>linger", "TTS Voice": "TTS-stemme", "Type": "Type", "Type Hugging Face Resolve (Download) URL": "Indtast Hugging Face Resolve (Download) URL", "Uh-oh! There was an issue with the response.": "Uh-oh! Der var et problem det det svar.", "UI": "UI", "Unarchive All": "Udpak alle arkiver", "Unarchive All Archived Chats": "Udpak alle arkiverede chats", "Unarchive Chat": "Fjern chat fra arkiv", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "<PERSON><PERSON><PERSON> op for mysterier", "Unpin": "Frig<PERSON><PERSON>", "Unravel secrets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Untagged": "<PERSON><PERSON>æ<PERSON>", "Untitled": "Unavngivet", "Update": "Opdater", "Update and Copy Link": "Opdater og kopier link", "Update for the latest features and improvements.": "<PERSON><PERSON>r for at få de nyeste funktioner og forbedringer.", "Update password": "Opdater adgangskode", "Updated": "<PERSON><PERSON><PERSON>", "Updated at": "Opdateret kl.", "Updated At": "<PERSON><PERSON><PERSON>.", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "Opgrader til en betalingsplan for at få adgang til udvidede funktioner, herunder tilpasning af tema og branding samt dedikeret support.", "Upload": "Upload", "Upload a GGUF model": "Upload en GGUF-model", "Upload Audio": "Upload lyd", "Upload directory": "Uploadmappe", "Upload files": "Upload filer", "Upload Files": "Upload filer", "Upload Pipeline": "Upload pipeline", "Upload Progress": "Uploadfremdrift", "URL": "URL", "URL Mode": "URL-tilstand", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "Brug '#' i promptinput for at indlæse og inkludere din viden.", "Use Gravatar": "Brug Gravatar", "Use groups to group your users and assign permissions.": "Brug grupper til at gruppere dine brugere og tildele rettigheder.", "Use Initials": "Brug initialer", "Use LLM": "", "Use no proxy to fetch page contents.": "Brug ingen proxy til at hente sideindhold.", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "bruger", "User": "Bruger:", "User location successfully retrieved.": "Brugerplacering hentet.", "User Webhooks": "<PERSON><PERSON><PERSON>", "Username": "Brugernavn", "Users": "Brugere", "Using the default arena model with all models. Click the plus button to add custom models.": "Brug den standard Arena-model med alle modeller. Klik på plusknappen for at tilføje brugerdefinerede modeller.", "Utilize": "<PERSON><PERSON><PERSON>", "Valid time units:": "Gyldi<PERSON> tids<PERSON>er:", "Valves": "<PERSON><PERSON><PERSON>", "Valves updated": "Ventiler opdateret", "Valves updated successfully": "Ventiler opdateret.", "variable": "variabel", "variable to have them replaced with clipboard content.": "variabel for at få dem erstattet med indholdet af udklipsholderen.", "Verify Connection": "<PERSON>eri<PERSON><PERSON> for<PERSON><PERSON><PERSON>", "Verify SSL Certificate": "Verificer SSL-certifikat", "Version": "Version", "Version {{selectedVersion}} of {{totalVersions}}": "Version {{selectedVersion}} af {{totalVersions}}", "View Replies": "<PERSON><PERSON> svar", "View Result from **{{NAME}}**": "Vis resultat fra **{{NAME}}**", "Visibility": "<PERSON><PERSON><PERSON>gh<PERSON>", "Vision": "", "Voice": "<PERSON><PERSON><PERSON>", "Voice Input": "Stemme Input", "Voice mode": "", "Warning": "<PERSON><PERSON><PERSON>", "Warning:": "<PERSON><PERSON><PERSON>:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Advarsel: <PERSON><PERSON> du aktiverer <PERSON>, vil brugerne kunne uploade vilkårlig kode på serveren.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Advarsel: <PERSON><PERSON> du opdaterer eller ændrer din indlejringsmodel, skal du importere alle dokumenter igen.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Advarsel: Jupyter-udførelse gør det muligt at udføre vilkårlig kode, hvilket udfordrer alvorlige sikkerhedsrisici - fortsæt med ekstremt omhyggelighed.", "Web": "Web", "Web API": "Web API", "Web Loader Engine": "", "Web Search": "Websøgning", "Web Search Engine": "Websøgemaskine", "Web Search in Chat": "Websøgning i chat", "Web Search Query Generation": "", "Webhook URL": "Webhook-URL", "WebUI Settings": "WebUI-indstillinger", "WebUI URL": "", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Weight of BM25 Retrieval": "", "What are you trying to achieve?": "Hvad prøver du at opnå?", "What are you working on?": "Hvad arbejder du på?", "What's New in": "Nyheder i", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "hvad end du er", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "Whisper (lokal)", "Why?": "Hvorfor?", "Widescreen Mode": "Widescreen-tilstand", "Won": "<PERSON><PERSON>", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "Arbejdsområde", "Workspace Permissions": "Arb<PERSON>ds<PERSON><PERSON><PERSON><PERSON>tti<PERSON>", "Write": "Skriv", "Write a prompt suggestion (e.g. Who are you?)": "Skriv et promptforslag (f.eks. Hvem er du?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Skriv en opsummering på 50 ord, der opsummerer [emne eller sø<PERSON>].", "Write something...": "Skriv noget...", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "<PERSON> går", "You": "<PERSON>", "You are currently using a trial license. Please contact support to upgrade your license.": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Du kan kun chatte med maksimalt {{maxCount}} fil(er) ad gangen.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Du kan personliggøre dine interaktioner med LLM'er ved at tilføje minder via knappen 'Administrer' nedenfor, hvilket gør dem mere nyttige og skræddersyet til dig.", "You cannot upload an empty file.": "Du kan ikke uploade en tom fil", "You do not have permission to upload files.": "Du har ikke rettighed til at uploade filer.", "You have no archived conversations.": "Du har ingen arkiverede samtaler.", "You have shared this chat": "Du har delt denne chat", "You're a helpful assistant.": "Du er en hjælpsom assistent.", "You're now logged in.": "Du er nu logget ind.", "Your account status is currently pending activation.": "Din kontostatus afventer i øjeblikket aktivering.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "<PERSON><PERSON> dit bidrag går direkte til plugin-udvikleren; Open WebUI tager ikke nogen procentdel. Den valgte finansieringsplatform kan dog have sine egne gebyrer.", "Youtube": "Youtube", "Youtube Language": "Youtube sprog", "Youtube Proxy URL": "Youtube Proxy URL"}