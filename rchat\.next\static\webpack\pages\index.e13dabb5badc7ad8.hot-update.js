"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/lib/chatService.ts":
/*!********************************!*\
  !*** ./src/lib/chatService.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatService: function() { return /* binding */ ChatService; }\n/* harmony export */ });\n/* harmony import */ var _config_chatConfig__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/chatConfig */ \"./src/config/chatConfig.ts\");\n\nclass ChatService {\n    /**\n   * Send a message to the R2R agent and get a response\n   */ async sendMessage(message, conversationId, messageHistory) {\n        try {\n            const config = (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_0__.loadChatConfig)();\n            // Prepare the search settings based on configuration\n            const chunkSearchSettings = {\n                indexMeasure: config.vectorSearch.indexMeasure,\n                enabled: config.vectorSearch.enabled,\n                ...config.vectorSearch.probes && {\n                    probes: config.vectorSearch.probes\n                },\n                ...config.vectorSearch.efSearch && {\n                    efSearch: config.vectorSearch.efSearch\n                }\n            };\n            const hybridSearchSettings = {\n                ...config.hybridSearch.fullTextWeight && {\n                    fulltextWeight: config.hybridSearch.fullTextWeight\n                },\n                ...config.hybridSearch.semanticWeight && {\n                    semanticWeight: config.hybridSearch.semanticWeight\n                },\n                ...config.hybridSearch.fullTextLimit && {\n                    fulltextLimit: config.hybridSearch.fullTextLimit\n                },\n                ...config.hybridSearch.rrfK && {\n                    rrfK: config.hybridSearch.rrfK\n                }\n            };\n            const graphSearchSettings = {\n                enabled: config.graphSearch.enabled,\n                ...config.graphSearch.maxCommunityDescriptionLength && {\n                    maxCommunityDescriptionLength: config.graphSearch.maxCommunityDescriptionLength\n                },\n                ...config.graphSearch.maxLlmQueries && {\n                    maxLlmQueriesForGlobalSearch: config.graphSearch.maxLlmQueries\n                },\n                ...config.graphSearch.localSearchLimits && {\n                    limits: config.graphSearch.localSearchLimits\n                }\n            };\n            const searchSettings = {\n                useSemanticSearch: config.vectorSearch.enabled,\n                useHybridSearch: config.hybridSearch.enabled,\n                useFulltextSearch: config.hybridSearch.enabled,\n                filters: config.vectorSearch.searchFilters ? JSON.parse(config.vectorSearch.searchFilters) : {},\n                limit: config.vectorSearch.searchLimit,\n                includeMetadata: config.vectorSearch.includeMetadatas,\n                chunkSettings: chunkSearchSettings,\n                hybridSettings: hybridSearchSettings,\n                graphSettings: graphSearchSettings\n            };\n            // Prepare generation settings\n            const ragGenerationConfig = {\n                stream: true,\n                temperature: config.ragGeneration.temperature,\n                topP: config.ragGeneration.topP,\n                maxTokensToSample: config.ragGeneration.maxTokensToSample\n            };\n            // Create the user message in R2R format\n            const userMessage = {\n                role: \"user\",\n                content: message\n            };\n            // Use agent mode based on configuration\n            if (config.app.defaultMode === \"rag_agent\") {\n                const streamResponse = await this.client.retrieval.agent({\n                    message: userMessage,\n                    ragGenerationConfig,\n                    searchSettings,\n                    conversationId\n                });\n                // Handle streaming response\n                return await this.processStreamResponse(streamResponse);\n            } else {\n                const streamResponse = await this.client.retrieval.rag({\n                    query: message,\n                    ragGenerationConfig,\n                    searchSettings\n                });\n                // Handle streaming response\n                return await this.processStreamResponse(streamResponse);\n            }\n        } catch (error) {\n            console.error(\"Error in chat service:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Process streaming response from R2R\n   */ async processStreamResponse(streamResponse) {\n        try {\n            const reader = streamResponse.getReader();\n            const decoder = new TextDecoder();\n            let buffer = \"\";\n            let fullContent = \"\";\n            let sources = [];\n            let metadata = {};\n            // Process the stream\n            while(true){\n                const { done, value } = await reader.read();\n                if (done) {\n                    break;\n                }\n                buffer += decoder.decode(value, {\n                    stream: true\n                });\n                // Process complete SSE events from the buffer\n                const events = buffer.split(\"\\n\\n\");\n                buffer = events.pop() || \"\"; // Keep the last potentially incomplete event in the buffer\n                for (const event of events){\n                    if (!event.trim()) {\n                        continue;\n                    }\n                    const lines = event.split(\"\\n\");\n                    const eventType = lines[0].startsWith(\"event: \") ? lines[0].slice(7) : \"\";\n                    const dataLine = lines.find((line)=>line.startsWith(\"data: \"));\n                    if (!dataLine) {\n                        continue;\n                    }\n                    const jsonStr = dataLine.slice(6);\n                    // Skip the [DONE] marker that indicates end of stream\n                    if (jsonStr.trim() === \"[DONE]\") {\n                        continue;\n                    }\n                    // Skip empty or invalid JSON strings\n                    if (!jsonStr.trim()) {\n                        continue;\n                    }\n                    try {\n                        const eventData = JSON.parse(jsonStr);\n                        if (eventType === \"search_results\") {\n                            // Handle search results\n                            if (eventData.search_results) {\n                                sources = eventData.search_results.chunk_search_results || [];\n                            }\n                        } else if (eventType === \"message\") {\n                            // Handle incremental content delta\n                            if (eventData.delta && eventData.delta.content) {\n                                const contentItems = eventData.delta.content;\n                                for (const item of contentItems){\n                                    if (item.type === \"text\" && item.payload && item.payload.value) {\n                                        fullContent += item.payload.value;\n                                    }\n                                }\n                            }\n                        }\n                    } catch (err) {\n                        // Only log errors for non-empty, non-DONE content\n                        if (jsonStr.trim() && jsonStr.trim() !== \"[DONE]\") {\n                            console.error(\"Error parsing SSE event data:\", err, jsonStr);\n                        }\n                    }\n                }\n            }\n            return {\n                message: fullContent || \"No response generated\",\n                sources,\n                metadata\n            };\n        } catch (error) {\n            console.error(\"Error processing stream response:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create a new conversation\n   */ async createConversation(name) {\n        try {\n            var _response_results;\n            const response = await this.client.conversations.create({\n                name: name || \"Conversation \".concat(new Date().toLocaleString())\n            });\n            if ((_response_results = response.results) === null || _response_results === void 0 ? void 0 : _response_results.id) {\n                return response.results.id;\n            } else {\n                throw new Error(\"Failed to create conversation\");\n            }\n        } catch (error) {\n            console.error(\"Error creating conversation:\", error);\n            // Return a mock ID for development\n            return \"conv-\".concat(Date.now());\n        }\n    }\n    /**\n   * Get conversation history\n   */ async getConversationHistory(conversationId) {\n        try {\n            const response = await this.client.conversations.retrieve({\n                id: conversationId\n            });\n            if (response.results) {\n                return response.results.map((msg)=>{\n                    var _msg_metadata, _msg_metadata1, _msg_metadata2;\n                    return {\n                        id: msg.id || \"msg-\".concat(Date.now(), \"-\").concat(Math.random()),\n                        role: ((_msg_metadata = msg.metadata) === null || _msg_metadata === void 0 ? void 0 : _msg_metadata.role) || \"user\",\n                        content: ((_msg_metadata1 = msg.metadata) === null || _msg_metadata1 === void 0 ? void 0 : _msg_metadata1.content) || msg.content || \"\",\n                        timestamp: ((_msg_metadata2 = msg.metadata) === null || _msg_metadata2 === void 0 ? void 0 : _msg_metadata2.timestamp) || new Date().toISOString(),\n                        metadata: msg.metadata\n                    };\n                });\n            }\n            return [];\n        } catch (error) {\n            console.error(\"Error getting conversation history:\", error);\n            return [];\n        }\n    }\n    /**\n   * List conversations\n   */ async listConversations() {\n        let offset = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 100;\n        try {\n            const response = await this.client.conversations.list({\n                offset,\n                limit\n            });\n            return response.results || [];\n        } catch (error) {\n            console.error(\"Error listing conversations:\", error);\n            return [];\n        }\n    }\n    /**\n   * Delete a conversation\n   */ async deleteConversation(conversationId) {\n        try {\n            await this.client.conversations.delete({\n                id: conversationId\n            });\n        } catch (error) {\n            console.error(\"Error deleting conversation:\", error);\n            // Don't throw error in development mode\n            console.warn(\"Conversation deletion failed, continuing...\");\n        }\n    }\n    constructor(client){\n        this.client = client;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/chatService.ts\n"));

/***/ })

});