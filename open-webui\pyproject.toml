[project]
name = "open-webui"
description = "Open WebUI"
authors = [
    { name = "<PERSON>", email = "<EMAIL>" }
]
license = { file = "LICENSE" }
dependencies = [
    "fastapi==0.115.7",
    "uvicorn[standard]==0.34.2",
    "pydantic==2.10.6",
    "python-multipart==0.0.20",

    "python-socketio==5.13.0",
    "python-jose==3.4.0",
    "passlib[bcrypt]==1.7.4",

    "requests==2.32.4",
    "aiohttp==3.11.11",
    "async-timeout",
    "aiocache",
    "aiofiles",
    "starlette-compress==1.6.0",

    "sqlalchemy==2.0.38",
    "alembic==1.14.0",
    "peewee==3.18.1",
    "peewee-migrate==1.12.2",
    "psycopg2-binary==2.9.9",
    "pgvector==0.4.0",
    "PyMySQL==1.1.1",
    "bcrypt==4.3.0",

    "pymongo",
    "redis",
    "boto3==1.35.53",

    "argon2-cffi==23.1.0",
    "APScheduler==3.10.4",


    "RestrictedPython==8.0",

    "loguru==0.7.3",
    "asgiref==3.8.1",

    "openai",
    "anthropic",
    "google-genai==1.15.0",
    "google-generativeai==0.8.5",
    "tiktoken",

    "langchain==0.3.24",
    "langchain-community==0.3.23",

    "fake-useragent==2.1.0",
    "chromadb==0.6.3",
    "pymilvus==2.5.0",
    "qdrant-client~=1.12.0",
    "opensearch-py==2.8.0",
    "playwright==1.49.1",
    "elasticsearch==9.0.1",
    "pinecone==6.0.2",

    "transformers",
    "sentence-transformers==4.1.0",
    "accelerate",
    "colbert-ai==0.2.21",
    "einops==0.8.1",

    "ftfy==6.2.3",
    "pypdf==4.3.1",
    "fpdf2==2.8.2",
    "pymdown-extensions==10.14.2",
    "docx2txt==0.8",
    "python-pptx==1.0.2",
    "unstructured==0.16.17",
    "nltk==3.9.1",
    "Markdown==3.7",
    "pypandoc==1.15",
    "pandas==2.2.3",
    "openpyxl==3.1.5",
    "pyxlsb==1.0.10",
    "xlrd==2.0.1",
    "validators==0.35.0",
    "psutil",
    "sentencepiece",
    "soundfile==0.13.1",
    "azure-ai-documentintelligence==1.0.2",

    "pillow==11.2.1",
    "opencv-python-headless==*********",
    "rapidocr-onnxruntime==1.4.4",
    "rank-bm25==0.2.2",

    "onnxruntime==1.20.1",

    "faster-whisper==1.1.1",

    "PyJWT[crypto]==2.10.1",
    "authlib==1.4.1",

    "black==25.1.0",
    "langfuse==2.44.0",
    "youtube-transcript-api==1.1.0",
    "pytube==15.0.0",

    "extract_msg",
    "pydub",
    "duckduckgo-search==8.0.2",

    "google-api-python-client",
    "google-auth-httplib2",
    "google-auth-oauthlib",

    "docker~=7.1.0",
    "pytest~=8.3.2",
    "pytest-docker~=3.1.1",

    "googleapis-common-protos==1.63.2",
    "google-cloud-storage==2.19.0",

    "azure-identity==1.20.0",
    "azure-storage-blob==12.24.1",

    "ldap3==2.9.1",

    "firecrawl-py==1.12.0",

    "tencentcloud-sdk-python==3.0.1336",

    "gcp-storage-emulator>=2024.8.3",

    "moto[s3]>=5.0.26",

]
readme = "README.md"
requires-python = ">= 3.11, < 3.13.0a1"
dynamic = ["version"]
classifiers = [
    "Development Status :: 4 - Beta",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Communications :: Chat",
    "Topic :: Multimedia",
]

[project.scripts]
open-webui = "open_webui:app"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.rye]
managed = true
dev-dependencies = []

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.version]
path = "package.json"
pattern = '"version":\s*"(?P<version>[^"]+)"'

[tool.hatch.build.hooks.custom]  # keep this for reading hooks from `hatch_build.py`

[tool.hatch.build.targets.wheel]
sources = ["backend"]
exclude = [
    ".dockerignore",
    ".gitignore",
    ".webui_secret_key",
    "dev.sh",
    "requirements.txt",
    "start.sh",
    "start_windows.bat",
    "webui.db",
    "chroma.sqlite3",
]
force-include = { "CHANGELOG.md" = "open_webui/CHANGELOG.md", build = "open_webui/frontend" }

[tool.codespell]
# Ref: https://github.com/codespell-project/codespell#using-a-config-file
skip = '.git*,*.svg,package-lock.json,i18n,*.lock,*.css,*-bundle.js,locales,example-doc.txt,emoji-shortcodes.json'
check-hidden = true
# ignore-regex = ''
ignore-words-list = 'ans'
