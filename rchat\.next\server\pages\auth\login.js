/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/auth/login";
exports.ids = ["pages/auth/login"];
exports.modules = {

/***/ "__barrel_optimize__?names=Eye,EyeOff!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***************************************************************************************************!*\
  !*** __barrel_optimize__?names=Eye,EyeOff!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Eye: () => (/* reexport safe */ _icons_eye_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   EyeOff: () => (/* reexport safe */ _icons_eye_off_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_eye_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/eye.js */ \"./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _icons_eye_off_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/eye-off.js */ \"./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1FeWUsRXllT2ZmIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUMrQyIsInNvdXJjZXMiOlsid2VicGFjazovL3JjaGF0Ly4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/ZGVhZCJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRXllIH0gZnJvbSBcIi4vaWNvbnMvZXllLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRXllT2ZmIH0gZnJvbSBcIi4vaWNvbnMvZXllLW9mZi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Eye,EyeOff!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cauth%5Clogin.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cauth%5Clogin.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./src/pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_auth_login_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\auth\\login.tsx */ \"./src/pages/auth/login.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_auth_login_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n_src_pages_auth_login_tsx__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_auth_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_auth_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_auth_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_auth_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_auth_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_auth_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_auth_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_auth_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_auth_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_auth_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_auth_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/auth/login\",\n        pathname: \"/auth/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _src_pages_auth_login_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cauth%5Clogin.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBS2hDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCxnV0FDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL3JjaGF0Ly4vc3JjL2NvbXBvbmVudHMvdWkvaW5wdXQudHN4P2M5ODMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5leHBvcnQgaW50ZXJmYWNlIElucHV0UHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5JbnB1dEhUTUxBdHRyaWJ1dGVzPEhUTUxJbnB1dEVsZW1lbnQ+IHt9XG5cbmNvbnN0IElucHV0ID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MSW5wdXRFbGVtZW50LCBJbnB1dFByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPGlucHV0XG4gICAgICAgIHR5cGU9e3R5cGV9XG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJmbGV4IGgtMTAgdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LXNtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxuXG5leHBvcnQgeyBJbnB1dCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsInJlZiIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/ui/input.tsx\n");

/***/ }),

/***/ "./src/config/chatConfig.ts":
/*!**********************************!*\
  !*** ./src/config/chatConfig.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultChatConfig: () => (/* binding */ defaultChatConfig),\n/* harmony export */   getDeploymentUrl: () => (/* binding */ getDeploymentUrl),\n/* harmony export */   loadChatConfig: () => (/* binding */ loadChatConfig),\n/* harmony export */   loadPublicConfig: () => (/* binding */ loadPublicConfig),\n/* harmony export */   saveChatConfig: () => (/* binding */ saveChatConfig)\n/* harmony export */ });\n/**\n * Chat Configuration for RChat Frontend\n *\n * This file contains all configuration options for the chat application,\n * including search settings, server connection, and UI preferences.\n */ /**\n * Default configuration values\n */ const defaultChatConfig = {\n    server: {\n        apiUrl: \"http://localhost:7272\",\n        port: 7272,\n        useHttps: false,\n        apiVersion: \"v3\",\n        timeout: 30000\n    },\n    app: {\n        appName: \"RChat\",\n        appDescription: \"R2R-powered chat application\",\n        version: \"1.0.0\",\n        defaultMode: \"rag_agent\"\n    },\n    vectorSearch: {\n        enabled: true,\n        searchLimit: 10,\n        searchFilters: \"{}\",\n        indexMeasure: \"cosine_distance\",\n        includeMetadatas: false,\n        probes: undefined,\n        efSearch: undefined\n    },\n    hybridSearch: {\n        enabled: false,\n        fullTextWeight: undefined,\n        semanticWeight: undefined,\n        fullTextLimit: undefined,\n        rrfK: undefined\n    },\n    graphSearch: {\n        enabled: true,\n        kgSearchLevel: null,\n        maxCommunityDescriptionLength: 100,\n        localSearchLimits: {},\n        maxLlmQueries: undefined\n    },\n    ragGeneration: {\n        temperature: 0.1,\n        topP: 1,\n        topK: 100,\n        maxTokensToSample: 1024,\n        kgTemperature: 0.1,\n        kgTopP: 1,\n        kgTopK: 100,\n        kgMaxTokensToSample: 1024\n    }\n};\n/**\n * Load configuration from localStorage with fallback to defaults\n */ function loadChatConfig() {\n    if (true) {\n        return defaultChatConfig;\n    }\n    try {\n        const stored = localStorage.getItem(\"rchatConfig\");\n        if (stored) {\n            const parsed = JSON.parse(stored);\n            // Merge with defaults to ensure all properties exist\n            return {\n                ...defaultChatConfig,\n                ...parsed,\n                server: {\n                    ...defaultChatConfig.server,\n                    ...parsed.server\n                },\n                app: {\n                    ...defaultChatConfig.app,\n                    ...parsed.app\n                },\n                vectorSearch: {\n                    ...defaultChatConfig.vectorSearch,\n                    ...parsed.vectorSearch\n                },\n                hybridSearch: {\n                    ...defaultChatConfig.hybridSearch,\n                    ...parsed.hybridSearch\n                },\n                graphSearch: {\n                    ...defaultChatConfig.graphSearch,\n                    ...parsed.graphSearch\n                },\n                ragGeneration: {\n                    ...defaultChatConfig.ragGeneration,\n                    ...parsed.ragGeneration\n                }\n            };\n        }\n    } catch (error) {\n        console.warn(\"Failed to load chat config from localStorage:\", error);\n    }\n    return defaultChatConfig;\n}\n/**\n * Save configuration to localStorage\n */ function saveChatConfig(config) {\n    if (true) {\n        return;\n    }\n    try {\n        localStorage.setItem(\"rchatConfig\", JSON.stringify(config));\n    } catch (error) {\n        console.error(\"Failed to save chat config to localStorage:\", error);\n    }\n}\n/**\n * Load configuration from public config.json file\n */ async function loadPublicConfig() {\n    if (true) {\n        return null;\n    }\n    try {\n        const response = await fetch(\"/config.json\");\n        if (!response.ok) {\n            console.warn(\"Could not load public config.json\");\n            return null;\n        }\n        const config = await response.json();\n        return {\n            server: {\n                apiUrl: config.apiUrl,\n                useHttps: config.useHttps,\n                timeout: config.timeout\n            },\n            app: {\n                appName: config.appName,\n                appDescription: config.appDescription,\n                version: config.version,\n                defaultMode: \"rag_agent\"\n            }\n        };\n    } catch (error) {\n        console.warn(\"Error loading public config.json:\", error);\n        return null;\n    }\n}\n/**\n * Get the deployment URL for the API\n */ function getDeploymentUrl(config) {\n    const chatConfig = config || loadChatConfig();\n    // Check for runtime config first (for Docker deployments)\n    if (false) {}\n    return chatConfig.server.apiUrl;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/config/chatConfig.ts\n");

/***/ }),

/***/ "./src/context/UserContext.tsx":
/*!*************************************!*\
  !*** ./src/context/UserContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: () => (/* binding */ UserProvider),\n/* harmony export */   useUserContext: () => (/* binding */ useUserContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var r2r_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! r2r-js */ \"r2r-js\");\n/* harmony import */ var r2r_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(r2r_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _config_chatConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../config/chatConfig */ \"./src/config/chatConfig.ts\");\n\n\n\n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst UserProvider = ({ children })=>{\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isAuthenticated: false,\n        user: null,\n        token: null\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [client, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Initialize client and check for existing session\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            try {\n                const deploymentUrl = (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_3__.getDeploymentUrl)();\n                const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(deploymentUrl);\n                setClient(newClient);\n                // Check for stored token\n                const storedToken = localStorage.getItem(\"r2r_token\");\n                const storedUser = localStorage.getItem(\"r2r_user\");\n                if (storedToken && storedUser) {\n                    try {\n                        const user = JSON.parse(storedUser);\n                        setAuthState({\n                            isAuthenticated: true,\n                            user,\n                            token: storedToken\n                        });\n                    } catch (error) {\n                        console.error(\"Error parsing stored user data:\", error);\n                        localStorage.removeItem(\"r2r_token\");\n                        localStorage.removeItem(\"r2r_user\");\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeAuth();\n    }, []);\n    const login = async (email, password, deploymentUrl)=>{\n        try {\n            const apiUrl = deploymentUrl || (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_3__.getDeploymentUrl)();\n            const loginClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(apiUrl);\n            const response = await loginClient.users.login({\n                email,\n                password\n            });\n            if (!response.results) {\n                throw new Error(\"Login failed: No results returned\");\n            }\n            const { access_token, user_id } = response.results;\n            if (!access_token) {\n                throw new Error(\"Login failed: No access token received\");\n            }\n            // Get user info\n            const userResponse = await loginClient.users.me();\n            if (!userResponse.results) {\n                throw new Error(\"Failed to get user information\");\n            }\n            const userInfo = userResponse.results;\n            const user = {\n                id: userInfo.id || user_id,\n                email: userInfo.email || email,\n                name: userInfo.name,\n                role: userInfo.role\n            };\n            // Store auth data\n            localStorage.setItem(\"r2r_token\", access_token);\n            localStorage.setItem(\"r2r_user\", JSON.stringify(user));\n            // Update state\n            setAuthState({\n                isAuthenticated: true,\n                user,\n                token: access_token\n            });\n            // Update client with new URL if provided\n            if (deploymentUrl) {\n                const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(deploymentUrl);\n                setClient(newClient);\n            }\n            return user;\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            throw error;\n        }\n    };\n    const logout = ()=>{\n        localStorage.removeItem(\"r2r_token\");\n        localStorage.removeItem(\"r2r_user\");\n        setAuthState({\n            isAuthenticated: false,\n            user: null,\n            token: null\n        });\n    };\n    const getClient = async ()=>{\n        if (!authState.isAuthenticated || !authState.token) {\n            return null;\n        }\n        if (!client) {\n            const deploymentUrl = (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_3__.getDeploymentUrl)();\n            const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(deploymentUrl);\n            setClient(newClient);\n            return newClient;\n        }\n        return client;\n    };\n    const value = {\n        authState,\n        login,\n        logout,\n        getClient,\n        isLoading\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\context\\\\UserContext.tsx\",\n        lineNumber: 157,\n        columnNumber: 10\n    }, undefined);\n};\nconst useUserContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (context === undefined) {\n        throw new Error(\"useUserContext must be used within a UserProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29udGV4dC9Vc2VyQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUF5RjtBQUN0RDtBQUVxQjtBQVV4RCxNQUFNTyw0QkFBY04sb0RBQWFBLENBQThCTztBQU14RCxNQUFNQyxlQUE0QyxDQUFDLEVBQUVDLFFBQVEsRUFBRTtJQUNwRSxNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBR1QsK0NBQVFBLENBQVk7UUFDcERVLGlCQUFpQjtRQUNqQkMsTUFBTTtRQUNOQyxPQUFPO0lBQ1Q7SUFDQSxNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBR2QsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDZSxRQUFRQyxVQUFVLEdBQUdoQiwrQ0FBUUEsQ0FBbUI7SUFFdkQsbURBQW1EO0lBQ25EQyxnREFBU0EsQ0FBQztRQUNSLE1BQU1nQixpQkFBaUI7WUFDckIsSUFBSTtnQkFDRixNQUFNQyxnQkFBZ0JmLG9FQUFnQkE7Z0JBQ3RDLE1BQU1nQixZQUFZLElBQUlqQiw2Q0FBU0EsQ0FBQ2dCO2dCQUNoQ0YsVUFBVUc7Z0JBRVYseUJBQXlCO2dCQUN6QixNQUFNQyxjQUFjQyxhQUFhQyxPQUFPLENBQUM7Z0JBQ3pDLE1BQU1DLGFBQWFGLGFBQWFDLE9BQU8sQ0FBQztnQkFFeEMsSUFBSUYsZUFBZUcsWUFBWTtvQkFDN0IsSUFBSTt3QkFDRixNQUFNWixPQUFPYSxLQUFLQyxLQUFLLENBQUNGO3dCQUN4QmQsYUFBYTs0QkFDWEMsaUJBQWlCOzRCQUNqQkM7NEJBQ0FDLE9BQU9RO3dCQUNUO29CQUNGLEVBQUUsT0FBT00sT0FBTzt3QkFDZEMsUUFBUUQsS0FBSyxDQUFDLG1DQUFtQ0E7d0JBQ2pETCxhQUFhTyxVQUFVLENBQUM7d0JBQ3hCUCxhQUFhTyxVQUFVLENBQUM7b0JBQzFCO2dCQUNGO1lBQ0YsRUFBRSxPQUFPRixPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsNEJBQTRCQTtZQUM1QyxTQUFVO2dCQUNSWixhQUFhO1lBQ2Y7UUFDRjtRQUVBRztJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU1ZLFFBQVEsT0FBT0MsT0FBZUMsVUFBa0JiO1FBQ3BELElBQUk7WUFDRixNQUFNYyxTQUFTZCxpQkFBaUJmLG9FQUFnQkE7WUFDaEQsTUFBTThCLGNBQWMsSUFBSS9CLDZDQUFTQSxDQUFDOEI7WUFFbEMsTUFBTUUsV0FBVyxNQUFNRCxZQUFZRSxLQUFLLENBQUNOLEtBQUssQ0FBQztnQkFDN0NDO2dCQUNBQztZQUNGO1lBRUEsSUFBSSxDQUFDRyxTQUFTRSxPQUFPLEVBQUU7Z0JBQ3JCLE1BQU0sSUFBSUMsTUFBTTtZQUNsQjtZQUVBLE1BQU0sRUFBRUMsWUFBWSxFQUFFQyxPQUFPLEVBQUUsR0FBR0wsU0FBU0UsT0FBTztZQUVsRCxJQUFJLENBQUNFLGNBQWM7Z0JBQ2pCLE1BQU0sSUFBSUQsTUFBTTtZQUNsQjtZQUVBLGdCQUFnQjtZQUNoQixNQUFNRyxlQUFlLE1BQU1QLFlBQVlFLEtBQUssQ0FBQ00sRUFBRTtZQUUvQyxJQUFJLENBQUNELGFBQWFKLE9BQU8sRUFBRTtnQkFDekIsTUFBTSxJQUFJQyxNQUFNO1lBQ2xCO1lBRUEsTUFBTUssV0FBV0YsYUFBYUosT0FBTztZQUNyQyxNQUFNekIsT0FBYTtnQkFDakJnQyxJQUFJRCxTQUFTQyxFQUFFLElBQUlKO2dCQUNuQlQsT0FBT1ksU0FBU1osS0FBSyxJQUFJQTtnQkFDekJjLE1BQU1GLFNBQVNFLElBQUk7Z0JBQ25CQyxNQUFNSCxTQUFTRyxJQUFJO1lBQ3JCO1lBRUEsa0JBQWtCO1lBQ2xCeEIsYUFBYXlCLE9BQU8sQ0FBQyxhQUFhUjtZQUNsQ2pCLGFBQWF5QixPQUFPLENBQUMsWUFBWXRCLEtBQUt1QixTQUFTLENBQUNwQztZQUVoRCxlQUFlO1lBQ2ZGLGFBQWE7Z0JBQ1hDLGlCQUFpQjtnQkFDakJDO2dCQUNBQyxPQUFPMEI7WUFDVDtZQUVBLHlDQUF5QztZQUN6QyxJQUFJcEIsZUFBZTtnQkFDakIsTUFBTUMsWUFBWSxJQUFJakIsNkNBQVNBLENBQUNnQjtnQkFDaENGLFVBQVVHO1lBQ1o7WUFFQSxPQUFPUjtRQUNULEVBQUUsT0FBT2UsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsZ0JBQWdCQTtZQUM5QixNQUFNQTtRQUNSO0lBQ0Y7SUFFQSxNQUFNc0IsU0FBUztRQUNiM0IsYUFBYU8sVUFBVSxDQUFDO1FBQ3hCUCxhQUFhTyxVQUFVLENBQUM7UUFDeEJuQixhQUFhO1lBQ1hDLGlCQUFpQjtZQUNqQkMsTUFBTTtZQUNOQyxPQUFPO1FBQ1Q7SUFDRjtJQUVBLE1BQU1xQyxZQUFZO1FBQ2hCLElBQUksQ0FBQ3pDLFVBQVVFLGVBQWUsSUFBSSxDQUFDRixVQUFVSSxLQUFLLEVBQUU7WUFDbEQsT0FBTztRQUNUO1FBRUEsSUFBSSxDQUFDRyxRQUFRO1lBQ1gsTUFBTUcsZ0JBQWdCZixvRUFBZ0JBO1lBQ3RDLE1BQU1nQixZQUFZLElBQUlqQiw2Q0FBU0EsQ0FBQ2dCO1lBQ2hDRixVQUFVRztZQUNWLE9BQU9BO1FBQ1Q7UUFFQSxPQUFPSjtJQUNUO0lBRUEsTUFBTW1DLFFBQXlCO1FBQzdCMUM7UUFDQXFCO1FBQ0FtQjtRQUNBQztRQUNBcEM7SUFDRjtJQUVBLHFCQUFPLDhEQUFDVCxZQUFZK0MsUUFBUTtRQUFDRCxPQUFPQTtrQkFBUTNDOzs7Ozs7QUFDOUMsRUFBRTtBQUVLLE1BQU02QyxpQkFBaUI7SUFDNUIsTUFBTUMsVUFBVXRELGlEQUFVQSxDQUFDSztJQUMzQixJQUFJaUQsWUFBWWhELFdBQVc7UUFDekIsTUFBTSxJQUFJZ0MsTUFBTTtJQUNsQjtJQUNBLE9BQU9nQjtBQUNULEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yY2hhdC8uL3NyYy9jb250ZXh0L1VzZXJDb250ZXh0LnRzeD9jZWU3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgdXNlRWZmZWN0LCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyByMnJDbGllbnQgfSBmcm9tICdyMnItanMnO1xuaW1wb3J0IHsgVXNlciwgQXV0aFN0YXRlIH0gZnJvbSAnLi4vdHlwZXMnO1xuaW1wb3J0IHsgZ2V0RGVwbG95bWVudFVybCB9IGZyb20gJy4uL2NvbmZpZy9jaGF0Q29uZmlnJztcblxuaW50ZXJmYWNlIFVzZXJDb250ZXh0VHlwZSB7XG4gIGF1dGhTdGF0ZTogQXV0aFN0YXRlO1xuICBsb2dpbjogKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcsIGRlcGxveW1lbnRVcmw/OiBzdHJpbmcpID0+IFByb21pc2U8VXNlcj47XG4gIGxvZ291dDogKCkgPT4gdm9pZDtcbiAgZ2V0Q2xpZW50OiAoKSA9PiBQcm9taXNlPHIyckNsaWVudCB8IG51bGw+O1xuICBpc0xvYWRpbmc6IGJvb2xlYW47XG59XG5cbmNvbnN0IFVzZXJDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxVc2VyQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XG5cbmludGVyZmFjZSBVc2VyUHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGU7XG59XG5cbmV4cG9ydCBjb25zdCBVc2VyUHJvdmlkZXI6IFJlYWN0LkZDPFVzZXJQcm92aWRlclByb3BzPiA9ICh7IGNoaWxkcmVuIH0pID0+IHtcbiAgY29uc3QgW2F1dGhTdGF0ZSwgc2V0QXV0aFN0YXRlXSA9IHVzZVN0YXRlPEF1dGhTdGF0ZT4oe1xuICAgIGlzQXV0aGVudGljYXRlZDogZmFsc2UsXG4gICAgdXNlcjogbnVsbCxcbiAgICB0b2tlbjogbnVsbCxcbiAgfSk7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2NsaWVudCwgc2V0Q2xpZW50XSA9IHVzZVN0YXRlPHIyckNsaWVudCB8IG51bGw+KG51bGwpO1xuXG4gIC8vIEluaXRpYWxpemUgY2xpZW50IGFuZCBjaGVjayBmb3IgZXhpc3Rpbmcgc2Vzc2lvblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGluaXRpYWxpemVBdXRoID0gYXN5bmMgKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgZGVwbG95bWVudFVybCA9IGdldERlcGxveW1lbnRVcmwoKTtcbiAgICAgICAgY29uc3QgbmV3Q2xpZW50ID0gbmV3IHIyckNsaWVudChkZXBsb3ltZW50VXJsKTtcbiAgICAgICAgc2V0Q2xpZW50KG5ld0NsaWVudCk7XG5cbiAgICAgICAgLy8gQ2hlY2sgZm9yIHN0b3JlZCB0b2tlblxuICAgICAgICBjb25zdCBzdG9yZWRUb2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdyMnJfdG9rZW4nKTtcbiAgICAgICAgY29uc3Qgc3RvcmVkVXNlciA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdyMnJfdXNlcicpO1xuXG4gICAgICAgIGlmIChzdG9yZWRUb2tlbiAmJiBzdG9yZWRVc2VyKSB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IHVzZXIgPSBKU09OLnBhcnNlKHN0b3JlZFVzZXIpO1xuICAgICAgICAgICAgc2V0QXV0aFN0YXRlKHtcbiAgICAgICAgICAgICAgaXNBdXRoZW50aWNhdGVkOiB0cnVlLFxuICAgICAgICAgICAgICB1c2VyLFxuICAgICAgICAgICAgICB0b2tlbjogc3RvcmVkVG9rZW4sXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcGFyc2luZyBzdG9yZWQgdXNlciBkYXRhOicsIGVycm9yKTtcbiAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdyMnJfdG9rZW4nKTtcbiAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdyMnJfdXNlcicpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgaW5pdGlhbGl6aW5nIGF1dGg6JywgZXJyb3IpO1xuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgaW5pdGlhbGl6ZUF1dGgoKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGxvZ2luID0gYXN5bmMgKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcsIGRlcGxveW1lbnRVcmw/OiBzdHJpbmcpOiBQcm9taXNlPFVzZXI+ID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgYXBpVXJsID0gZGVwbG95bWVudFVybCB8fCBnZXREZXBsb3ltZW50VXJsKCk7XG4gICAgICBjb25zdCBsb2dpbkNsaWVudCA9IG5ldyByMnJDbGllbnQoYXBpVXJsKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBsb2dpbkNsaWVudC51c2Vycy5sb2dpbih7XG4gICAgICAgIGVtYWlsLFxuICAgICAgICBwYXNzd29yZCxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLnJlc3VsdHMpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdMb2dpbiBmYWlsZWQ6IE5vIHJlc3VsdHMgcmV0dXJuZWQnKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgeyBhY2Nlc3NfdG9rZW4sIHVzZXJfaWQgfSA9IHJlc3BvbnNlLnJlc3VsdHMgYXMgYW55O1xuXG4gICAgICBpZiAoIWFjY2Vzc190b2tlbikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0xvZ2luIGZhaWxlZDogTm8gYWNjZXNzIHRva2VuIHJlY2VpdmVkJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIEdldCB1c2VyIGluZm9cbiAgICAgIGNvbnN0IHVzZXJSZXNwb25zZSA9IGF3YWl0IGxvZ2luQ2xpZW50LnVzZXJzLm1lKCk7XG5cbiAgICAgIGlmICghdXNlclJlc3BvbnNlLnJlc3VsdHMpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZ2V0IHVzZXIgaW5mb3JtYXRpb24nKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgdXNlckluZm8gPSB1c2VyUmVzcG9uc2UucmVzdWx0cyBhcyBhbnk7XG4gICAgICBjb25zdCB1c2VyOiBVc2VyID0ge1xuICAgICAgICBpZDogdXNlckluZm8uaWQgfHwgdXNlcl9pZCxcbiAgICAgICAgZW1haWw6IHVzZXJJbmZvLmVtYWlsIHx8IGVtYWlsLFxuICAgICAgICBuYW1lOiB1c2VySW5mby5uYW1lLFxuICAgICAgICByb2xlOiB1c2VySW5mby5yb2xlLFxuICAgICAgfTtcblxuICAgICAgLy8gU3RvcmUgYXV0aCBkYXRhXG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgncjJyX3Rva2VuJywgYWNjZXNzX3Rva2VuKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdyMnJfdXNlcicsIEpTT04uc3RyaW5naWZ5KHVzZXIpKTtcblxuICAgICAgLy8gVXBkYXRlIHN0YXRlXG4gICAgICBzZXRBdXRoU3RhdGUoe1xuICAgICAgICBpc0F1dGhlbnRpY2F0ZWQ6IHRydWUsXG4gICAgICAgIHVzZXIsXG4gICAgICAgIHRva2VuOiBhY2Nlc3NfdG9rZW4sXG4gICAgICB9KTtcblxuICAgICAgLy8gVXBkYXRlIGNsaWVudCB3aXRoIG5ldyBVUkwgaWYgcHJvdmlkZWRcbiAgICAgIGlmIChkZXBsb3ltZW50VXJsKSB7XG4gICAgICAgIGNvbnN0IG5ld0NsaWVudCA9IG5ldyByMnJDbGllbnQoZGVwbG95bWVudFVybCk7XG4gICAgICAgIHNldENsaWVudChuZXdDbGllbnQpO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gdXNlcjtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignTG9naW4gZXJyb3I6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGxvZ291dCA9ICgpID0+IHtcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgncjJyX3Rva2VuJyk7XG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3Iycl91c2VyJyk7XG4gICAgc2V0QXV0aFN0YXRlKHtcbiAgICAgIGlzQXV0aGVudGljYXRlZDogZmFsc2UsXG4gICAgICB1c2VyOiBudWxsLFxuICAgICAgdG9rZW46IG51bGwsXG4gICAgfSk7XG4gIH07XG5cbiAgY29uc3QgZ2V0Q2xpZW50ID0gYXN5bmMgKCk6IFByb21pc2U8cjJyQ2xpZW50IHwgbnVsbD4gPT4ge1xuICAgIGlmICghYXV0aFN0YXRlLmlzQXV0aGVudGljYXRlZCB8fCAhYXV0aFN0YXRlLnRva2VuKSB7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG5cbiAgICBpZiAoIWNsaWVudCkge1xuICAgICAgY29uc3QgZGVwbG95bWVudFVybCA9IGdldERlcGxveW1lbnRVcmwoKTtcbiAgICAgIGNvbnN0IG5ld0NsaWVudCA9IG5ldyByMnJDbGllbnQoZGVwbG95bWVudFVybCk7XG4gICAgICBzZXRDbGllbnQobmV3Q2xpZW50KTtcbiAgICAgIHJldHVybiBuZXdDbGllbnQ7XG4gICAgfVxuXG4gICAgcmV0dXJuIGNsaWVudDtcbiAgfTtcblxuICBjb25zdCB2YWx1ZTogVXNlckNvbnRleHRUeXBlID0ge1xuICAgIGF1dGhTdGF0ZSxcbiAgICBsb2dpbixcbiAgICBsb2dvdXQsXG4gICAgZ2V0Q2xpZW50LFxuICAgIGlzTG9hZGluZyxcbiAgfTtcblxuICByZXR1cm4gPFVzZXJDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+e2NoaWxkcmVufTwvVXNlckNvbnRleHQuUHJvdmlkZXI+O1xufTtcblxuZXhwb3J0IGNvbnN0IHVzZVVzZXJDb250ZXh0ID0gKCk6IFVzZXJDb250ZXh0VHlwZSA9PiB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KFVzZXJDb250ZXh0KTtcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlVXNlckNvbnRleHQgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIFVzZXJQcm92aWRlcicpO1xuICB9XG4gIHJldHVybiBjb250ZXh0O1xufTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJyMnJDbGllbnQiLCJnZXREZXBsb3ltZW50VXJsIiwiVXNlckNvbnRleHQiLCJ1bmRlZmluZWQiLCJVc2VyUHJvdmlkZXIiLCJjaGlsZHJlbiIsImF1dGhTdGF0ZSIsInNldEF1dGhTdGF0ZSIsImlzQXV0aGVudGljYXRlZCIsInVzZXIiLCJ0b2tlbiIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImNsaWVudCIsInNldENsaWVudCIsImluaXRpYWxpemVBdXRoIiwiZGVwbG95bWVudFVybCIsIm5ld0NsaWVudCIsInN0b3JlZFRva2VuIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInN0b3JlZFVzZXIiLCJKU09OIiwicGFyc2UiLCJlcnJvciIsImNvbnNvbGUiLCJyZW1vdmVJdGVtIiwibG9naW4iLCJlbWFpbCIsInBhc3N3b3JkIiwiYXBpVXJsIiwibG9naW5DbGllbnQiLCJyZXNwb25zZSIsInVzZXJzIiwicmVzdWx0cyIsIkVycm9yIiwiYWNjZXNzX3Rva2VuIiwidXNlcl9pZCIsInVzZXJSZXNwb25zZSIsIm1lIiwidXNlckluZm8iLCJpZCIsIm5hbWUiLCJyb2xlIiwic2V0SXRlbSIsInN0cmluZ2lmeSIsImxvZ291dCIsImdldENsaWVudCIsInZhbHVlIiwiUHJvdmlkZXIiLCJ1c2VVc2VyQ29udGV4dCIsImNvbnRleHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/context/UserContext.tsx\n");

/***/ }),

/***/ "./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatTimestamp: () => (/* binding */ formatTimestamp),\n/* harmony export */   generateUUID: () => (/* binding */ generateUUID),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format timestamp to readable string\n */ function formatTimestamp(timestamp) {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffMins = Math.floor(diffMs / 60000);\n    const diffHours = Math.floor(diffMs / 3600000);\n    const diffDays = Math.floor(diffMs / 86400000);\n    if (diffMins < 1) {\n        return \"Just now\";\n    } else if (diffMins < 60) {\n        return `${diffMins}m ago`;\n    } else if (diffHours < 24) {\n        return `${diffHours}h ago`;\n    } else if (diffDays < 7) {\n        return `${diffDays}d ago`;\n    } else {\n        return date.toLocaleDateString();\n    }\n}\n/**\n * Truncate text to specified length\n */ function truncateText(text, maxLength = 50) {\n    if (text.length <= maxLength) {\n        return text;\n    }\n    return text.substring(0, maxLength) + \"...\";\n}\n/**\n * Generate a random UUID v4\n */ function generateUUID() {\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function(c) {\n        const r = Math.random() * 16 | 0;\n        const v = c === \"x\" ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n    });\n}\n/**\n * Debounce function\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Check if running in browser environment\n */ function isBrowser() {\n    return \"undefined\" !== \"undefined\";\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/utils.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/UserContext */ \"./src/context/UserContext.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n        attribute: \"class\",\n        defaultTheme: \"system\",\n        enableSystem: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_UserContext__WEBPACK_IMPORTED_MODULE_2__.UserProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 10,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQzRDO0FBQ1M7QUFDdkI7QUFFZixTQUFTRSxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFZO0lBQzVELHFCQUNFLDhEQUFDSixzREFBYUE7UUFBQ0ssV0FBVTtRQUFRQyxjQUFhO1FBQVNDLFlBQVk7a0JBQ2pFLDRFQUFDTiw4REFBWUE7c0JBQ1gsNEVBQUNFO2dCQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJaEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yY2hhdC8uL3NyYy9wYWdlcy9fYXBwLnRzeD9mOWQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCc7XG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIH0gZnJvbSAnbmV4dC10aGVtZXMnO1xuaW1wb3J0IHsgVXNlclByb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0L1VzZXJDb250ZXh0JztcbmltcG9ydCAnQC9zdHlsZXMvZ2xvYmFscy5jc3MnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxUaGVtZVByb3ZpZGVyIGF0dHJpYnV0ZT1cImNsYXNzXCIgZGVmYXVsdFRoZW1lPVwic3lzdGVtXCIgZW5hYmxlU3lzdGVtPlxuICAgICAgPFVzZXJQcm92aWRlcj5cbiAgICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgICAgPC9Vc2VyUHJvdmlkZXI+XG4gICAgPC9UaGVtZVByb3ZpZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlRoZW1lUHJvdmlkZXIiLCJVc2VyUHJvdmlkZXIiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJhdHRyaWJ1dGUiLCJkZWZhdWx0VGhlbWUiLCJlbmFibGVTeXN0ZW0iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/_document.tsx":
/*!*********************************!*\
  !*** ./src/pages/_document.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 7,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"RChat - R2R-powered chat application\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2RvY3VtZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNkQ7QUFFOUMsU0FBU0k7SUFDdEIscUJBQ0UsOERBQUNKLCtDQUFJQTtRQUFDSyxNQUFLOzswQkFDVCw4REFBQ0osK0NBQUlBOztrQ0FDSCw4REFBQ0s7d0JBQUtDLFNBQVE7Ozs7OztrQ0FDZCw4REFBQ0Q7d0JBQUtFLE1BQUs7d0JBQWNDLFNBQVE7Ozs7OztrQ0FDakMsOERBQUNIO3dCQUFLRSxNQUFLO3dCQUFXQyxTQUFROzs7Ozs7a0NBQzlCLDhEQUFDQzt3QkFBS0MsS0FBSTt3QkFBT0MsTUFBSzs7Ozs7Ozs7Ozs7OzBCQUV4Qiw4REFBQ0M7O2tDQUNDLDhEQUFDWCwrQ0FBSUE7Ozs7O2tDQUNMLDhEQUFDQyxxREFBVUE7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSW5CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmNoYXQvLi9zcmMvcGFnZXMvX2RvY3VtZW50LnRzeD8xODhlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEh0bWwsIEhlYWQsIE1haW4sIE5leHRTY3JpcHQgfSBmcm9tICduZXh0L2RvY3VtZW50JztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRG9jdW1lbnQoKSB7XG4gIHJldHVybiAoXG4gICAgPEh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8SGVhZD5cbiAgICAgICAgPG1ldGEgY2hhclNldD1cInV0Zi04XCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImRlc2NyaXB0aW9uXCIgY29udGVudD1cIlJDaGF0IC0gUjJSLXBvd2VyZWQgY2hhdCBhcHBsaWNhdGlvblwiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJ2aWV3cG9ydFwiIGNvbnRlbnQ9XCJ3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MVwiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cImljb25cIiBocmVmPVwiL2Zhdmljb24uaWNvXCIgLz5cbiAgICAgIDwvSGVhZD5cbiAgICAgIDxib2R5PlxuICAgICAgICA8TWFpbiAvPlxuICAgICAgICA8TmV4dFNjcmlwdCAvPlxuICAgICAgPC9ib2R5PlxuICAgIDwvSHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJIdG1sIiwiSGVhZCIsIk1haW4iLCJOZXh0U2NyaXB0IiwiRG9jdW1lbnQiLCJsYW5nIiwibWV0YSIsImNoYXJTZXQiLCJuYW1lIiwiY29udGVudCIsImxpbmsiLCJyZWwiLCJocmVmIiwiYm9keSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/_document.tsx\n");

/***/ }),

/***/ "./src/pages/auth/login.tsx":
/*!**********************************!*\
  !*** ./src/pages/auth/login.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff!=!lucide-react */ \"__barrel_optimize__?names=Eye,EyeOff!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"./src/components/ui/input.tsx\");\n/* harmony import */ var _config_chatConfig__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/chatConfig */ \"./src/config/chatConfig.ts\");\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/context/UserContext */ \"./src/context/UserContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_input__WEBPACK_IMPORTED_MODULE_3__]);\n_components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\nconst LoginPage = ()=>{\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [serverHealth, setServerHealth] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [loginSuccess, setLoginSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { login, authState } = (0,_context_UserContext__WEBPACK_IMPORTED_MODULE_5__.useUserContext)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    // Get deployment URL from configuration\n    const deploymentUrl = (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_4__.getDeploymentUrl)();\n    // Load default credentials from runtime config if available\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (false) {}\n    }, []);\n    // Check deployment health\n    const checkDeploymentHealth = async ()=>{\n        try {\n            const response = await fetch(`${deploymentUrl}/v3/health`);\n            return response.ok;\n        } catch (error) {\n            console.error(\"Health check failed:\", error);\n            return false;\n        }\n    };\n    // Handle login submission\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(null);\n        try {\n            const result = await login(email, password, deploymentUrl);\n            setLoginSuccess(true);\n        } catch (error) {\n            console.error(\"Login failed:\", error);\n            // Only check server health after a failed login attempt\n            const isServerHealthy = await checkDeploymentHealth();\n            let errorMessage = \"An unknown error occurred\";\n            if (error instanceof Error) {\n                errorMessage = error.message;\n            } else if (typeof error === \"string\") {\n                errorMessage = error;\n            }\n            // Provide appropriate error message based on server health\n            const serverStatusMessage = isServerHealthy ? \"Server is running normally, please check your credentials and try again.\" : \"Unable to communicate with server, please check the API address in the configuration file.\";\n            setError(`Login failed. ${serverStatusMessage}\\n\\nError: ${errorMessage}`);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Handle successful login redirect\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (loginSuccess && authState.isAuthenticated) {\n            router.push(\"/\");\n        }\n    }, [\n        loginSuccess,\n        authState.isAuthenticated,\n        router\n    ]);\n    const togglePasswordVisibility = ()=>{\n        setShowPassword(!showPassword);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-screen max-h-[100dvh] text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full absolute top-0 left-0 bg-white dark:bg-black\"\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full absolute top-0 left-0 right-0 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed m-10 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"self-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-6 h-6 rounded-full bg-gradient-to-r from-blue-600 to-purple-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bg-transparent min-h-screen w-full flex justify-center font-primary z-50 text-black dark:text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full sm:max-w-md px-10 min-h-screen flex flex-col text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-auto pb-10 w-full dark:text-gray-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                className: \"flex flex-col justify-center\",\n                                onSubmit: handleSubmit,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-medium\",\n                                            children: \"Sign in to RChat\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"email\",\n                                                        className: \"text-sm font-medium text-left mb-1 block\",\n                                                        children: \"Email\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"email\",\n                                                        type: \"email\",\n                                                        className: \"my-0.5 w-full text-sm bg-transparent border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2\",\n                                                        autoComplete: \"email\",\n                                                        name: \"email\",\n                                                        placeholder: \"Enter Your Email\",\n                                                        value: email,\n                                                        onChange: (e)=>setEmail(e.target.value),\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"password\",\n                                                        className: \"text-sm font-medium text-left mb-1 block\",\n                                                        children: \"Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"password\",\n                                                                type: showPassword ? \"text\" : \"password\",\n                                                                className: \"my-0.5 w-full text-sm bg-transparent border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 pr-10\",\n                                                                placeholder: \"Enter Your Password\",\n                                                                autoComplete: \"current-password\",\n                                                                name: \"current-password\",\n                                                                value: password,\n                                                                onChange: (e)=>setPassword(e.target.value),\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: togglePasswordVisibility,\n                                                                className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\",\n                                                                children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__.EyeOff, {\n                                                                    className: \"h-5 w-5\",\n                                                                    \"aria-hidden\": \"true\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 25\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Eye, {\n                                                                    className: \"h-5 w-5\",\n                                                                    \"aria-hidden\": \"true\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"bg-gray-700/5 hover:bg-gray-700/10 dark:bg-gray-100/5 dark:hover:bg-gray-100/10 dark:text-gray-300 dark:hover:text-white transition w-full rounded-full font-medium text-sm py-2.5\",\n                                            type: \"submit\",\n                                            disabled: isLoading,\n                                            children: isLoading ? \"Signing in...\" : \"Sign in\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, undefined),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 dark:text-red-400 text-sm whitespace-pre-line\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\auth\\\\login.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoginPage);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/auth/login.tsx\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next-themes":
/*!******************************!*\
  !*** external "next-themes" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-themes");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "r2r-js":
/*!*************************!*\
  !*** external "r2r-js" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("r2r-js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cauth%5Clogin.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();