(()=>{var e={};e.id=344,e.ids=[344,888,660],e.modules={6317:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{config:()=>g,default:()=>d,getServerSideProps:()=>x,getStaticPaths:()=>h,getStaticProps:()=>m,reportWebVitals:()=>f,routeModule:()=>S,unstable_getServerProps:()=>y,unstable_getServerSideProps:()=>b,unstable_getStaticParams:()=>k,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>p});var s=r(7093),l=r(5244),i=r(1323),o=r(1070),n=r(7724),c=r(8733),u=e([c]);c=(u.then?(await u)():u)[0];let d=(0,i.l)(c,"default"),m=(0,i.l)(c,"getStaticProps"),h=(0,i.l)(c,"getStaticPaths"),x=(0,i.l)(c,"getServerSideProps"),g=(0,i.l)(c,"config"),f=(0,i.l)(c,"reportWebVitals"),p=(0,i.l)(c,"unstable_getStaticProps"),v=(0,i.l)(c,"unstable_getStaticPaths"),k=(0,i.l)(c,"unstable_getStaticParams"),y=(0,i.l)(c,"unstable_getServerProps"),b=(0,i.l)(c,"unstable_getServerSideProps"),S=new s.PagesRouteModule({definition:{kind:l.x.PAGES,page:"/auth/login",pathname:"/auth/login",bundlePath:"",filename:""},components:{App:n.default,Document:o.default},userland:c});a()}catch(e){a(e)}})},7233:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(1462).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},3980:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(1462).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},8501:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{I:()=>n});var s=r(997),l=r(6689),i=r(3917),o=e([i]);i=(o.then?(await o)():o)[0];let n=l.forwardRef(({className:e,type:t,...r},a)=>s.jsx("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...r}));n.displayName="Input",a()}catch(e){a(e)}})},6361:(e,t,r)=>{"use strict";r.d(t,{YQ:()=>s,pe:()=>l});let a={server:{apiUrl:"http://localhost:7272",port:7272,useHttps:!1,apiVersion:"v3",timeout:3e4},app:{appName:"RChat",appDescription:"R2R-powered chat application",version:"1.0.0",defaultMode:"rag_agent"},vectorSearch:{enabled:!0,searchLimit:10,searchFilters:"{}",indexMeasure:"cosine_distance",includeMetadatas:!1,probes:void 0,efSearch:void 0},hybridSearch:{enabled:!1,fullTextWeight:void 0,semanticWeight:void 0,fullTextLimit:void 0,rrfK:void 0},graphSearch:{enabled:!0,kgSearchLevel:null,maxCommunityDescriptionLength:100,localSearchLimits:{},maxLlmQueries:void 0},ragGeneration:{temperature:.1,topP:1,topK:100,maxTokensToSample:1024,kgTemperature:.1,kgTopP:1,kgTopK:100,kgMaxTokensToSample:1024}};function s(){return a}function l(e){return(e||a).server.apiUrl}},6259:(e,t,r)=>{"use strict";r.d(t,{d:()=>n,S:()=>c});var a=r(997),s=r(6689);let l=require("r2r-js");var i=r(6361);let o=(0,s.createContext)(void 0),n=({children:e})=>{let[t,r]=(0,s.useState)({isAuthenticated:!1,user:null,token:null}),[n,c]=(0,s.useState)(!0),[u,d]=(0,s.useState)(null);(0,s.useEffect)(()=>{(async()=>{try{let e=(0,i.pe)(),t=new l.r2rClient(e);d(t);let a=localStorage.getItem("chatAccessToken")||localStorage.getItem("r2r_token"),s=localStorage.getItem("chatRefreshToken"),o=localStorage.getItem("r2r_user");if(a&&o)try{let e=JSON.parse(o);s&&t.setTokens(a,s),r({isAuthenticated:!0,user:e,token:a})}catch(e){console.error("Error parsing stored user data:",e),localStorage.removeItem("r2r_token"),localStorage.removeItem("r2r_user"),localStorage.removeItem("chatAccessToken"),localStorage.removeItem("chatRefreshToken")}}catch(e){console.error("Error initializing auth:",e)}finally{c(!1)}})()},[]);let m=async(e,t,a)=>{try{let s=a||(0,i.pe)(),o=new l.r2rClient(s),n=await o.users.login({email:e,password:t});if(!n.results)throw Error("Login failed: No results returned");localStorage.setItem("chatAccessToken",n.results.accessToken.token),localStorage.setItem("chatRefreshToken",n.results.refreshToken.token),o.setTokens(n.results.accessToken.token,n.results.refreshToken.token),d(o);let c=await o.users.me();if(!c.results)throw Error("Failed to get user information");let u="user";try{await o.system.settings(),u="admin"}catch(e){e instanceof Error&&"status"in e&&403===e.status||console.error("Unexpected error when checking user role:",e)}let m={id:c.results.id,email:c.results.email||e,name:c.results.name,role:u};return localStorage.setItem("r2r_token",n.results.accessToken.token),localStorage.setItem("r2r_user",JSON.stringify(m)),r({isAuthenticated:!0,user:m,token:n.results.accessToken.token}),m}catch(e){if(console.error("Login error:",e),e instanceof Error){if(e.message.includes("401")||e.message.includes("Unauthorized"))throw Error("Invalid email or password. Please check your credentials.");if(e.message.includes("404")||e.message.includes("Not Found"))throw Error("User not found. Please check your email address.");if(e.message.includes("500")||e.message.includes("Internal Server Error"))throw Error("Server error. Please try again later.");if(e.message.includes("Network Error")||e.message.includes("fetch"))throw Error("Cannot connect to server. Please check if R2R backend is running.")}throw e}},h=async()=>{if(!t.isAuthenticated||!t.token)return null;if(!u){let e=(0,i.pe)(),t=new l.r2rClient(e),r=localStorage.getItem("chatAccessToken")||localStorage.getItem("r2r_token"),a=localStorage.getItem("chatRefreshToken");return r&&t.setTokens(r,a||""),d(t),t}return u};return a.jsx(o.Provider,{value:{authState:t,login:m,logout:()=>{localStorage.removeItem("r2r_token"),localStorage.removeItem("r2r_user"),localStorage.removeItem("chatAccessToken"),localStorage.removeItem("chatRefreshToken"),r({isAuthenticated:!1,user:null,token:null}),d(null)},getClient:h,isLoading:n},children:e})},c=()=>{let e=(0,s.useContext)(o);if(void 0===e)throw Error("useUserContext must be used within a UserProvider");return e}},3917:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{DO:()=>u,aF:()=>c,cn:()=>o,i$:()=>n});var s=r(6593),l=r(8097),i=e([s,l]);function o(...e){return(0,l.twMerge)((0,s.clsx)(e))}function n(e){let t=new Date(e),r=new Date().getTime()-t.getTime(),a=Math.floor(r/6e4),s=Math.floor(r/36e5),l=Math.floor(r/864e5);return a<1?"Just now":a<60?`${a}m ago`:s<24?`${s}h ago`:l<7?`${l}d ago`:t.toLocaleDateString()}function c(e,t=50){return e.length<=t?e:e.substring(0,t)+"..."}function u(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}[s,l]=i.then?(await i)():i,a()}catch(e){a(e)}})},7724:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var a=r(997);let s=require("next-themes");var l=r(6259);function i({Component:e,pageProps:t}){return a.jsx(s.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,children:a.jsx(l.d,{children:a.jsx(e,{...t})})})}r(108)},1070:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(997),s=r(6859);function l(){return(0,a.jsxs)(s.Html,{lang:"en",children:[(0,a.jsxs)(s.Head,{children:[a.jsx("meta",{charSet:"utf-8"}),a.jsx("meta",{name:"description",content:"RChat - R2R-powered chat application"}),a.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),a.jsx("link",{rel:"icon",href:"/favicon.ico"})]}),(0,a.jsxs)("body",{children:[a.jsx(s.Main,{}),a.jsx(s.NextScript,{})]})]})}},8733:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>h});var s=r(997),l=r(7233),i=r(3980),o=r(1163),n=r(6689),c=r(8501),u=r(6361),d=r(6259),m=e([c]);c=(m.then?(await m)():m)[0];let h=()=>{let[e,t]=(0,n.useState)("<EMAIL>"),[r,a]=(0,n.useState)(""),[m,h]=(0,n.useState)(!1),[x,g]=(0,n.useState)(null),[f,p]=(0,n.useState)(!1),[v,k]=(0,n.useState)(!0),[y,b]=(0,n.useState)(!1),{login:S,authState:w}=(0,d.S)(),j=(0,o.useRouter)(),N=(0,u.pe)();(0,n.useEffect)(()=>{},[]);let T=async()=>{try{return(await fetch(`${N}/v3/health`)).ok}catch(e){return console.error("Health check failed:",e),!1}},P=async t=>{t.preventDefault(),p(!0),g(null);try{await S(e,r,N)&&b(!0)}catch(r){console.error("Login failed:",r);let e=await T(),t="An unknown error occurred";r instanceof Error?t=r.message:"string"==typeof r&&(t=r),g(`Login failed. ${e?"Server is running normally, please check your credentials and try again.":"Unable to communicate with server, please check the API address in the configuration file."}

Error: ${t}`)}finally{p(!1)}};return(0,n.useEffect)(()=>{y&&w.isAuthenticated&&j.push("/")},[y,w.isAuthenticated,j]),(0,s.jsxs)("div",{className:"w-full h-screen max-h-[100dvh] text-white relative",children:[s.jsx("div",{className:"w-full h-full absolute top-0 left-0 bg-white dark:bg-black"}),s.jsx("div",{className:"w-full absolute top-0 left-0 right-0 h-8"}),s.jsx("div",{className:"fixed m-10 z-50",children:s.jsx("div",{className:"flex space-x-2",children:s.jsx("div",{className:"self-center",children:s.jsx("div",{className:"w-6 h-6 rounded-full bg-gradient-to-r from-blue-600 to-purple-600"})})})}),s.jsx("div",{className:"fixed bg-transparent min-h-screen w-full flex justify-center font-primary z-50 text-black dark:text-white",children:s.jsx("div",{className:"w-full sm:max-w-md px-10 min-h-screen flex flex-col text-center",children:(0,s.jsxs)("div",{className:"my-auto pb-10 w-full dark:text-gray-100",children:[(0,s.jsxs)("form",{className:"flex flex-col justify-center",onSubmit:P,children:[s.jsx("div",{className:"mb-1",children:s.jsx("div",{className:"text-2xl font-medium",children:"Sign in to RChat"})}),(0,s.jsxs)("div",{className:"flex flex-col mt-4",children:[(0,s.jsxs)("div",{className:"mb-2",children:[s.jsx("label",{htmlFor:"email",className:"text-sm font-medium text-left mb-1 block",children:"Email"}),s.jsx(c.I,{id:"email",type:"email",className:"my-0.5 w-full text-sm bg-transparent border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2",autoComplete:"email",name:"email",placeholder:"Enter Your Email",value:e,onChange:e=>t(e.target.value),required:!0})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"password",className:"text-sm font-medium text-left mb-1 block",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[s.jsx(c.I,{id:"password",type:m?"text":"password",className:"my-0.5 w-full text-sm bg-transparent border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 pr-10",placeholder:"Enter Your Password",autoComplete:"current-password",name:"current-password",value:r,onChange:e=>a(e.target.value),required:!0}),s.jsx("button",{type:"button",onClick:()=>{h(!m)},className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors",children:m?s.jsx(l.Z,{className:"h-5 w-5","aria-hidden":"true"}):s.jsx(i.Z,{className:"h-5 w-5","aria-hidden":"true"})})]})]})]}),s.jsx("div",{className:"mt-5",children:s.jsx("button",{className:"bg-gray-700/5 hover:bg-gray-700/10 dark:bg-gray-100/5 dark:hover:bg-gray-100/10 dark:text-gray-300 dark:hover:text-white transition w-full rounded-full font-medium text-sm py-2.5",type:"submit",disabled:f,children:f?"Signing in...":"Sign in"})}),(0,s.jsxs)("div",{className:"mt-3 text-center",children:[s.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Default R2R user: <EMAIL>"}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["Server: ",N]})]})]}),x&&s.jsx("div",{className:"mt-6 p-4 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-md",children:s.jsx("p",{className:"text-red-600 dark:text-red-400 text-sm whitespace-pre-line",children:x})})]})})})]})};a()}catch(e){a(e)}})},108:()=>{},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},6593:e=>{"use strict";e.exports=import("clsx")},8097:e=>{"use strict";e.exports=import("tailwind-merge")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[859,295],()=>r(6317));module.exports=a})();