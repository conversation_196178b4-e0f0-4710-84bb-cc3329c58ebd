import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useUserContext } from '@/context/UserContext';
import ChatInterface from '@/components/chat/ChatInterface';
import Head from 'next/head';

const HomePage: React.FC = () => {
  const { authState, isLoading } = useUserContext();
  const router = useRouter();
  const [showSettings, setShowSettings] = useState(false);

  useEffect(() => {
    if (!isLoading && !authState.isAuthenticated) {
      router.push('/auth/login');
    }
  }, [authState.isAuthenticated, isLoading, router]);

  const handleSettingsClick = () => {
    setShowSettings(true);
    // Settings modal/page to be implemented later
    console.log('Settings clicked');
  };

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-white dark:bg-gray-900">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center">
            <span className="text-white font-bold text-xl">R</span>
          </div>
          <div className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Loading RChat...
          </div>
          <div className="text-gray-600 dark:text-gray-400">
            Please wait while we initialize your session
          </div>
        </div>
      </div>
    );
  }

  if (!authState.isAuthenticated) {
    return null; // Will redirect to login
  }

  return (
    <>
      <Head>
        <title>RChat - R2R-powered Chat</title>
        <meta name="description" content="Chat with your R2R-powered AI assistant" />
      </Head>
      
      <ChatInterface onSettingsClick={handleSettingsClick} />
    </>
  );
};

export default HomePage;
