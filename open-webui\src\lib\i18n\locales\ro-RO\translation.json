{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' sau '-1' făr<PERSON> expirare.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(de ex. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(de ex. `sh webui.sh --api`)", "(latest)": "(ultimul)", "(leave blank for to use commercial endpoint)": "", "{{ models }}": "{{ modele }}", "{{COUNT}} Available Tools": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "", "{{user}}'s Chats": "Conversațiile lui {{user}}", "{{webUIName}} Backend Required": "Este necesar backend-ul {{webUIName}}", "*Prompt node ID(s) are required for image generation": "*Sunt necesare ID-urile nodurilor de solicitare pentru generarea imaginii*", "A new version (v{{LATEST_VERSION}}) is now available.": "O nouă versiune (v{{LATEST_VERSION}}) este acum disponibilă.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Un model de sarcină este utilizat pentru realizarea unor sarcini precum generarea de titluri pentru conversații și interogări de căutare pe web", "a user": "un utilizator", "About": "<PERSON><PERSON><PERSON>", "Accept autocomplete generation / Jump to prompt variable": "", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "<PERSON><PERSON>", "Account Activation Pending": "Activarea contului în așteptare", "Accurate information": "Informații precise", "Actions": "Acțiuni", "Activate": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "Utilizatori activi", "Add": "Adaugă", "Add a model ID": "Adaugă un ID de model", "Add a short description about what this model does": "Adaugă o scurtă descriere despre ce face acest model", "Add a tag": "Adaugă o etichetă", "Add Arena Model": "Adaugă Modelul Arena", "Add Connection": "<PERSON><PERSON><PERSON>", "Add Content": "Adăugați conținut", "Add content here": "Adăugați conținut aici", "Add Custom Parameter": "", "Add custom prompt": "Adaugă prompt personalizat", "Add Files": "<PERSON><PERSON><PERSON>", "Add Group": "Adaugă grup", "Add Memory": "Adaugă memorie", "Add Model": "Adaugă model", "Add Reaction": "Adaugă reacție", "Add Tag": "<PERSON>ug<PERSON> etiche<PERSON>", "Add Tags": "Adaugă etichete", "Add text content": "Adăugați conținut textual", "Add User": "Adaugă utilizator", "Add User Group": "Adaugă grup de utilizatori", "Adjusting these settings will apply changes universally to all users.": "Ajustarea acestor setări va aplica modificările universal pentru toți utilizatorii.", "admin": "administrator", "Admin": "Administrator", "Admin Panel": "Panoul de administrare", "Admin Settings": "<PERSON><PERSON><PERSON> pen<PERSON> administrator", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Administratorii au acces la toate instrumentele în orice moment; utilizatorii au nevoie de instrumente asignate pe model în spațiul de lucru.", "Advanced Parameters": "<PERSON><PERSON><PERSON>", "Advanced Params": "<PERSON><PERSON><PERSON>", "All": "", "All Documents": "Toate documentele", "All models deleted successfully": "Toate modelele au fost șterse cu succes", "Allow Call": "", "Allow Chat Controls": "Permite controalele chat-ului", "Allow Chat Delete": "Permite ștergerea chat-ului", "Allow Chat Deletion": "Permite ștergerea conversațiilor", "Allow Chat Edit": "Permite editarea chat-ului", "Allow Chat Export": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow File Upload": "Permite încărcarea fi<PERSON>", "Allow Multiple Models in Chat": "", "Allow non-local voices": "Permite voci non-locale", "Allow Speech to Text": "", "Allow Temporary Chat": "Permite chat temporar", "Allow Text to Speech": "", "Allow User Location": "Permite localizarea utilizatorului", "Allow Voice Interruption in Call": "Permite intreruperea vocii în apel", "Allowed Endpoints": "", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "Deja ai un cont?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "Întotdeauna", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Always Play Notification Sound": "", "Amazing": "Uim<PERSON>", "an assistant": "un asistent", "Analyzed": "<PERSON><PERSON><PERSON><PERSON>", "Analyzing...": "Se analizează...", "and": "și", "and {{COUNT}} more": "și {{COUNT}} mai multe", "and create a new shared link.": "și creează un nou link partajat.", "Android": "", "API": "", "API Base URL": "URL Bază API", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "Cheie API", "API Key created.": "Cheie API creată.", "API Key Endpoint Restrictions": "", "API keys": "Chei API", "API Version": "", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "<PERSON><PERSON>", "Archive": "Arhivează", "Archive All Chats": "Arhivează Toate Conversațiile", "Archived Chats": "Conversații Arhivate", "archived-chat-export": "", "Are you sure you want to clear all memories? This action cannot be undone.": "", "Are you sure you want to delete this channel?": "Ești sigur că vrei să ștergi acest canal?", "Are you sure you want to delete this message?": "Ești sigur că vrei să ștergi acest mesaj?", "Are you sure you want to unarchive all archived chats?": "Ești sigur că vrei să dezarhivezi toate convers<PERSON><PERSON><PERSON><PERSON> a<PERSON>?", "Are you sure?": "Ești sigur?", "Arena Models": "Arena Models", "Artifacts": "Artefacte", "Ask": "", "Ask a question": "Pune o întrebare", "Assistant": "Asistent", "Attach file from knowledge": "", "Attention to detail": "Atenție la detalii", "Attribute for Mail": "", "Attribute for Username": "", "Audio": "Audio", "August": "August", "Auth": "", "Authenticate": "Autentificare", "Authentication": "", "Auto": "", "Auto-Copy Response to Clipboard": "Copiere Automată a Răspunsului în Clipboard", "Auto-playback response": "Redare automată a răspunsului", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "Automatic1111 este un proiect popular pentru interfața grafică a utilizatorului a modelelor de difuzie stabilă. Aceasta oferă o interfață web pentru a genera imagini folosind AI și este utilizată pe scară largă pentru a experimenta cu generarea de artă AI.", "AUTOMATIC1111 Api Auth String": "Șir de Autentificare API AUTOMATIC1111", "AUTOMATIC1111 Base URL": "URL Bază AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "Este necesar URL-ul Bază AUTOMATIC1111.", "Available list": "Listă disponibilă", "Available Tools": "", "available!": "disponibil!", "Awful": "", "Azure AI Speech": "Azure AI Speech este un serviciu care face parte din suita de servicii cognitive oferite de Microsoft Azure. Acesta permite integrarea capabilităților de recunoaștere vocală, generare a vorbirii și transcriere automată în aplicații. Servic<PERSON>l oferă dezvoltatorilor posibilitatea de a crea aplicații care pot converti vorbirea în text, genera vorbire naturală din text sau traduce între limbi. Azure AI Speech este util în diverse scenarii, cum ar fi asistenți vocali, aplicații de servicii pentru clienți sau instrumente de accesibilitate, facilitând o interacțiune mai naturală între utilizatori și tehnologie.", "Azure Region": "Regiune Azure", "Back": "Înapoi", "Bad Response": "Răspuns Greșit", "Banners": "<PERSON><PERSON>", "Base Model (From)": "<PERSON> de Bază (De la)", "before": "înainte", "Being lazy": "<PERSON><PERSON>", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Bocha Search API Key": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "Cheie API Brave Search", "By {{name}}": "", "Bypass Embedding and Retrieval": "", "Bypass Web Loader": "", "Calendar": "", "Call": "Apel", "Call feature is not supported when using Web STT engine": "Funcția de apel nu este suportată când se utilizează motorul Web STT", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "Anulează", "Capabilities": "Capabilități", "Capture": "", "Capture Audio": "", "Certificate Path": "", "Change Password": "Schimbă Parola", "Channel Name": "", "Channels": "", "Character": "<PERSON><PERSON>", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "Conversație", "Chat Background Image": "Imagine de Fundal pentru Conversație", "Chat Bubble UI": "Interfață cu Bule de Conversație", "Chat Controls": "Controale pentru Conversație", "Chat direction": "Direcția conversației", "Chat Overview": "Prezentare generală a conversației", "Chat Permissions": "", "Chat Tags Auto-Generation": "Generare automată a etichetelor de conversație", "Chats": "Conversații", "Check Again": "Verifică din Nou", "Check for updates": "Verifică actualizări", "Checking for updates...": "Se verifică actualizările...", "Choose a model before saving...": "Alege un model înainte de a salva...", "Chunk Overlap": "Suprapunere Bloc", "Chunk Size": "Dimensiune Bloc", "Ciphers": "<PERSON><PERSON><PERSON><PERSON>", "Citation": "Citație", "Citations": "", "Clear memory": "Șterge memoria", "Clear Memory": "", "click here": "apasă aici.", "Click here for filter guides.": "Apasă aici pentru ghidul de filtrare.", "Click here for help.": "Apasă aici pentru ajutor.", "Click here to": "Apasă aici pentru", "Click here to download user import template file.": "Apasă aici pentru a descărca fișierul șablon de import utilizator.", "Click here to learn more about faster-whisper and see the available models.": "<PERSON><PERSON>i clic aici pentru a afla mai multe despre faster-whisper și pentru a vedea modelele disponibile.", "Click here to see available models.": "", "Click here to select": "Apasă aici pentru a selecta", "Click here to select a csv file.": "Apasă aici pentru a selecta un fișier csv.", "Click here to select a py file.": "Apasă aici pentru a selecta un fișier py.", "Click here to upload a workflow.json file.": "Faceți clic aici pentru a încărca un fișier workflow.json.", "click here.": "apasă aici.", "Click on the user role button to change a user's role.": "Apasă pe butonul rolului utilizatorului pentru a schimba rolul unui utilizator.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Permisiunea de scriere în clipboard a fost refuzată. Vă rugăm să verificați setările browserului pentru a acorda accesul necesar.", "Clone": "Clonează", "Clone Chat": "Clonează chat", "Clone of {{TITLE}}": "", "Close": "<PERSON><PERSON><PERSON>", "Close modal": "", "Close settings modal": "", "Code execution": "Executarea codului", "Code Execution": "", "Code Execution Engine": "", "Code Execution Timeout": "", "Code formatted successfully": "Cod formatat cu succes", "Code Interpreter": "", "Code Interpreter Engine": "", "Code Interpreter Prompt Template": "", "Collapse": "", "Collection": "<PERSON><PERSON><PERSON><PERSON>", "Color": "<PERSON><PERSON><PERSON>", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "URL De Bază ComfyUI", "ComfyUI Base URL is required.": "Este necesar URL-ul De Bază ComfyUI.", "ComfyUI Workflow": "Flux de lucru ComfyUI", "ComfyUI Workflow Nodes": "Noduri de flux de lucru ComfyUI", "Command": "Comandă", "Completions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Concurrent Requests": "<PERSON><PERSON><PERSON>", "Configure": "", "Confirm": "Confirmă", "Confirm Password": "Confirm<PERSON>", "Confirm your action": "Confirmă acțiunea ta", "Confirm your new password": "", "Connect to your own OpenAI compatible API endpoints.": "", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "Conexiuni", "Connections saved successfully": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "Contactează administratorul pentru acces WebUI", "Content": "<PERSON><PERSON><PERSON><PERSON>", "Content Extraction Engine": "", "Continue Response": "Continuă Răspunsul", "Continue with {{provider}}": "Continuă cu {{provider}}", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Controlează modul în care textul mesajului este divizat pentru cererile TTS. 'Punctuation' împarte în propoziții, 'paragraphs' împarte în paragrafe, iar 'none' menține mesajul ca un șir unic.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "Con<PERSON>ale", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Copied": "Copiat", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "URL-ul conversației partajate a fost copiat în clipboard!", "Copied to clipboard": "Copiat în clipboard", "Copy": "Copiază", "Copy Formatted Text": "", "Copy last code block": "Copiază ultimul bloc de cod", "Copy last response": "Copiază ultimul răspuns", "Copy Link": "Copiază Link", "Copy to clipboard": "Copiază în clipboard", "Copying to clipboard was successful!": "Copierea în clipboard a fost realizată cu succes!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "", "Create": "", "Create a knowledge base": "", "Create a model": "Creează un model", "Create Account": "Creează Cont", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "Creează cunoștințe", "Create new key": "Creează cheie nouă", "Create new secret key": "Creează cheie secretă nouă", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "<PERSON>reat la", "Created At": "<PERSON><PERSON><PERSON>", "Created by": "<PERSON><PERSON><PERSON> de", "CSV Import": "Import CSV", "Ctrl+Enter to Send": "", "Current Model": "Model Curent", "Current Password": "<PERSON><PERSON><PERSON>", "Custom": "Personalizat", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "", "Dark": "Întunecat", "Database": "Bază de Date", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "December": "Decembrie", "Default": "Implicit", "Default (Open AI)": "Implicit (Open AI)", "Default (SentenceTransformers)": "Implicit (SentenceTransformers)", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "Model Implicit", "Default model updated": "Modelul implicit a fost actualizat", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "Sugestii de Prompt Implicite", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "Rolul Implicit al Utilizatorului", "Delete": "Șterge", "Delete a model": "Șterge un model", "Delete All Chats": "Șterge Toate Conversațiile", "Delete All Models": "", "Delete chat": "Șterge conversația", "Delete Chat": "Șterge Conversația", "Delete chat?": "Șterge conversația?", "Delete folder?": "Ștergeți folderul?", "Delete function?": "Șterge funcția?", "Delete Message": "", "Delete message?": "", "Delete note?": "", "Delete prompt?": "Șterge promptul?", "delete this link": "șterge acest link", "Delete tool?": "Șterge instrumentul?", "Delete User": "Șterge Utilizatorul", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} șters", "Deleted {{name}}": "{{name}} <PERSON><PERSON>", "Deleted User": "", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "", "Description": "Des<PERSON><PERSON><PERSON>", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "Nu a urmat complet instrucțiunile", "Direct": "", "Direct Connections": "", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "", "Direct Connections settings updated": "", "Direct Tool Servers": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "Dezactivat", "Discover a function": "Descoperă o funcție", "Discover a model": "Descoperă un model", "Discover a prompt": "Descoperă un prompt", "Discover a tool": "Descoperă un instrument", "Discover how to use Open WebUI and seek support from the community.": "", "Discover wonders": "", "Discover, download, and explore custom functions": "Descoperă, descarcă și explorează funcții personalizate", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON><PERSON>, descarcă și explorează prompturi personalizate", "Discover, download, and explore custom tools": "Descoperă, descarcă și explorează instrumente personalizate", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON><PERSON>, descarcă și explorează presetări de model", "Dismissible": "Ignorabil", "Display": "", "Display Emoji in Call": "Afișează Emoji în Apel", "Display the username instead of You in the Chat": "Afișează numele utilizatorului în loc de Tu în Conversație", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "Nu instalați funcții din surse în care nu aveți încredere completă.", "Do not install tools from sources you do not fully trust.": "Nu instalați instrumente din surse în care nu aveți încredere completă.", "Docling": "", "Docling Server URL required.": "", "Document": "Document", "Document Intelligence": "", "Document Intelligence endpoint and key required.": "", "Documentation": "Documentație", "Documents": "Documente", "does not make any external connections, and your data stays securely on your locally hosted server.": "nu face nicio cone<PERSON>une externă, iar datele tale rămân în siguranță pe serverul găzduit local.", "Domain Filter List": "", "Don't have an account?": "Nu ai un cont?", "don't install random functions from sources you don't trust.": "nu instala funcții aleatorii din surse în care nu ai încredere.", "don't install random tools from sources you don't trust.": "nu instala instrumente aleatorii din surse în care nu ai încredere.", "Don't like the style": "Nu îți place stilul", "Done": "Gata", "Download": "Des<PERSON><PERSON><PERSON>", "Download as SVG": "", "Download canceled": "Des<PERSON><PERSON><PERSON><PERSON> an<PERSON>", "Download Database": "Descarcă Baza de Date", "Drag and drop a file to upload or select a file to view": "", "Draw": "Desenează", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "de ex. '30s', '10m'. Unitățile de timp valide sunt 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "", "e.g. A filter to remove profanity from text": "", "e.g. en": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "e.g.) en,fr,de": "", "Edit": "Editează", "Edit Arena Model": "Editați Modelul Arena", "Edit Channel": "Editează canalul", "Edit Connection": "Editează cone<PERSON>a", "Edit Default Permissions": "Editează permisiunile implicite", "Edit Memory": "Editează Memorie", "Edit User": "Editează Utilizator", "Edit User Group": "Editează grupul de utilizatori", "Eject": "", "ElevenLabs": "ElevenLabs", "Email": "Email", "Embark on adventures": "", "Embedding": "", "Embedding Batch Size": "Dimensiune Lot de Încapsulare", "Embedding Model": "<PERSON> <PERSON> Î<PERSON>", "Embedding Model Engine": "Motor de Model de Încapsulare", "Embedding model set to \"{{embedding_model}}\"": "Modelul de încapsulare setat la \"{{embedding_model}}\"", "Enable API Key": "Activează cheia API", "Enable autocomplete generation for chat messages": "Activează generarea automată pentru mesajele de chat", "Enable Code Execution": "", "Enable Code Interpreter": "Activează interpretul de cod", "Enable Community Sharing": "Activează Partajarea Comunitară", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "Activează Evaluarea Mesajelor", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "Activează Înscrierile Noi", "Enabled": "Activat", "Endpoint URL": "", "Enforce Temporary Chat": "", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Asigurați-vă că fișierul CSV include 4 coloane în această ordine: Nume, Email, Parolă, Rol.", "Enter {{role}} message here": "Introduceți mesajul pentru {{role}} aici", "Enter a detail about yourself for your LLMs to recall": "Introduceți un detaliu despre dvs. pe care LLM-urile să-l rețină", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "Introduceți șirul de autentificare API (de ex. username:password)", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter BM25 Weight": "", "Enter Bocha Search API Key": "", "Enter Brave Search API Key": "Introduceți Cheia API Brave Search", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "Introduceți Scara CFG (de ex. 7.0)", "Enter Chunk Overlap": "Introduceți Suprapunerea Blocului", "Enter Chunk Size": "Introduceți Dimensiunea Blocului", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter Datalab Marker API Key": "", "Enter description": "Introduceți descrierea", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "", "Enter Document Intelligence Key": "", "Enter domains separated by commas (e.g., example.com,site.org)": "", "Enter Exa API Key": "", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter Github Raw URL": "Introduceți URL-ul Raw de pe Github", "Enter Google PSE API Key": "Introduceți Cheia API Google PSE", "Enter Google PSE Engine Id": "Introduceți ID-ul Motorului Google PSE", "Enter Image Size (e.g. 512x512)": "Introduceți Dimensiunea Imaginii (de ex. 512x512)", "Enter Jina API Key": "", "Enter Jupyter Password": "", "Enter Jupyter Token": "", "Enter Jupyter URL": "", "Enter Kagi Search API Key": "", "Enter Key Behavior": "", "Enter language codes": "Introduceți codurile limbilor", "Enter Mistral API Key": "", "Enter Model ID": "Introdu codul modelului", "Enter model tag (e.g. {{modelTag}})": "Introduceți eticheta modelului (de ex. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "Introduceți Numărul de <PERSON>i (de ex. 50)", "Enter Perplexity API Key": "", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "", "Enter reasoning effort": "", "Enter Sampler (e.g. Euler a)": "Introduce <PERSON> (de exemplu, Euler a)", "Enter Scheduler (e.g. Karras)": "Introduceți Programatorul (de exemplu, Karras)", "Enter Score": "Introduceți Scorul", "Enter SearchApi API Key": "Introduceți cheia API SearchApi", "Enter SearchApi Engine": "Introduceți motorul SearchApi", "Enter Searxng Query URL": "Introduceți URL-ul Interogării Searxng", "Enter Seed": "", "Enter SerpApi API Key": "", "Enter SerpApi Engine": "", "Enter Serper API Key": "Introduceți Cheia API Serper", "Enter Serply API Key": "Introduceți Cheia API Serply", "Enter Serpstack API Key": "Introduceți Cheia API Serpstack", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "Introduceți secvența de oprire", "Enter system prompt": "Introduceți promptul de sistem", "Enter system prompt here": "", "Enter Tavily API Key": "Introduceți Cheia API Tavily", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "Introduceți URL-ul Serverului Tika", "Enter timeout in seconds": "", "Enter to Send": "", "Enter Top K": "Introduceți Top K", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "Introduceți URL-ul (de ex. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Introduceți URL-ul (de ex. http://localhost:11434)", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your current password": "", "Enter Your Email": "Introduceți Email-ul Dvs.", "Enter Your Full Name": "Introduceți Numele Dvs. Complet", "Enter your message": "Introduceți mesajul dvs.", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "", "Enter Your Password": "Introduceți Parola Dvs.", "Enter Your Role": "Introduceți Rolul Dvs.", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "Eroare", "ERROR": "EROARE", "Error accessing Google Drive: {{error}}": "", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "<PERSON><PERSON><PERSON><PERSON>", "Exa API Key": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: mail": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "", "Exclude": "Exclude", "Execute code for analysis": "", "Executing **{{NAME}}**...": "", "Expand": "", "Experimental": "Experimental", "Explain": "", "Explore the cosmos": "", "Export": "Exportă", "Export All Archived Chats": "", "Export All Chats (All Users)": "Exportă Toate Conversațiile (Toți Utilizatorii)", "Export chat (.json)": "Exportă conversația (.json)", "Export Chats": "Exportă Conversațiile", "Export Config to JSON File": "Exportă Configurația în Fișier JSON", "Export Functions": "Exportă Funcțiile", "Export Models": "Exportă Modelele", "Export Presets": "", "Export Prompt Suggestions": "", "Export Prompts": "Exportă Prompturile", "Export to CSV": "", "Export Tools": "Exportă Instrumentele", "External": "", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Failed to add file.": "Eșec la adăugarea fișierului.", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to copy link": "", "Failed to create API Key.": "Crearea cheii API a eșuat.", "Failed to delete note": "", "Failed to fetch models": "", "Failed to load file content.": "", "Failed to read clipboard contents": "Citirea conținutului clipboard-ului a eș<PERSON>t", "Failed to save connections": "", "Failed to save models configuration": "", "Failed to update settings": "Actualizarea setă<PERSON> a <PERSON>șuat", "Failed to upload file.": "Încărcarea fișierului a eșuat.", "Features": "", "Features Permissions": "", "February": "<PERSON><PERSON><PERSON><PERSON>", "Feedback Details": "", "Feedback History": "Istoricul feedback-ului", "Feedbacks": "", "Feel free to add specific details": "Adăugați detalii specifice fără nicio ezitare", "File": "<PERSON><PERSON><PERSON>", "File added successfully.": "Fișierul a fost adăugat cu succes.", "File content updated successfully.": "Conținutul fișierului a fost actualizat cu succes.", "File Mode": "<PERSON><PERSON>", "File not found.": "Fișierul nu a fost găsit.", "File removed successfully.": "Fișierul a fost eliminat cu succes.", "File size should not exceed {{maxSize}} MB.": "Dimensiunea fișierului nu ar trebui să depășească {{maxSize}} MB.", "File Upload": "", "File uploaded successfully": "", "Files": "Fișiere", "Filter is now globally disabled": "Filtrul este acum dezactivat global", "Filter is now globally enabled": "Filtrul este acum activat global", "Filters": "Filtre", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Detectată falsificarea amprentelor: Nu se pot folosi inițialele ca avatar. Se utilizează imaginea de profil implicită.", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Fluidly stream large external response chunks": "Transmite fluent blocuri mari de răspuns extern", "Focus chat input": "Focalizează câmpul de intrare pentru conversație", "Folder deleted successfully": "Folder șters cu succes", "Folder name cannot be empty.": "Numele folderului nu poate fi gol.", "Folder name updated successfully": "Numele folderului a fost actualizat cu succes", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "A urmat instrucțiunile perfect", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "", "Form": "Formular", "Format your variables using brackets like this:": "Formatează variabilele folosind acolade așa:", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "", "Function": "Fun<PERSON><PERSON><PERSON>", "Function Calling": "", "Function created successfully": "Funcția a fost creată cu succes", "Function deleted successfully": "Funcția a fost ștearsă cu succes", "Function Description": "", "Function ID": "", "Function imported successfully": "", "Function is now globally disabled": "Funcția este acum dezactivată global", "Function is now globally enabled": "Funcția este acum activată global", "Function Name": "", "Function updated successfully": "Funcția a fost actualizată cu succes", "Functions": "Funcții", "Functions allow arbitrary code execution.": "Funcțiile permit executarea arbitrară a codului.", "Functions imported successfully": "Funcțiile au fost importate cu succes", "Gemini": "", "Gemini API Config": "", "Gemini API Key is required.": "", "General": "General", "Generate": "", "Generate an image": "", "Generate Image": "Generează Imagine", "Generate prompt pair": "", "Generating search query": "Se generează interogarea de căutare", "Generating...": "", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "Global", "Good Response": "Răspuns Bun", "Google Drive": "", "Google PSE API Key": "Cheie API Google PSE", "Google PSE Engine Id": "ID Motor Google PSE", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "Haptic Feedback": "Feedback haptic", "Hello, {{name}}": "Salut, {{name}}", "Help": "<PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "Ajută-ne să creăm cel mai bun clasament al comunității împărtășind istoricul tău de feedback!", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "Ascunde", "Hide from Sidebar": "", "Hide Model": "", "High Contrast Mode": "", "Home": "", "Host": "", "How can I help you today?": "Cum te pot ajuta astăzi?", "How would you rate this response?": "", "HTML": "", "Hybrid Search": "<PERSON><PERSON><PERSON><PERSON>", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Recunosc că am citit și înțeleg implicațiile acțiunii mele. Sunt conștient de riscurile asociate cu executarea codului arbitrar și am verificat fiabilitatea sursei.", "ID": "ID", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "", "Image": "", "Image Compression": "", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "", "Image Generation (Experimental)": "<PERSON><PERSON><PERSON> (Experimental)", "Image Generation Engine": "Motor de Generare a Imaginilor", "Image Max Compression Size": "", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "", "Image Prompt Generation Prompt": "", "Image Settings": "<PERSON><PERSON><PERSON>", "Images": "<PERSON><PERSON><PERSON><PERSON>", "Import": "", "Import Chats": "Importă Conversațiile", "Import Config from JSON File": "Importarea configurației dintr-un fișier JSON", "Import From Link": "", "Import Functions": "Importă Funcțiile", "Import Models": "Importă Modelele", "Import Notes": "", "Import Presets": "", "Import Prompt Suggestions": "", "Import Prompts": "Importă Prompturile", "Import Tools": "Importă Instrumentele", "Include": "Include", "Include `--api-auth` flag when running stable-diffusion-webui": "Includeți flag-ul `--api-auth` când rula<PERSON>i stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Includeți flag-ul `--api` c<PERSON>d r<PERSON>i stable-diffusion-webui", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "Informații", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input commands": "Comenz<PERSON> de intrare", "Install from Github URL": "Instalează de la URL-ul Github", "Instant Auto-Send After Voice Transcription": "Trimitere Automată Instantanee După Transcrierea Vocii", "Integration": "", "Interface": "Interfață", "Invalid file content": "", "Invalid file format.": "Format de fișier invalid.", "Invalid JSON file": "", "Invalid Tag": "Etichetă Invalidă", "is typing...": "", "January": "<PERSON><PERSON><PERSON>", "Jina API Key": "", "join our Discord for help.": "alătură-te Discord-ului nostru pentru ajutor.", "JSON": "JSON", "JSON Preview": "Previzualizare JSON", "July": "<PERSON><PERSON><PERSON>", "June": "<PERSON><PERSON><PERSON>", "Jupyter Auth": "", "Jupyter URL": "", "JWT Expiration": "Expirarea JWT", "JWT Token": "Token JWT", "Kagi Search API Key": "", "Keep in Sidebar": "", "Key": "", "Keyboard shortcuts": "Scurtături de la Tastatură", "Knowledge": "Cunoștințe", "Knowledge Access": "", "Knowledge created successfully.": "Cunoașterea a fost creată cu succes.", "Knowledge deleted successfully.": "Cunoștințele au fost șterse cu succes.", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "Resetarea cunoștințelor a fost efectuată cu succes.", "Knowledge updated successfully": "Cunoașterea a fost actualizată cu succes", "Kokoro.js (Browser)": "", "Kokoro.js Dtype": "", "Label": "", "Landing Page Mode": "<PERSON><PERSON><PERSON> Aterizare", "Language": "Limbă", "Language Locales": "", "Languages": "", "Last Active": "Ultima Activitate", "Last Modified": "Ultima Modificare", "Last reply": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "<PERSON>bel de c<PERSON>", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "Lăsați gol pentru nelimitat", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "Lăsați gol pentru a include toate modelele sau selectați modele specifice", "Leave empty to use the default prompt, or enter a custom prompt": "Lăsați gol pentru a utiliza promptul implicit sau introduceți un prompt personalizat", "Leave model field empty to use the default model.": "", "License": "", "Light": "Luminos", "Listening...": "Ascult...", "Llama.cpp": "", "LLMs can make mistakes. Verify important information.": "LLM-urile pot face greșeli. Verificați informațiile importante.", "Loader": "", "Loading Kokoro.js...": "", "Local": "", "Local Task Model": "", "Location access not allowed": "", "Lost": "<PERSON><PERSON><PERSON>", "LTR": "LTR", "Made by Open WebUI Community": "Realizat de Comunitatea OpenWebUI", "Make password visible in the user interface": "", "Make sure to enclose them with": "Asigurați-vă că le închideți cu", "Make sure to export a workflow.json file as API format from ComfyUI.": "Asigură-te că exporți un fișier {{workflow.json}} în format API din {{ComfyUI}}.", "Manage": "Gestionează", "Manage Direct Connections": "", "Manage Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "Gestionează Conductele", "Manage Tool Servers": "", "March": "<PERSON><PERSON>", "Markdown": "", "Max Speakers": "", "Max Upload Count": "<PERSON>um<PERSON>r maxi<PERSON><PERSON>", "Max Upload Size": "Dimensiune Maximă de Încărcare", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Maxim 3 modele pot fi descărcate simultan. Vă rugăm să încercați din nou mai târziu.", "May": "<PERSON>", "Memories accessible by LLMs will be shown here.": "Memoriile accesibile de LLM-uri vor fi afișate aici.", "Memory": "Memorie", "Memory added successfully": "Memoria a fost adăugată cu succes", "Memory cleared successfully": "Memoria a fost ștearsă cu succes", "Memory deleted successfully": "Memoria a fost ștearsă cu succes", "Memory updated successfully": "Memoria a fost actualizată cu succes", "Merge Responses": "Combină răspunsurile", "Merged Response": "Răspuns Combinat", "Message rating should be enabled to use this feature": "Evaluarea mesajelor ar trebui să fie activată pentru a utiliza această funcționalitate.", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Mesajele pe care le trimiteți după crearea link-ului dvs. nu vor fi partajate. Utilizatorii cu URL-ul vor putea vizualiza conversația partajată.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "Model", "Model '{{modelName}}' has been successfully downloaded.": "Modelul '{{modelName}}' a fost descărcat cu succes.", "Model '{{modelTag}}' is already in queue for downloading.": "Modelul '{{modelTag}}' este deja în coada de descărcare.", "Model {{modelId}} not found": "Modelul {{modelId}} nu a fost găsit", "Model {{modelName}} is not vision capable": "Modelul {{modelName}} nu are capacități de viziune", "Model {{name}} is now {{status}}": "Modelul {{name}} este acum {{status}}", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts file inputs": "", "Model accepts image inputs": "Modelul acceptă imagini ca intrări.", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "Modelul a fost creat cu succes!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Calea sistemului de fișiere al modelului detectată. Este necesar numele scurt al modelului pentru actualizare, nu se poate continua.", "Model Filtering": "", "Model ID": "ID Model", "Model IDs": "", "Model Name": "Nume model", "Model not selected": "Modelul nu a fost selectat", "Model Params": "Parametri Model", "Model Permissions": "", "Model unloaded successfully": "", "Model updated successfully": "Modelul a fost actualizat cu succes", "Model(s) do not support file upload": "", "Modelfile Content": "Conținutul Fișierului Model", "Models": "<PERSON>e", "Models Access": "", "Models configuration saved successfully": "", "Models Public Sharing": "", "Mojeek Search API Key": "", "more": "mai mult", "More": "<PERSON> multe", "My Notes": "", "Name": "Nume", "Name your knowledge base": "", "Native": "", "New Chat": "Conversație Nouă", "New Folder": "", "New Function": "", "New Note": "", "New Password": "<PERSON><PERSON><PERSON>", "New Tool": "", "new-channel": "", "Next message": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "Nu a fost găsit niciun conținut", "No content found in file.": "", "No content to speak": "Nu există conținut de vorbit", "No distance available": "<PERSON><PERSON> distan<PERSON> disponibilă", "No feedbacks found": "<PERSON><PERSON><PERSON> <PERSON> găsit", "No file selected": "Nu a fost selectat niciun fișier", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "<PERSON>ciun conținut HTML, CSS sau JavaScript găsit.", "No inference engine with management support found": "", "No knowledge found": "Nu au fost găsite informații.", "No memories to clear": "", "No model IDs": "", "No models found": "Nu s-au gă<PERSON>t modele", "No models selected": "", "No Notes": "", "No results found": "Nu au fost găsite rezultate", "No search query generated": "Nu a fost generată nicio interogare de căutare", "No source available": "<PERSON><PERSON> sur<PERSON>ă disponibilă", "No users were found.": "", "No valves to update": "Nu există valve de actualizat", "None": "Niciunul", "Not factually correct": "Nu este corect din punct de vedere factual", "Not helpful": "Nu este de ajutor", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Notă: <PERSON><PERSON><PERSON> setați un scor minim, căutarea va returna doar documente cu un scor mai mare sau egal cu scorul minim.", "Notes": "Note", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Notific<PERSON><PERSON>", "November": "Noiembrie", "OAuth ID": "ID OAuth", "October": "<PERSON><PERSON><PERSON>", "Off": "Dezactivat", "Okay, Let's Go!": "Ok, <PERSON><PERSON> Începem!", "OLED Dark": "Întunecat OLED", "Ollama": "Ollama", "Ollama API": "API Ollama", "Ollama API settings updated": "", "Ollama Version": "Versiune <PERSON>", "On": "Activat", "OneDrive": "", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "Doar caracterele alfanumerice și cratimele sunt permise în șirul de comandă.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Doar colecțiile pot fi editate, creați o nouă bază de cunoștințe pentru a edita/adăuga documente.", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Oops! Se pare că URL-ul este invalid. Vă rugăm să verificați din nou și să încercați din nou.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Ups! Încă mai există fișiere care se încarcă. Vă rugăm să așteptați până se finalizează încărcarea.", "Oops! There was an error in the previous response.": "Ups! A apărut o eroare în răspunsul anterior.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Oops! Utilizați o metodă nesuportată (doar frontend). Vă rugăm să serviți WebUI din backend.", "Open file": "<PERSON><PERSON><PERSON> fi<PERSON>ul", "Open in full screen": "Deschide în ecran complet", "Open modal to configure connection": "", "Open new chat": "Deschide conversație nouă", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "Open WebUI folosește faster-whisper intern.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Versiunea Open WebUI (v{{OPEN_WEBUI_VERSION}}) este mai mică decât versiunea necesară (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "API OpenAI", "OpenAI API Config": "Configurația API OpenAI", "OpenAI API Key is required.": "Este necesară cheia API OpenAI.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "Este necesar URL-ul/Cheia OpenAI.", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "sau", "Organize your users": "", "Other": "Altele", "OUTPUT": "Output rezultatat", "Output format": "Formatul de ieșire", "Output Format": "", "Overview": "Privire <PERSON> ansa<PERSON>lu", "page": "pagina", "Paginate": "", "Parameters": "", "Password": "Pa<PERSON><PERSON>", "Paste Large Text as File": "", "PDF document (.pdf)": "Document PDF (.pdf)", "PDF Extract Images (OCR)": "Extrage Imagini PDF (OCR)", "pending": "<PERSON>n <PERSON>", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "Permisiunea refuzată la accesarea dispozitivelor media", "Permission denied when accessing microphone": "Permisiunea refuzată la accesarea microfonului", "Permission denied when accessing microphone: {{error}}": "Permisiunea refuzată la accesarea microfonului: {{error}}", "Permissions": "", "Perplexity API Key": "", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "Personalizare", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "Fixează", "Pinned": "Fixat", "Pioneer insights": "", "Pipeline deleted successfully": "Conducta a fost ștearsă cu succes", "Pipeline downloaded successfully": "Conducta a fost descărcată cu succes", "Pipelines": "Conducte", "Pipelines Not Detected": "Conducte Nedetectate", "Pipelines Valves": "<PERSON><PERSON><PERSON>", "Plain text (.md)": "", "Plain text (.txt)": "Text simplu (.txt)", "Playground": "<PERSON><PERSON>", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "Vă rugăm să revizuiți cu atenție următoarele avertismente:", "Please do not close the settings page while loading the model.": "", "Please enter a prompt": "Te rog să introduci un mesaj", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "<PERSON><PERSON> rugăm să completați toate <PERSON><PERSON><PERSON><PERSON><PERSON>.", "Please select a model first.": "", "Please select a model.": "", "Please select a reason": "<PERSON>ă rugăm să selectați un motiv", "Port": "", "Positive attitude": "<PERSON><PERSON><PERSON><PERSON>", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Prevent file creation": "", "Preview": "", "Previous 30 days": "Ultimele 30 de zile", "Previous 7 days": "Ultimele 7 zile", "Previous message": "", "Private": "", "Profile Image": "Imagine de Profil", "Prompt": "", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (de ex. Spune-mi un fapt amuzant despre Imperiul Roman)", "Prompt Autocompletion": "", "Prompt Content": "Conținut Prompt", "Prompt created successfully": "", "Prompt suggestions": "Sugestii de Prompt", "Prompt updated successfully": "", "Prompts": "Prompturi", "Prompts Access": "", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "Extrage \"{{searchValue}}\" de pe Ollama.com", "Pull a model from Ollama.com": "Extrage un model de pe Ollama.com", "Query Generation Prompt": "", "RAG Template": "Șablon RAG", "Rating": "Evaluare", "Re-rank models by topic similarity": "Reordonează modelele în funcție de similaritatea tematică", "Read": "Citește", "Read Aloud": "Citește cu Voce Tare", "Reason": "", "Reasoning Effort": "", "Record": "", "Record voice": "Înregistrează vocea", "Redirecting you to Open WebUI Community": "Vă redirecționăm către Comunitatea OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Referiți-vă la dvs. ca \"Utilizator\" (de ex., \"Utilizatorul învață spaniolă\")", "References from": "Refer<PERSON><PERSON><PERSON> din", "Refused when it shouldn't have": "Refuzat când nu ar fi trebuit", "Regenerate": "Regenerare", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "Note de Lansare", "Releases": "", "Relevance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Relevance Threshold": "", "Remove": "Înlătură", "Remove {{MODELID}} from list.": "", "Remove Model": "Înlătură Modelul", "Remove this tag from list": "", "Rename": "Redenumește", "Reorder Models": "", "Reply in Thread": "", "Reranking Engine": "", "Reranking Model": "<PERSON> <PERSON>", "Reset": "Resetează", "Reset All Models": "", "Reset Upload Directory": "Resetează Directorul de Încărcare", "Reset Vector Storage/Knowledge": "Resetarea Stocării/Vectoului de Cunoștințe", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Notificările de răspuns nu pot fi activate deoarece permisiunile site-ului au fost refuzate. Vă rugăm să vizitați setările browserului pentru a acorda accesul necesar.", "Response splitting": "Împărțirea răspunsurilor", "Response Watermark": "", "Result": "Rezultat", "Retrieval": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "Introducere text îmbogățit pentru chat", "RK": "RK", "Role": "Rol", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "Execută", "Running": "Rulează", "Save": "Salvează", "Save & Create": "Salvează & Creează", "Save & Update": "Salvează & Actualizează", "Save As Copy": "Salvează ca Copie", "Save Tag": "Salvează Eticheta", "Saved": "<PERSON><PERSON>", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Salvarea jurnalelor de conversație direct în stocarea browserului dvs. nu mai este suportată. Vă rugăm să luați un moment pentru a descărca și a șterge jurnalele de conversație făcând clic pe butonul de mai jos. Nu vă faceți gri<PERSON>, puteți reimporta ușor jurnalele de conversație în backend prin", "Scroll On Branch Change": "", "Search": "Caut<PERSON>", "Search a model": "Caută un model", "Search Base": "", "Search Chats": "Caută în Conversații", "Search Collection": "<PERSON><PERSON><PERSON><PERSON>", "Search Filters": "", "search for tags": "caută etichete", "Search Functions": "Caută Funcții", "Search Knowledge": "<PERSON><PERSON><PERSON><PERSON>", "Search Models": "Caută Modele", "Search options": "", "Search Prompts": "Caut<PERSON>", "Search Result Count": "<PERSON><PERSON><PERSON><PERSON>", "Search the internet": "", "Search Tools": "Caută Instrumente", "SearchApi API Key": "Cheie API pentru SearchApi", "SearchApi Engine": "Motorul SearchApi", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON><PERSON> \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON><PERSON> cu<PERSON> pentru \"{{searchQuery}}\"", "Searching the web...": "", "Searxng Query URL": "URL Interogare Searxng", "See readme.md for instructions": "Consultați readme.md pentru instrucțiuni", "See what's new": "Vezi ce e nou", "Seed": "", "Select a base model": "Selectează un model de bază", "Select a engine": "Selectează un motor", "Select a function": "Selectează o funcție", "Select a group": "", "Select a model": "Selectează un model", "Select a pipeline": "Selectează o conductă", "Select a pipeline url": "Selectează un URL de conductă", "Select a tool": "Selectează un instrument", "Select an auth method": "", "Select an Ollama instance": "", "Select Engine": "Selectează motorul", "Select Knowledge": "Selectarea cunoștințelor (Knowledge Selection) este un proces esențial în multiple domenii, incluzând inteligența artificială și învățarea automată. Aceasta presupune alegerea corectă a informațiilor sau datelor relevante dintr-un set mai mare pentru a le utiliza în analize, modele sau sisteme specifice. De exemplu, în învățarea automată, selectarea caracteristicilor este un aspect al selectării cunoștințelor și implică alegerea celor mai relevante date de intrare care contribuie la îmbunătățirea preciziei modelului.", "Select only one model to call": "Selecteaz<PERSON> doar un singur model pentru apel", "Selected model(s) do not support image inputs": "Modelul(e) selectat(e) nu suportă intrări de imagine", "Semantic distance to query": "Distanța semantică față de interogare", "Send": "Trimite", "Send a Message": "Trimite un Mesaj", "Send message": "Trimite mesajul", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Trimite `stream_options: { include_usage: true }` în cerere. Furnizorii care suportă această opțiune vor returna informații despre utilizarea token-urilor în răspuns când este setată.", "September": "Septembrie", "SerpApi API Key": "", "SerpApi Engine": "", "Serper API Key": "Cheie API Serper", "Serply API Key": "Cheie API Serply", "Serpstack API Key": "Cheie API Serpstack", "Server connection verified": "Conexiunea la server a fost verificată", "Set as default": "Setează ca implicit", "Set CFG Scale": "Setează scala CFG", "Set Default Model": "Setează Modelul Implicit", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Setează modelul de încapsulare (de ex. {{model}})", "Set Image Size": "Setează Dimensiunea Imaginilor", "Set reranking model (e.g. {{model}})": "Setează modelul de rearanjare (de ex. {{model}})", "Set Sampler": "Set Samply.", "Set Scheduler": "Setare Programatorului de Sarcini", "Set Steps": "<PERSON><PERSON><PERSON><PERSON>", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "Setează Voce", "Set whisper model": "Setează modelul whisper", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "<PERSON><PERSON><PERSON>", "Settings saved successfully!": "Setările au fost salvate cu succes!", "Share": "Partajează", "Share Chat": "Partajează Conversația", "Share to Open WebUI Community": "Partajează cu Comunitatea OpenWebUI", "Sharing Permissions": "", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "<PERSON><PERSON>ș<PERSON><PERSON>", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "Afișează Detaliile Administratorului în Suprapunerea Contului În Așteptare", "Show All": "", "Show Less": "", "Show Model": "", "Show shortcuts": "Afișeaz<PERSON> scurtături", "Show your support!": "Arată-ți susținerea!", "Showcased creativity": "Creativitate expusă", "Sign in": "Autentificare", "Sign in to {{WEBUI_NAME}}": "Conectează-te la {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "Deconectare", "Sign up": "Înregistrare", "Sign up to {{WEBUI_NAME}}": "Înregistrează-te la {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "", "Signing in to {{WEBUI_NAME}}": "Autentificare în {{WEBUI_NAME}}", "sk-1234": "", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "Sursă", "Speech Playback Speed": "Viteza de redare a vorbirii", "Speech recognition error: {{error}}": "<PERSON><PERSON><PERSON> de recunoaștere vocală: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "Motor de Conversie a Vocii în Text", "Stop": "Oprire", "Stop Generating": "", "Stop Sequence": "Oprește Secvența", "Stream Chat Response": "Răspuns Stream Chat", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "Model STT", "STT Settings": "Setări STT", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "Subtitlu (de ex. despre Imperiul Roman)", "Success": "Succes", "Successfully updated.": "Actualizat cu succes.", "Suggested": "Sugerat", "Support": "Suport", "Support this plugin:": "Susține acest plugin:", "Supported MIME Types": "", "Sync directory": "Sincronizează directorul", "System": "Sistem", "System Instructions": "Instrucțiuni pentru sistem", "System Prompt": "Prompt de Sistem", "Tags": "", "Tags Generation": "", "Tags Generation Prompt": "Generarea de Etichete Prompt", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "", "Tap to interrupt": "Apasă pentru a întrerupe", "Task Model": "", "Tasks": "", "Tavily API Key": "Cheie <PERSON>ly", "Tavily Extract Depth": "", "Tell us more:": "<PERSON><PERSON><PERSON>-ne mai multe:", "Temperature": "Temperatură", "Temporary Chat": "<PERSON><PERSON>", "Text Splitter": "Divizor de Text", "Text-to-Speech": "", "Text-to-Speech Engine": "Motor de Conversie a Textului în Vorbire", "Thanks for your feedback!": "Mulțumim pentru feedback!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Dezvoltatorii din spatele acestui plugin sunt voluntari pasionați din comunitate. Dacă considerați acest plugin util, vă rugăm să luați în considerare contribuția la dezvoltarea sa.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Clasamentul de evaluare se bazează pe sistemul de rating Elo și este actualizat în timp real.", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Clasamentul este în prezent în versiune beta și este posibil să ajustăm calculul evaluărilor pe măsură ce rafinăm algoritmul.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Dimensiunea maximă a fișierului în MB. Dacă dimensiunea fișierului depășește această limită, fișierul nu va fi încărcat.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Numărul maxim de fișiere care pot fi utilizate simultan în chat. Dacă numărul de fișiere depășește această limită, fișierele nu vor fi încărcate.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Scorul ar trebui să fie o valoare între 0.0 (0%) și 1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "Temă", "Thinking...": "Gândește...", "This action cannot be undone. Do you wish to continue?": "Această acțiune nu poate fi anulată. Doriți să continuați?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won't appear in history and your messages will not be saved.": "", "This chat won’t appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Acest lucru asigură că conversațiile dvs. valoroase sunt salvate în siguranță în baza de date a backend-ului dvs. Mulțumim!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Aceasta este o funcție experimentală, poate să nu funcționeze așa cum vă așteptați și este supusă schimbării în orice moment.", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Această opțiune va șterge toate fișierelor existente din colecție și le va înlocui cu fișierele nou încărcate.", "This response was generated by \"{{model}}\"": "Acest răspuns a fost generat de \"{{model}}\"", "This will delete": "Aceasta va șterge", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "Acest lucru va șterge <strong>{{NAME}}</strong> și <strong>toate conținuturile sale</strong>.", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Aceasta va reseta baza de cunoștințe și va sincroniza toate fișierele. Doriți să continuați?", "Thorough explanation": "Explicație detaliată", "Thought for {{DURATION}}": "", "Thought for {{DURATION}} seconds": "", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Este necesar URL-ul serverului Tika.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Sfat: Actualizați mai multe sloturi de variabile consecutiv apăsând tasta tab în câmpul de intrare al conversației după fiecare înlocuire.", "Title": "Titlu", "Title (e.g. Tell me a fun fact)": "Titlu (de ex. Spune-mi un fapt amuzant)", "Title Auto-Generation": "Generare Automată a Titlului", "Title cannot be an empty string.": "Titlul nu poate fi un șir gol.", "Title Generation": "", "Title Generation Prompt": "Prompt de Generare a Titlului", "TLS": "", "To access the available model names for downloading,": "Pentru a accesa numele modelelor disponibile pentru descărcare,", "To access the GGUF models available for downloading,": "Pentru a accesa modelele GGUF disponibile pentru descărcare,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Pentru a accesa WebUI, vă rugăm să contactați administratorul. Administratorii pot gestiona statusurile utilizatorilor din Panoul de Administrare.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Pentru a atașa baza de cunoștințe aici, ad<PERSON><PERSON>ți-o mai întâi în spațiul de lucru \"Knowledge\".", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Pentru a vă proteja confidențialitatea, doar e<PERSON>, ID-urile modelelor, etichetele și metadatele sunt partajate din feedback-ul dumneavoastră—jurnalele de chat rămân private și nu sunt incluse.", "To select actions here, add them to the \"Functions\" workspace first.": "Pentru a selecta acțiuni aici, adăugați-le mai întâi în spațiul de lucru \"Funcții\".", "To select filters here, add them to the \"Functions\" workspace first.": "Pentru a selecta filtrele aici, ad<PERSON><PERSON>ț<PERSON>-le mai întâi în spațiul de lucru \"Funcții\".", "To select toolkits here, add them to the \"Tools\" workspace first.": "Pentru a selecta kiturile de instrumente aici, adăugați-le mai întâi în spațiul de lucru \"Instrumente\".", "Toast notifications for new updates": "Notificări toast pentru actualizări noi", "Today": "<PERSON><PERSON><PERSON><PERSON>", "Toggle search": "", "Toggle settings": "<PERSON><PERSON><PERSON><PERSON>", "Toggle sidebar": "Comută bara laterală", "Toggle whether current connection is active.": "", "Token": "Token", "Too verbose": "<PERSON>a de<PERSON><PERSON><PERSON>", "Tool created successfully": "Instrumentul a fost creat cu succes", "Tool deleted successfully": "Instrumentul a fost șters cu succes", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "Instrumentul a fost importat cu succes", "Tool Name": "", "Tool Servers": "", "Tool updated successfully": "Instrumentul a fost actualizat cu succes", "Tools": "Instrumente", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "Instrumentele sunt un sistem de apelare a funcțiilor cu executare arbitrară a codului", "Tools Function Calling Prompt": "", "Tools have a function calling system that allows arbitrary code execution.": "Instrumentele au un sistem de apelare a funcțiilor care permite executarea arbitrară a codului.", "Tools Public Sharing": "", "Top K": "Top K", "Top K Reranker": "", "Transformers": "", "Trouble accessing Ollama?": "Probleme la accesarea Ollama?", "Trust Proxy Environment": "", "TTS Model": "Model TTS", "TTS Settings": "Setări TTS", "TTS Voice": "Voce TTS", "Type": "Tip", "Type Hugging Face Resolve (Download) URL": "Introduceți URL-ul de Rezolvare (Descărcare) Hugging Face", "Uh-oh! There was an issue with the response.": "Oh, nu! A apărut o problemă cu răspunsul.", "UI": "Interfață Utilizator", "Unarchive All": "Dezarhivează tot", "Unarchive All Archived Chats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> toate convers<PERSON><PERSON><PERSON><PERSON>", "Unarchive Chat": "Dezarhivează conversația", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "", "Unpin": "Anulează Fixarea", "Unravel secrets": "Dezvăluie secretele", "Untagged": "Netichetat", "Untitled": "", "Update": "Actualizează", "Update and Copy Link": "Actualizează și Copiază Link-ul", "Update for the latest features and improvements.": "Actualizare pentru cele mai recente caracteristici și îmbunătățiri.", "Update password": "Actualizează parola", "Updated": "Actualizat", "Updated at": "Actualizat la", "Updated At": "Actualizat la", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "", "Upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Upload a GGUF model": "Încarcă un model GGUF", "Upload Audio": "", "Upload directory": "Încărcare director", "Upload files": "Încărcați fișiere", "Upload Files": "Înc<PERSON><PERSON><PERSON>", "Upload Pipeline": "Încarcă Conducta", "Upload Progress": "Progres Î<PERSON>ă<PERSON>", "URL": "", "URL Mode": "Mod URL", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "Folosește '#' în prompt pentru a încărca și include cunoștin<PERSON><PERSON> tale.", "Use Gravatar": "Foloseș<PERSON> Gravatar", "Use groups to group your users and assign permissions.": "", "Use Initials": "Folosește Inițialele", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "utilizator", "User": "Utilizator", "User location successfully retrieved.": "Localizarea utilizatorului a fost preluată cu succes.", "User Webhooks": "", "Username": "", "Users": "Util<PERSON><PERSON><PERSON>", "Using the default arena model with all models. Click the plus button to add custom models.": "Folosind modelul implicit de arenă cu toate modelele. Faceți clic pe butonul plus pentru a adăuga modele personalizate.", "Utilize": "Utilizează", "Valid time units:": "Unități de timp valide:", "Valves": "Valve", "Valves updated": "Valve actualizate", "Valves updated successfully": "Valve actualizate cu succes", "variable": "variabilă", "variable to have them replaced with clipboard content.": "variabilă pentru a fi înlocuite cu conținutul clipboard-ului.", "Verify Connection": "", "Verify SSL Certificate": "", "Version": "Versiune", "Version {{selectedVersion}} of {{totalVersions}}": "Versiunea {{selectedVersion}} din {{totalVersions}}", "View Replies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "View Result from **{{NAME}}**": "", "Visibility": "Vizibilitate", "Vision": "", "Voice": "Voce", "Voice Input": "Intrare vocală", "Voice mode": "", "Warning": "Avertisment", "Warning:": "Avertisment:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Avertisment: Dacă actualizați sau schimbați modelul de încapsulare, va trebui să reimportați toate documentele.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "", "Web": "Web", "Web API": "API Web", "Web Loader Engine": "", "Web Search": "Căutare Web", "Web Search Engine": "Motor de Căutare Web", "Web Search in Chat": "", "Web Search Query Generation": "", "Webhook URL": "URL Webhook", "WebUI Settings": "Setări WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Weight of BM25 Retrieval": "", "What are you trying to achieve?": "", "What are you working on?": "", "What's New in": "Ce e Nou în", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "<PERSON><PERSON><PERSON> (Local)", "Why?": "", "Widescreen Mode": "<PERSON><PERSON>", "Won": "<PERSON><PERSON><PERSON><PERSON>", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "Spațiu de Lucru", "Workspace Permissions": "", "Write": "<PERSON><PERSON>", "Write a prompt suggestion (e.g. Who are you?)": "Scrieți o sugestie de prompt (de ex. Cine ești?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Scrieți un rezumat în 50 de cuvinte care rezumă [subiect sau cuvânt cheie].", "Write something...": "<PERSON><PERSON> ceva...", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "<PERSON><PERSON>", "You": "Tu", "You are currently using a trial license. Please contact support to upgrade your license.": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Puteți discuta cu un număr maxim de {{maxCount}} fișier(e) simultan.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Puteți personaliza interacțiunile dvs. cu LLM-urile adăugând amintiri prin butonul 'Gestionează' de mai jos, făcându-le mai utile și adaptate la dvs.", "You cannot upload an empty file.": "Nu poți încărca un fișier gol.", "You do not have permission to upload files.": "", "You have no archived conversations.": "Nu aveți conversații arhivate.", "You have shared this chat": "Ați partajat această conversație", "You're a helpful assistant.": "Ești un asistent util.", "You're now logged in.": "Acum ești autentificat.", "Your account status is currently pending activation.": "Statusul contului dvs. este în așteptare pentru activare.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Întreaga dvs. contribuție va merge direct la dezvoltatorul plugin-ului; Open WebUI nu ia niciun procent. Cu toate acestea, platforma de finanțare aleasă ar putea avea propriile taxe.", "Youtube": "Youtube", "Youtube Language": "", "Youtube Proxy URL": ""}