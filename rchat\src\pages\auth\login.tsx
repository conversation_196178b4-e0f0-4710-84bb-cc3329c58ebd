import { Eye, EyeOff } from 'lucide-react';
import { useRouter } from 'next/router';
import React, { useState, useEffect } from 'react';

import Layout from '@/components/layout/Layout';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/input';
import { getDeploymentUrl } from '@/config/chatConfig';
import { useUserContext } from '@/context/UserContext';

const LoginPage: React.FC = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [serverHealth, setServerHealth] = useState(true);
  const [loginSuccess, setLoginSuccess] = useState(false);
  const { login, authState } = useUserContext();
  const router = useRouter();

  // Get deployment URL from configuration
  const deploymentUrl = getDeploymentUrl();

  // Load default credentials from runtime config if available
  useEffect(() => {
    if (typeof window !== 'undefined' && window.__RUNTIME_CONFIG__) {
      if (
        window.__RUNTIME_CONFIG__.NEXT_PUBLIC_R2R_DEFAULT_EMAIL &&
        !window.__RUNTIME_CONFIG__.NEXT_PUBLIC_R2R_DEFAULT_EMAIL.includes(
          '__NEXT_PUBLIC_R2R_DEFAULT_EMAIL__'
        )
      ) {
        setEmail(window.__RUNTIME_CONFIG__.NEXT_PUBLIC_R2R_DEFAULT_EMAIL);
      }

      if (
        window.__RUNTIME_CONFIG__.NEXT_PUBLIC_R2R_DEFAULT_PASSWORD &&
        !window.__RUNTIME_CONFIG__.NEXT_PUBLIC_R2R_DEFAULT_PASSWORD.includes(
          '__NEXT_PUBLIC_R2R_DEFAULT_PASSWORD__'
        )
      ) {
        setPassword(
          window.__RUNTIME_CONFIG__.NEXT_PUBLIC_R2R_DEFAULT_PASSWORD
        );
      }
    }
  }, []);

  // Check deployment health
  const checkDeploymentHealth = async (): Promise<boolean> => {
    try {
      const response = await fetch(`${deploymentUrl}/v3/health`);
      return response.ok;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  };

  // Handle login submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    try {
      const result = await login(email, password, deploymentUrl);
      if (result) {
        setLoginSuccess(true);
      }
    } catch (error) {
      console.error('Login failed:', error);

      // Only check server health after a failed login attempt
      const isServerHealthy = await checkDeploymentHealth();

      let errorMessage = 'An unknown error occurred';
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      // Provide appropriate error message based on server health
      const serverStatusMessage = isServerHealthy
        ? 'Server is running normally, please check your credentials and try again.'
        : 'Unable to communicate with server, please check the API address in the configuration file.';

      setError(`Login failed. ${serverStatusMessage}\n\nError: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle successful login redirect
  useEffect(() => {
    if (loginSuccess && authState.isAuthenticated) {
      router.push('/');
    }
  }, [loginSuccess, authState.isAuthenticated, router]);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="w-full h-screen max-h-[100dvh] text-white relative">
      <div className="w-full h-full absolute top-0 left-0 bg-white dark:bg-black"></div>

      <div className="w-full absolute top-0 left-0 right-0 h-8" />

      <div className="fixed m-10 z-50">
        <div className="flex space-x-2">
          <div className="self-center">
            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-600 to-purple-600"></div>
          </div>
        </div>
      </div>

      <div className="fixed bg-transparent min-h-screen w-full flex justify-center font-primary z-50 text-black dark:text-white">
        <div className="w-full sm:max-w-md px-10 min-h-screen flex flex-col text-center">
          <div className="my-auto pb-10 w-full dark:text-gray-100">
            <form
              className="flex flex-col justify-center"
              onSubmit={handleSubmit}
            >
              <div className="mb-1">
                <div className="text-2xl font-medium">
                  Sign in to RChat
                </div>
              </div>

              <div className="flex flex-col mt-4">
                <div className="mb-2">
                  <label htmlFor="email" className="text-sm font-medium text-left mb-1 block">
                    Email
                  </label>
                  <Input
                    id="email"
                    type="email"
                    className="my-0.5 w-full text-sm bg-transparent border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2"
                    autoComplete="email"
                    name="email"
                    placeholder="Enter Your Email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                  />
                </div>

                <div>
                  <label htmlFor="password" className="text-sm font-medium text-left mb-1 block">
                    Password
                  </label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      className="my-0.5 w-full text-sm bg-transparent border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 pr-10"
                      placeholder="Enter Your Password"
                      autoComplete="current-password"
                      name="current-password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                    />
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5" aria-hidden="true" />
                      ) : (
                        <Eye className="h-5 w-5" aria-hidden="true" />
                      )}
                    </button>
                  </div>
                </div>
              </div>

              <div className="mt-5">
                <button
                  className="bg-gray-700/5 hover:bg-gray-700/10 dark:bg-gray-100/5 dark:hover:bg-gray-100/10 dark:text-gray-300 dark:hover:text-white transition w-full rounded-full font-medium text-sm py-2.5"
                  type="submit"
                  disabled={isLoading}
                >
                  {isLoading ? 'Signing in...' : 'Sign in'}
                </button>
              </div>

              <div className="mt-3 text-center">
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Default R2R user: <EMAIL>
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Server: {deploymentUrl}
                </p>
              </div>
            </form>

            {error && (
              <div className="mt-6 p-4 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-md">
                <p className="text-red-600 dark:text-red-400 text-sm whitespace-pre-line">
                  {error}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
