"use strict";(()=>{var e={};e.id=660,e.ids=[660],e.modules={1070:(e,r,t)=>{t.r(r),t.d(r,{default:()=>a});var i=t(997),s=t(6859);function a(){return(0,i.jsxs)(s.Html,{lang:"en",children:[(0,i.jsxs)(s.Head,{children:[i.jsx("meta",{charSet:"utf-8"}),i.jsx("meta",{name:"description",content:"RChat - R2R-powered chat application"}),i.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),i.jsx("link",{rel:"icon",href:"/favicon.ico"})]}),(0,i.jsxs)("body",{children:[i.jsx(s.<PERSON>,{}),i.jsx(s.NextScript,{})]})]})}},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},6689:e=>{e.exports=require("react")},997:e=>{e.exports=require("react/jsx-runtime")},5315:e=>{e.exports=require("path")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[859],()=>t(1070));module.exports=i})();