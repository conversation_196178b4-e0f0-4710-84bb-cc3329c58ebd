import type { AppProps } from 'next/app';
import { ThemeProvider } from 'next-themes';
import { UserProvider } from '@/context/UserContext';
import '@/styles/globals.css';

export default function App({ Component, pageProps }: AppProps) {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <UserProvider>
        <Component {...pageProps} />
      </UserProvider>
    </ThemeProvider>
  );
}
