apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: open-webui-ingress
  namespace: open-webui
  #annotations:
    # Use appropriate annotations for your Ingress controller, e.g., for NGINX:
    # nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: open-webui.minikube.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: open-webui-service
            port:
              number: 8080
