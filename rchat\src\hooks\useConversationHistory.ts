import { useState, useEffect, useCallback } from "react";
import { useUserContext } from "@/context/UserContext";
import { Conversation, Message } from "@/types";
import { ChatService } from "@/lib/chatService";

interface UseConversationHistoryReturn {
  conversations: Conversation[];
  isLoading: boolean;
  error: string | null;
  fetchConversations: () => Promise<void>;
  createNewConversation: (name?: string) => Promise<string | null>;
  loadConversation: (conversationId: string) => Promise<Message[]>;
  deleteConversation: (conversationId: string) => Promise<void>;
  refreshConversations: () => Promise<void>;
}

export const useConversationHistory = (): UseConversationHistoryReturn => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { getClient, authState } = useUserContext();

  const fetchConversations = useCallback(async () => {
    if (!authState.isAuthenticated) {
      setConversations([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const client = await getClient();
      if (!client) {
        throw new Error("Failed to get authenticated client");
      }

      const chatService = new ChatService(client);
      const response = await chatService.listConversations(0, 100);

      // Convert R2R conversation format to our format
      // Note: R2R API returns Unix timestamps that need to be converted
      const formattedConversations: Conversation[] = response.map((conv: any) => {
        // Handle timestamp conversion - R2R returns Unix timestamps
        const createdAt = conv.created_at ? new Date(conv.created_at * 1000).toISOString() : new Date().toISOString();
        const updatedAt = conv.updated_at ? new Date(conv.updated_at * 1000).toISOString() : createdAt;

        return {
          id: conv.id,
          name: conv.name || `Conversation ${new Date(conv.created_at * 1000).toLocaleDateString()}`,
          messages: [], // Messages will be loaded separately when needed
          created_at: createdAt,
          updated_at: updatedAt,
          metadata: conv.metadata || {},
          messageCount: conv.message_count || 0,
          userId: conv.user_id,
          lastMessage: conv.last_message,
        };
      });

      // Sort by creation date (newest first)
      formattedConversations.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      setConversations(formattedConversations);
    } catch (err) {
      console.error("Error fetching conversations:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch conversations");
    } finally {
      setIsLoading(false);
    }
  }, [getClient, authState.isAuthenticated]);

  const createNewConversation = useCallback(async (name?: string): Promise<string | null> => {
    if (!authState.isAuthenticated) {
      setError("Please log in to create conversations");
      return null;
    }

    try {
      const client = await getClient();
      if (!client) {
        throw new Error("Failed to get authenticated client");
      }

      const chatService = new ChatService(client);
      const conversationId = await chatService.createConversation(name);

      // Refresh conversations list
      await fetchConversations();

      return conversationId;
    } catch (err) {
      console.error("Error creating conversation:", err);
      setError(err instanceof Error ? err.message : "Failed to create conversation");
      return null;
    }
  }, [getClient, authState.isAuthenticated, fetchConversations]);

  const loadConversation = useCallback(async (conversationId: string): Promise<Message[]> => {
    if (!authState.isAuthenticated) {
      setError("Please log in to load conversations");
      return [];
    }

    try {
      const client = await getClient();
      if (!client) {
        throw new Error("Failed to get authenticated client");
      }

      const chatService = new ChatService(client);
      const messages = await chatService.getConversationHistory(conversationId);

      return messages;
    } catch (err) {
      console.error("Error loading conversation:", err);
      setError(err instanceof Error ? err.message : "Failed to load conversation");
      return [];
    }
  }, [getClient, authState.isAuthenticated]);

  const deleteConversation = useCallback(async (conversationId: string): Promise<void> => {
    if (!authState.isAuthenticated) {
      setError("Please log in to delete conversations");
      return;
    }

    try {
      const client = await getClient();
      if (!client) {
        throw new Error("Failed to get authenticated client");
      }

      const chatService = new ChatService(client);
      await chatService.deleteConversation(conversationId);

      // Remove from local state
      setConversations(prev => prev.filter(conv => conv.id !== conversationId));
    } catch (err) {
      console.error("Error deleting conversation:", err);
      setError(err instanceof Error ? err.message : "Failed to delete conversation");
    }
  }, [getClient, authState.isAuthenticated]);

  const refreshConversations = useCallback(async (): Promise<void> => {
    await fetchConversations();
  }, [fetchConversations]);

  // Load conversations when user authenticates
  useEffect(() => {
    if (authState.isAuthenticated) {
      fetchConversations();
    } else {
      setConversations([]);
      setError(null);
    }
  }, [authState.isAuthenticated, fetchConversations]);

  return {
    conversations,
    isLoading,
    error,
    fetchConversations,
    createNewConversation,
    loadConversation,
    deleteConversation,
    refreshConversations,
  };
};

