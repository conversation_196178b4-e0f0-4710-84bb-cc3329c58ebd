(()=>{var e={};e.id=820,e.ids=[820,888,660],e.modules={1323:(e,t)=>{"use strict";Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},6968:(e,t,r)=>{"use strict";r.r(t),r.d(t,{config:()=>f,default:()=>u,getServerSideProps:()=>p,getStaticPaths:()=>d,getStaticProps:()=>c,reportWebVitals:()=>h,routeModule:()=>x,unstable_getServerProps:()=>y,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>g,unstable_getStaticProps:()=>m});var n=r(7093),o=r(5244),s=r(1323),a=r(1070),l=r(7724),i=r(6971);let u=(0,s.l)(i,"default"),c=(0,s.l)(i,"getStaticProps"),d=(0,s.l)(i,"getStaticPaths"),p=(0,s.l)(i,"getServerSideProps"),f=(0,s.l)(i,"config"),h=(0,s.l)(i,"reportWebVitals"),m=(0,s.l)(i,"unstable_getStaticProps"),g=(0,s.l)(i,"unstable_getStaticPaths"),v=(0,s.l)(i,"unstable_getStaticParams"),y=(0,s.l)(i,"unstable_getServerProps"),S=(0,s.l)(i,"unstable_getServerSideProps"),x=new n.PagesRouteModule({definition:{kind:o.x.PAGES,page:"/_error",pathname:"/_error",bundlePath:"",filename:""},components:{App:l.default,Document:a.default},userland:i})},6971:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let n=r(167),o=r(997),s=n._(r(6689)),a=n._(r(7828)),l={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function i(e){let{res:t,err:r}=e;return{statusCode:t&&t.statusCode?t.statusCode:r?r.statusCode:404}}let u={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{lineHeight:"48px"},h1:{display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top"},h2:{fontSize:14,fontWeight:400,lineHeight:"28px"},wrap:{display:"inline-block"}};class c extends s.default.Component{render(){let{statusCode:e,withDarkMode:t=!0}=this.props,r=this.props.title||l[e]||"An unexpected error has occurred";return(0,o.jsxs)("div",{style:u.error,children:[(0,o.jsx)(a.default,{children:(0,o.jsx)("title",{children:e?e+": "+r:"Application error: a client-side exception has occurred"})}),(0,o.jsxs)("div",{style:u.desc,children:[(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}"+(t?"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}":"")}}),e?(0,o.jsx)("h1",{className:"next-error-h1",style:u.h1,children:e}):null,(0,o.jsx)("div",{style:u.wrap,children:(0,o.jsxs)("h2",{style:u.h2,children:[this.props.title||e?r:(0,o.jsx)(o.Fragment,{children:"Application error: a client-side exception has occurred (see the browser console for more information)"}),"."]})})]})]})}}c.displayName="ErrorPage",c.getInitialProps=i,c.origGetInitialProps=i,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5495:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},7828:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return m},defaultHead:function(){return d}});let n=r(167),o=r(8760),s=r(997),a=o._(r(6689)),l=n._(r(7215)),i=r(8039),u=r(1988),c=r(5495);function d(e){void 0===e&&(e=!1);let t=[(0,s.jsx)("meta",{charSet:"utf-8"})];return e||t.push((0,s.jsx)("meta",{name:"viewport",content:"width=device-width"})),t}function p(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(1997);let f=["name","httpEquiv","charSet","itemProp"];function h(e,t){let{inAmpMode:r}=t;return e.reduce(p,[]).reverse().concat(d(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return o=>{let s=!0,a=!1;if(o.key&&"number"!=typeof o.key&&o.key.indexOf("$")>0){a=!0;let t=o.key.slice(o.key.indexOf("$")+1);e.has(t)?s=!1:e.add(t)}switch(o.type){case"title":case"base":t.has(o.type)?s=!1:t.add(o.type);break;case"meta":for(let e=0,t=f.length;e<t;e++){let t=f[e];if(o.props.hasOwnProperty(t)){if("charSet"===t)r.has(t)?s=!1:r.add(t);else{let e=o.props[t],r=n[t]||new Set;("name"!==t||!a)&&r.has(e)?s=!1:(r.add(e),n[t]=r)}}}}return s}}()).reverse().map((e,t)=>{let n=e.key||t;if(!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,a.default.cloneElement(e,t)}return a.default.cloneElement(e,{key:n})})}let m=function(e){let{children:t}=e,r=(0,a.useContext)(i.AmpStateContext),n=(0,a.useContext)(u.HeadManagerContext);return(0,s.jsx)(l.default,{reduceComponentsToState:h,headManager:n,inAmpMode:(0,c.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7215:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let n=r(6689),o=()=>{},s=()=>{};function a(e){var t;let{headManager:r,reduceComponentsToState:a}=e;function l(){if(r&&r.mountedInstances){let t=n.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(a(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),l(),o(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),o(()=>(r&&(r._pendingUpdate=l),()=>{r&&(r._pendingUpdate=l)})),s(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},1997:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},6361:(e,t,r)=>{"use strict";r.d(t,{YQ:()=>o,pe:()=>s});let n={server:{apiUrl:"http://localhost:7272",port:7272,useHttps:!1,apiVersion:"v3",timeout:3e4},app:{appName:"RChat",appDescription:"R2R-powered chat application",version:"1.0.0",defaultMode:"rag_agent"},vectorSearch:{enabled:!0,searchLimit:10,searchFilters:"{}",indexMeasure:"cosine_distance",includeMetadatas:!1,probes:void 0,efSearch:void 0},hybridSearch:{enabled:!1,fullTextWeight:void 0,semanticWeight:void 0,fullTextLimit:void 0,rrfK:void 0},graphSearch:{enabled:!0,kgSearchLevel:null,maxCommunityDescriptionLength:100,localSearchLimits:{},maxLlmQueries:void 0},ragGeneration:{temperature:.1,topP:1,topK:100,maxTokensToSample:1024,kgTemperature:.1,kgTopP:1,kgTopK:100,kgMaxTokensToSample:1024}};function o(){return n}function s(e){return(e||n).server.apiUrl}},6259:(e,t,r)=>{"use strict";r.d(t,{d:()=>i,S:()=>u});var n=r(997),o=r(6689);let s=require("r2r-js");var a=r(6361);let l=(0,o.createContext)(void 0),i=({children:e})=>{let[t,r]=(0,o.useState)({isAuthenticated:!1,user:null,token:null}),[i,u]=(0,o.useState)(!0),[c,d]=(0,o.useState)(null);(0,o.useEffect)(()=>{(async()=>{try{let e=(0,a.pe)(),t=new s.r2rClient(e);d(t);let n=localStorage.getItem("chatAccessToken")||localStorage.getItem("r2r_token"),o=localStorage.getItem("chatRefreshToken"),l=localStorage.getItem("r2r_user");if(n&&l)try{let e=JSON.parse(l);o&&t.setTokens(n,o),r({isAuthenticated:!0,user:e,token:n})}catch(e){console.error("Error parsing stored user data:",e),localStorage.removeItem("r2r_token"),localStorage.removeItem("r2r_user"),localStorage.removeItem("chatAccessToken"),localStorage.removeItem("chatRefreshToken")}}catch(e){console.error("Error initializing auth:",e)}finally{u(!1)}})()},[]);let p=async(e,t,n)=>{try{let o=n||(0,a.pe)(),l=new s.r2rClient(o),i=await l.users.login({email:e,password:t});if(!i.results)throw Error("Login failed: No results returned");localStorage.setItem("chatAccessToken",i.results.accessToken.token),localStorage.setItem("chatRefreshToken",i.results.refreshToken.token),l.setTokens(i.results.accessToken.token,i.results.refreshToken.token),d(l);let u=await l.users.me();if(!u.results)throw Error("Failed to get user information");let c="user";try{await l.system.settings(),c="admin"}catch(e){e instanceof Error&&"status"in e&&403===e.status||console.error("Unexpected error when checking user role:",e)}let p={id:u.results.id,email:u.results.email||e,name:u.results.name,role:c};return localStorage.setItem("r2r_token",i.results.accessToken.token),localStorage.setItem("r2r_user",JSON.stringify(p)),r({isAuthenticated:!0,user:p,token:i.results.accessToken.token}),p}catch(e){if(console.error("Login error:",e),e instanceof Error){if(e.message.includes("401")||e.message.includes("Unauthorized"))throw Error("Invalid email or password. Please check your credentials.");if(e.message.includes("404")||e.message.includes("Not Found"))throw Error("User not found. Please check your email address.");if(e.message.includes("500")||e.message.includes("Internal Server Error"))throw Error("Server error. Please try again later.");if(e.message.includes("Network Error")||e.message.includes("fetch"))throw Error("Cannot connect to server. Please check if R2R backend is running.")}throw e}},f=async()=>{if(!t.isAuthenticated||!t.token)return null;if(!c){let e=(0,a.pe)(),t=new s.r2rClient(e),r=localStorage.getItem("chatAccessToken")||localStorage.getItem("r2r_token"),n=localStorage.getItem("chatRefreshToken");return r&&t.setTokens(r,n||""),d(t),t}return c};return n.jsx(l.Provider,{value:{authState:t,login:p,logout:()=>{localStorage.removeItem("r2r_token"),localStorage.removeItem("r2r_user"),localStorage.removeItem("chatAccessToken"),localStorage.removeItem("chatRefreshToken"),r({isAuthenticated:!1,user:null,token:null}),d(null)},getClient:f,isLoading:i},children:e})},u=()=>{let e=(0,o.useContext)(l);if(void 0===e)throw Error("useUserContext must be used within a UserProvider");return e}},7724:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(997);let o=require("next-themes");var s=r(6259);function a({Component:e,pageProps:t}){return n.jsx(o.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,children:n.jsx(s.d,{children:n.jsx(e,{...t})})})}r(108)},1070:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(997),o=r(6859);function s(){return(0,n.jsxs)(o.Html,{lang:"en",children:[(0,n.jsxs)(o.Head,{children:[n.jsx("meta",{charSet:"utf-8"}),n.jsx("meta",{name:"description",content:"RChat - R2R-powered chat application"}),n.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),n.jsx("link",{rel:"icon",href:"/favicon.ico"})]}),(0,n.jsxs)("body",{children:[n.jsx(o.Main,{}),n.jsx(o.NextScript,{})]})]})}},108:()=>{},5244:(e,t)=>{"use strict";var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},8039:(e,t,r)=>{"use strict";e.exports=r(7093).vendored.contexts.AmpContext},1988:(e,t,r)=>{"use strict";e.exports=r(7093).vendored.contexts.HeadManagerContext},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},6689:e=>{"use strict";e.exports=require("react")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},5315:e=>{"use strict";e.exports=require("path")},8760:(e,t)=>{"use strict";function r(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(r=function(e){return e?n:t})(e)}t._=t._interop_require_wildcard=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=r(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var l=s?Object.getOwnPropertyDescriptor(e,a):null;l&&(l.get||l.set)?Object.defineProperty(o,a,l):o[a]=e[a]}return o.default=e,n&&n.set(e,o),o}}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[859],()=>r(6968));module.exports=n})();