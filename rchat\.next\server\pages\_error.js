/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_error";
exports.ids = ["pages/_error"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./src/pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! private-next-pages/_error */ \"./node_modules/next/dist/pages/_error.js\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/config/chatConfig.ts":
/*!**********************************!*\
  !*** ./src/config/chatConfig.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultChatConfig: () => (/* binding */ defaultChatConfig),\n/* harmony export */   getDeploymentUrl: () => (/* binding */ getDeploymentUrl),\n/* harmony export */   loadChatConfig: () => (/* binding */ loadChatConfig),\n/* harmony export */   loadPublicConfig: () => (/* binding */ loadPublicConfig),\n/* harmony export */   saveChatConfig: () => (/* binding */ saveChatConfig)\n/* harmony export */ });\n/**\n * Chat Configuration for RChat Frontend\n *\n * This file contains all configuration options for the chat application,\n * including search settings, server connection, and UI preferences.\n */ /**\n * Default configuration values\n */ const defaultChatConfig = {\n    server: {\n        apiUrl: \"http://localhost:7272\",\n        port: 7272,\n        useHttps: false,\n        apiVersion: \"v3\",\n        timeout: 30000\n    },\n    app: {\n        appName: \"RChat\",\n        appDescription: \"R2R-powered chat application\",\n        version: \"1.0.0\",\n        defaultMode: \"rag_agent\"\n    },\n    vectorSearch: {\n        enabled: true,\n        searchLimit: 10,\n        searchFilters: \"{}\",\n        indexMeasure: \"cosine_distance\",\n        includeMetadatas: false,\n        probes: undefined,\n        efSearch: undefined\n    },\n    hybridSearch: {\n        enabled: false,\n        fullTextWeight: undefined,\n        semanticWeight: undefined,\n        fullTextLimit: undefined,\n        rrfK: undefined\n    },\n    graphSearch: {\n        enabled: true,\n        kgSearchLevel: null,\n        maxCommunityDescriptionLength: 100,\n        localSearchLimits: {},\n        maxLlmQueries: undefined\n    },\n    ragGeneration: {\n        temperature: 0.1,\n        topP: 1,\n        topK: 100,\n        maxTokensToSample: 1024,\n        kgTemperature: 0.1,\n        kgTopP: 1,\n        kgTopK: 100,\n        kgMaxTokensToSample: 1024\n    }\n};\n/**\n * Load configuration from localStorage with fallback to defaults\n */ function loadChatConfig() {\n    if (true) {\n        return defaultChatConfig;\n    }\n    try {\n        const stored = localStorage.getItem(\"rchatConfig\");\n        if (stored) {\n            const parsed = JSON.parse(stored);\n            // Merge with defaults to ensure all properties exist\n            return {\n                ...defaultChatConfig,\n                ...parsed,\n                server: {\n                    ...defaultChatConfig.server,\n                    ...parsed.server\n                },\n                app: {\n                    ...defaultChatConfig.app,\n                    ...parsed.app\n                },\n                vectorSearch: {\n                    ...defaultChatConfig.vectorSearch,\n                    ...parsed.vectorSearch\n                },\n                hybridSearch: {\n                    ...defaultChatConfig.hybridSearch,\n                    ...parsed.hybridSearch\n                },\n                graphSearch: {\n                    ...defaultChatConfig.graphSearch,\n                    ...parsed.graphSearch\n                },\n                ragGeneration: {\n                    ...defaultChatConfig.ragGeneration,\n                    ...parsed.ragGeneration\n                }\n            };\n        }\n    } catch (error) {\n        console.warn(\"Failed to load chat config from localStorage:\", error);\n    }\n    return defaultChatConfig;\n}\n/**\n * Save configuration to localStorage\n */ function saveChatConfig(config) {\n    if (true) {\n        return;\n    }\n    try {\n        localStorage.setItem(\"rchatConfig\", JSON.stringify(config));\n    } catch (error) {\n        console.error(\"Failed to save chat config to localStorage:\", error);\n    }\n}\n/**\n * Load configuration from public config.json file\n */ async function loadPublicConfig() {\n    if (true) {\n        return null;\n    }\n    try {\n        const response = await fetch(\"/config.json\");\n        if (!response.ok) {\n            console.warn(\"Could not load public config.json\");\n            return null;\n        }\n        const config = await response.json();\n        return {\n            server: {\n                apiUrl: config.apiUrl,\n                useHttps: config.useHttps,\n                timeout: config.timeout\n            },\n            app: {\n                appName: config.appName,\n                appDescription: config.appDescription,\n                version: config.version,\n                defaultMode: \"rag_agent\"\n            }\n        };\n    } catch (error) {\n        console.warn(\"Error loading public config.json:\", error);\n        return null;\n    }\n}\n/**\n * Get the deployment URL for the API\n */ function getDeploymentUrl(config) {\n    const chatConfig = config || loadChatConfig();\n    // Check for runtime config first (for Docker deployments)\n    if (false) {}\n    return chatConfig.server.apiUrl;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/config/chatConfig.ts\n");

/***/ }),

/***/ "./src/context/UserContext.tsx":
/*!*************************************!*\
  !*** ./src/context/UserContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: () => (/* binding */ UserProvider),\n/* harmony export */   useUserContext: () => (/* binding */ useUserContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var r2r_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! r2r-js */ \"r2r-js\");\n/* harmony import */ var r2r_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(r2r_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _config_chatConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../config/chatConfig */ \"./src/config/chatConfig.ts\");\n\n\n\n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst UserProvider = ({ children })=>{\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isAuthenticated: false,\n        user: null,\n        token: null\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [client, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Initialize client and check for existing session\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            try {\n                const deploymentUrl = (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_3__.getDeploymentUrl)();\n                const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(deploymentUrl);\n                setClient(newClient);\n                // Check for stored token (try both rchat and chatfrontend token keys)\n                const storedToken = localStorage.getItem(\"chatAccessToken\") || localStorage.getItem(\"r2r_token\");\n                const storedRefreshToken = localStorage.getItem(\"chatRefreshToken\");\n                const storedUser = localStorage.getItem(\"r2r_user\");\n                if (storedToken && storedUser) {\n                    try {\n                        const user = JSON.parse(storedUser);\n                        // Set up client with stored tokens\n                        if (storedRefreshToken) {\n                            newClient.setTokens(storedToken, storedRefreshToken);\n                        }\n                        setAuthState({\n                            isAuthenticated: true,\n                            user,\n                            token: storedToken\n                        });\n                    } catch (error) {\n                        console.error(\"Error parsing stored user data:\", error);\n                        localStorage.removeItem(\"r2r_token\");\n                        localStorage.removeItem(\"r2r_user\");\n                        localStorage.removeItem(\"chatAccessToken\");\n                        localStorage.removeItem(\"chatRefreshToken\");\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeAuth();\n    }, []);\n    const login = async (email, password, deploymentUrl)=>{\n        try {\n            const apiUrl = deploymentUrl || (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_3__.getDeploymentUrl)();\n            const loginClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(apiUrl);\n            const tokens = await loginClient.users.login({\n                email: email,\n                password: password\n            });\n            if (!tokens.results) {\n                throw new Error(\"Login failed: No results returned\");\n            }\n            // Store tokens like chatfrontend does\n            localStorage.setItem(\"chatAccessToken\", tokens.results.accessToken.token);\n            localStorage.setItem(\"chatRefreshToken\", tokens.results.refreshToken.token);\n            // Set tokens on client\n            loginClient.setTokens(tokens.results.accessToken.token, tokens.results.refreshToken.token);\n            setClient(loginClient);\n            // Get user info\n            const userInfo = await loginClient.users.me();\n            if (!userInfo.results) {\n                throw new Error(\"Failed to get user information\");\n            }\n            // Check user role like chatfrontend does\n            let userRole = \"user\";\n            try {\n                await loginClient.system.settings();\n                userRole = \"admin\";\n            } catch (error) {\n                if (error instanceof Error && \"status\" in error && error.status === 403) {\n                // User doesn't have admin access, keep as \"user\"\n                } else {\n                    console.error(\"Unexpected error when checking user role:\", error);\n                }\n            }\n            const user = {\n                id: userInfo.results.id,\n                email: userInfo.results.email || email,\n                name: userInfo.results.name,\n                role: userRole\n            };\n            // Store auth data\n            localStorage.setItem(\"r2r_token\", tokens.results.accessToken.token);\n            localStorage.setItem(\"r2r_user\", JSON.stringify(user));\n            // Update state\n            setAuthState({\n                isAuthenticated: true,\n                user,\n                token: tokens.results.accessToken.token\n            });\n            return user;\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            // Provide more detailed error information\n            if (error instanceof Error) {\n                if (error.message.includes(\"401\") || error.message.includes(\"Unauthorized\")) {\n                    throw new Error(\"Invalid email or password. Please check your credentials.\");\n                } else if (error.message.includes(\"404\") || error.message.includes(\"Not Found\")) {\n                    throw new Error(\"User not found. Please check your email address.\");\n                } else if (error.message.includes(\"500\") || error.message.includes(\"Internal Server Error\")) {\n                    throw new Error(\"Server error. Please try again later.\");\n                } else if (error.message.includes(\"Network Error\") || error.message.includes(\"fetch\")) {\n                    throw new Error(\"Cannot connect to server. Please check if R2R backend is running.\");\n                }\n            }\n            throw error;\n        }\n    };\n    const logout = ()=>{\n        localStorage.removeItem(\"r2r_token\");\n        localStorage.removeItem(\"r2r_user\");\n        localStorage.removeItem(\"chatAccessToken\");\n        localStorage.removeItem(\"chatRefreshToken\");\n        setAuthState({\n            isAuthenticated: false,\n            user: null,\n            token: null\n        });\n        setClient(null);\n    };\n    const getClient = async ()=>{\n        if (!authState.isAuthenticated || !authState.token) {\n            return null;\n        }\n        if (!client) {\n            const deploymentUrl = (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_3__.getDeploymentUrl)();\n            const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(deploymentUrl);\n            // Set tokens if available\n            const accessToken = localStorage.getItem(\"chatAccessToken\") || localStorage.getItem(\"r2r_token\");\n            const refreshToken = localStorage.getItem(\"chatRefreshToken\");\n            if (accessToken) {\n                newClient.setTokens(accessToken, refreshToken || \"\");\n            }\n            setClient(newClient);\n            return newClient;\n        }\n        return client;\n    };\n    const value = {\n        authState,\n        login,\n        logout,\n        getClient,\n        isLoading\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\context\\\\UserContext.tsx\",\n        lineNumber: 204,\n        columnNumber: 10\n    }, undefined);\n};\nconst useUserContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (context === undefined) {\n        throw new Error(\"useUserContext must be used within a UserProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/context/UserContext.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/UserContext */ \"./src/context/UserContext.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n        attribute: \"class\",\n        defaultTheme: \"system\",\n        enableSystem: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_UserContext__WEBPACK_IMPORTED_MODULE_2__.UserProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 10,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQzRDO0FBQ1M7QUFDdkI7QUFFZixTQUFTRSxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFZO0lBQzVELHFCQUNFLDhEQUFDSixzREFBYUE7UUFBQ0ssV0FBVTtRQUFRQyxjQUFhO1FBQVNDLFlBQVk7a0JBQ2pFLDRFQUFDTiw4REFBWUE7c0JBQ1gsNEVBQUNFO2dCQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJaEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yY2hhdC8uL3NyYy9wYWdlcy9fYXBwLnRzeD9mOWQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCc7XG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIH0gZnJvbSAnbmV4dC10aGVtZXMnO1xuaW1wb3J0IHsgVXNlclByb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0L1VzZXJDb250ZXh0JztcbmltcG9ydCAnQC9zdHlsZXMvZ2xvYmFscy5jc3MnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxUaGVtZVByb3ZpZGVyIGF0dHJpYnV0ZT1cImNsYXNzXCIgZGVmYXVsdFRoZW1lPVwic3lzdGVtXCIgZW5hYmxlU3lzdGVtPlxuICAgICAgPFVzZXJQcm92aWRlcj5cbiAgICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgICAgPC9Vc2VyUHJvdmlkZXI+XG4gICAgPC9UaGVtZVByb3ZpZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlRoZW1lUHJvdmlkZXIiLCJVc2VyUHJvdmlkZXIiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJhdHRyaWJ1dGUiLCJkZWZhdWx0VGhlbWUiLCJlbmFibGVTeXN0ZW0iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/_document.tsx":
/*!*********************************!*\
  !*** ./src/pages/_document.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 7,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"RChat - R2R-powered chat application\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2RvY3VtZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNkQ7QUFFOUMsU0FBU0k7SUFDdEIscUJBQ0UsOERBQUNKLCtDQUFJQTtRQUFDSyxNQUFLOzswQkFDVCw4REFBQ0osK0NBQUlBOztrQ0FDSCw4REFBQ0s7d0JBQUtDLFNBQVE7Ozs7OztrQ0FDZCw4REFBQ0Q7d0JBQUtFLE1BQUs7d0JBQWNDLFNBQVE7Ozs7OztrQ0FDakMsOERBQUNIO3dCQUFLRSxNQUFLO3dCQUFXQyxTQUFROzs7Ozs7a0NBQzlCLDhEQUFDQzt3QkFBS0MsS0FBSTt3QkFBT0MsTUFBSzs7Ozs7Ozs7Ozs7OzBCQUV4Qiw4REFBQ0M7O2tDQUNDLDhEQUFDWCwrQ0FBSUE7Ozs7O2tDQUNMLDhEQUFDQyxxREFBVUE7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSW5CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmNoYXQvLi9zcmMvcGFnZXMvX2RvY3VtZW50LnRzeD8xODhlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEh0bWwsIEhlYWQsIE1haW4sIE5leHRTY3JpcHQgfSBmcm9tICduZXh0L2RvY3VtZW50JztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRG9jdW1lbnQoKSB7XG4gIHJldHVybiAoXG4gICAgPEh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8SGVhZD5cbiAgICAgICAgPG1ldGEgY2hhclNldD1cInV0Zi04XCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImRlc2NyaXB0aW9uXCIgY29udGVudD1cIlJDaGF0IC0gUjJSLXBvd2VyZWQgY2hhdCBhcHBsaWNhdGlvblwiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJ2aWV3cG9ydFwiIGNvbnRlbnQ9XCJ3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MVwiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cImljb25cIiBocmVmPVwiL2Zhdmljb24uaWNvXCIgLz5cbiAgICAgIDwvSGVhZD5cbiAgICAgIDxib2R5PlxuICAgICAgICA8TWFpbiAvPlxuICAgICAgICA8TmV4dFNjcmlwdCAvPlxuICAgICAgPC9ib2R5PlxuICAgIDwvSHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJIdG1sIiwiSGVhZCIsIk1haW4iLCJOZXh0U2NyaXB0IiwiRG9jdW1lbnQiLCJsYW5nIiwibWV0YSIsImNoYXJTZXQiLCJuYW1lIiwiY29udGVudCIsImxpbmsiLCJyZWwiLCJocmVmIiwiYm9keSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/_document.tsx\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next-themes":
/*!******************************!*\
  !*** external "next-themes" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-themes");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "r2r-js":
/*!*************************!*\
  !*** external "r2r-js" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("r2r-js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();