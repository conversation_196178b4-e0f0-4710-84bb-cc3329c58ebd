import React, { useState, useEffect } from 'react';
import { useUserContext } from '@/context/UserContext';
import { Message } from '@/types';
import { generateUUID } from '@/lib/utils';
import { ChatService } from '@/lib/chatService';
import ChatHeader from './ChatHeader';
import MessageList from './MessageList';
import ChatInput from './ChatInput';
import ChatHistorySidebar from './ChatHistorySidebar';

interface ChatInterfaceProps {
  onSettingsClick?: () => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ onSettingsClick }) => {
  const { authState, getClient } = useUserContext();
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);

  // Load initial messages or conversation
  useEffect(() => {
    // For now, start with an empty conversation
    // Later this will load from conversation history
    setMessages([]);
  }, []);

  const handleSendMessage = async (content: string) => {
    if (!authState.isAuthenticated) {
      setError('Please log in to send messages');
      return;
    }

    const userMessage: Message = {
      id: generateUUID(),
      role: 'user',
      content,
      timestamp: new Date().toISOString(),
    };

    // Add user message immediately
    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);
    setError(null);

    try {
      const client = await getClient();
      if (!client) {
        throw new Error('Failed to get authenticated client');
      }

      const chatService = new ChatService(client);

      // Create conversation if none exists
      let conversationId = currentConversationId;
      if (!conversationId) {
        conversationId = await chatService.createConversation();
        setCurrentConversationId(conversationId);
      }

      // Send message to R2R agent
      const response = await chatService.sendMessage(
        content,
        conversationId,
        messages
      );

      const assistantMessage: Message = {
        id: generateUUID(),
        role: 'assistant',
        content: response.message,
        timestamp: new Date().toISOString(),
        metadata: {
          sources: response.sources,
          ...response.metadata,
        },
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error sending message:', error);
      setError(error instanceof Error ? error.message : 'Failed to send message');

      // Remove the user message if there was an error
      setMessages(prev => prev.filter(msg => msg.id !== userMessage.id));
    } finally {
      setIsLoading(false);
    }
  };

  const handleMessageEdit = (messageId: string, newContent: string) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId ? { ...msg, content: newContent } : msg
      )
    );
  };

  const handleMessageDelete = (messageId: string) => {
    setMessages(prev => prev.filter(msg => msg.id !== messageId));
  };

  const handleConversationSelect = async (conversationId: string) => {
    try {
      setIsLoading(true);
      const client = await getClient();
      if (!client) {
        throw new Error('Failed to get authenticated client');
      }

      const chatService = new ChatService(client);
      const history = await chatService.getConversationHistory(conversationId);

      setMessages(history);
      setCurrentConversationId(conversationId);
    } catch (error) {
      console.error('Error loading conversation:', error);
      setError('Failed to load conversation');
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewChat = () => {
    setMessages([]);
    setCurrentConversationId(null);
    setSidebarOpen(false);
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div className="h-screen flex flex-col bg-white dark:bg-gray-900">
      {/* Chat History Sidebar */}
      <ChatHistorySidebar
        isOpen={sidebarOpen}
        onToggle={toggleSidebar}
        onConversationSelect={handleConversationSelect}
        onNewChat={handleNewChat}
        selectedConversationId={currentConversationId}
      />

      {/* Fixed Header */}
      <ChatHeader onSettingsClick={onSettingsClick} />

      {/* Scrollable Messages Area */}
      <div className={`flex-1 pt-16 pb-20 overflow-hidden transition-all duration-300 ${
        sidebarOpen ? 'lg:ml-80' : ''
      }`}>
        <MessageList
          messages={messages}
          isLoading={isLoading}
          onMessageEdit={handleMessageEdit}
          onMessageDelete={handleMessageDelete}
        />
      </div>

      {/* Fixed Input Area */}
      <div className={`transition-all duration-300 ${
        sidebarOpen ? 'lg:ml-80' : ''
      }`}>
        <ChatInput
          onSendMessage={handleSendMessage}
          disabled={isLoading}
          placeholder="Type your message..."
        />
      </div>

      {/* Error display */}
      {error && (
        <div className="fixed top-20 left-1/2 transform -translate-x-1/2 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 text-red-600 dark:text-red-400 px-4 py-2 rounded-lg shadow-lg z-50">
          {error}
          <button
            onClick={() => setError(null)}
            className="ml-2 text-red-800 dark:text-red-200 hover:text-red-900 dark:hover:text-red-100"
          >
            ×
          </button>
        </div>
      )}
    </div>
  );
};

export default ChatInterface;
