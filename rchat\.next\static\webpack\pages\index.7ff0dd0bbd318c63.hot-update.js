"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/lib/chatService.ts":
/*!********************************!*\
  !*** ./src/lib/chatService.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatService: function() { return /* binding */ ChatService; }\n/* harmony export */ });\n/* harmony import */ var _config_chatConfig__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/chatConfig */ \"./src/config/chatConfig.ts\");\n\nclass ChatService {\n    /**\n   * Send a message to the R2R agent and get a response\n   */ async sendMessage(message, conversationId, messageHistory) {\n        try {\n            const config = (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_0__.loadChatConfig)();\n            // Prepare the search settings based on configuration\n            const chunkSearchSettings = {\n                indexMeasure: config.vectorSearch.indexMeasure,\n                enabled: config.vectorSearch.enabled,\n                ...config.vectorSearch.probes && {\n                    probes: config.vectorSearch.probes\n                },\n                ...config.vectorSearch.efSearch && {\n                    efSearch: config.vectorSearch.efSearch\n                }\n            };\n            const hybridSearchSettings = {\n                ...config.hybridSearch.fullTextWeight && {\n                    fulltextWeight: config.hybridSearch.fullTextWeight\n                },\n                ...config.hybridSearch.semanticWeight && {\n                    semanticWeight: config.hybridSearch.semanticWeight\n                },\n                ...config.hybridSearch.fullTextLimit && {\n                    fulltextLimit: config.hybridSearch.fullTextLimit\n                },\n                ...config.hybridSearch.rrfK && {\n                    rrfK: config.hybridSearch.rrfK\n                }\n            };\n            const graphSearchSettings = {\n                enabled: config.graphSearch.enabled,\n                ...config.graphSearch.maxCommunityDescriptionLength && {\n                    maxCommunityDescriptionLength: config.graphSearch.maxCommunityDescriptionLength\n                },\n                ...config.graphSearch.maxLlmQueries && {\n                    maxLlmQueriesForGlobalSearch: config.graphSearch.maxLlmQueries\n                },\n                ...config.graphSearch.localSearchLimits && {\n                    limits: config.graphSearch.localSearchLimits\n                }\n            };\n            const searchSettings = {\n                useSemanticSearch: config.vectorSearch.enabled,\n                useHybridSearch: config.hybridSearch.enabled,\n                useFulltextSearch: config.hybridSearch.enabled,\n                filters: config.vectorSearch.searchFilters ? JSON.parse(config.vectorSearch.searchFilters) : {},\n                limit: config.vectorSearch.searchLimit,\n                includeMetadata: config.vectorSearch.includeMetadatas,\n                chunkSettings: chunkSearchSettings,\n                hybridSettings: hybridSearchSettings,\n                graphSettings: graphSearchSettings\n            };\n            // Prepare generation settings\n            const ragGenerationConfig = {\n                stream: true,\n                temperature: config.ragGeneration.temperature,\n                topP: config.ragGeneration.topP,\n                maxTokensToSample: config.ragGeneration.maxTokensToSample\n            };\n            // Create the user message in R2R format\n            const userMessage = {\n                role: \"user\",\n                content: message\n            };\n            // Use agent mode based on configuration\n            if (config.app.defaultMode === \"rag_agent\") {\n                const streamResponse = await this.client.retrieval.agent({\n                    message: userMessage,\n                    ragGenerationConfig,\n                    searchSettings,\n                    conversationId\n                });\n                // Handle streaming response\n                return await this.processStreamResponse(streamResponse);\n            } else {\n                const streamResponse = await this.client.retrieval.rag({\n                    query: message,\n                    ragGenerationConfig,\n                    searchSettings\n                });\n                // Handle streaming response\n                return await this.processStreamResponse(streamResponse);\n            }\n        } catch (error) {\n            console.error(\"Error in chat service:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Process streaming response from R2R\n   */ async processStreamResponse(streamResponse) {\n        try {\n            console.log(\"Processing stream response:\", streamResponse);\n            console.log(\"Stream response type:\", typeof streamResponse);\n            console.log(\"Stream response has getReader:\", typeof streamResponse.getReader);\n            const reader = streamResponse.getReader();\n            const decoder = new TextDecoder();\n            let buffer = \"\";\n            let fullContent = \"\";\n            let sources = [];\n            let metadata = {};\n            // Process the stream\n            while(true){\n                const { done, value } = await reader.read();\n                if (done) {\n                    break;\n                }\n                buffer += decoder.decode(value, {\n                    stream: true\n                });\n                // Process complete SSE events from the buffer\n                const events = buffer.split(\"\\n\\n\");\n                buffer = events.pop() || \"\"; // Keep the last potentially incomplete event in the buffer\n                for (const event of events){\n                    if (!event.trim()) {\n                        continue;\n                    }\n                    const lines = event.split(\"\\n\");\n                    const eventType = lines[0].startsWith(\"event: \") ? lines[0].slice(7) : \"\";\n                    const dataLine = lines.find((line)=>line.startsWith(\"data: \"));\n                    if (!dataLine) {\n                        continue;\n                    }\n                    const jsonStr = dataLine.slice(6);\n                    try {\n                        const eventData = JSON.parse(jsonStr);\n                        if (eventType === \"search_results\") {\n                            // Handle search results\n                            if (eventData.search_results) {\n                                sources = eventData.search_results.chunk_search_results || [];\n                            }\n                        } else if (eventType === \"message\") {\n                            // Handle incremental content delta\n                            if (eventData.delta && eventData.delta.content) {\n                                const contentItems = eventData.delta.content;\n                                for (const item of contentItems){\n                                    if (item.type === \"text\" && item.payload && item.payload.value) {\n                                        fullContent += item.payload.value;\n                                    }\n                                }\n                            }\n                        }\n                    } catch (err) {\n                        console.error(\"Error parsing SSE event data:\", err, jsonStr);\n                    }\n                }\n            }\n            return {\n                message: fullContent || \"No response generated\",\n                sources,\n                metadata\n            };\n        } catch (error) {\n            console.error(\"Error processing stream response:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create a new conversation\n   */ async createConversation(name) {\n        try {\n            var _response_results;\n            const response = await this.client.conversations.create({\n                name: name || \"Conversation \".concat(new Date().toLocaleString())\n            });\n            if ((_response_results = response.results) === null || _response_results === void 0 ? void 0 : _response_results.id) {\n                return response.results.id;\n            } else {\n                throw new Error(\"Failed to create conversation\");\n            }\n        } catch (error) {\n            console.error(\"Error creating conversation:\", error);\n            // Return a mock ID for development\n            return \"conv-\".concat(Date.now());\n        }\n    }\n    /**\n   * Get conversation history\n   */ async getConversationHistory(conversationId) {\n        try {\n            const response = await this.client.conversations.retrieve({\n                id: conversationId\n            });\n            if (response.results) {\n                return response.results.map((msg)=>{\n                    var _msg_metadata, _msg_metadata1, _msg_metadata2;\n                    return {\n                        id: msg.id || \"msg-\".concat(Date.now(), \"-\").concat(Math.random()),\n                        role: ((_msg_metadata = msg.metadata) === null || _msg_metadata === void 0 ? void 0 : _msg_metadata.role) || \"user\",\n                        content: ((_msg_metadata1 = msg.metadata) === null || _msg_metadata1 === void 0 ? void 0 : _msg_metadata1.content) || msg.content || \"\",\n                        timestamp: ((_msg_metadata2 = msg.metadata) === null || _msg_metadata2 === void 0 ? void 0 : _msg_metadata2.timestamp) || new Date().toISOString(),\n                        metadata: msg.metadata\n                    };\n                });\n            }\n            return [];\n        } catch (error) {\n            console.error(\"Error getting conversation history:\", error);\n            return [];\n        }\n    }\n    /**\n   * List conversations\n   */ async listConversations() {\n        let offset = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 100;\n        try {\n            const response = await this.client.conversations.list({\n                offset,\n                limit\n            });\n            return response.results || [];\n        } catch (error) {\n            console.error(\"Error listing conversations:\", error);\n            return [];\n        }\n    }\n    /**\n   * Delete a conversation\n   */ async deleteConversation(conversationId) {\n        try {\n            await this.client.conversations.delete({\n                id: conversationId\n            });\n        } catch (error) {\n            console.error(\"Error deleting conversation:\", error);\n            // Don't throw error in development mode\n            console.warn(\"Conversation deletion failed, continuing...\");\n        }\n    }\n    constructor(client){\n        this.client = client;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/chatService.ts\n"));

/***/ })

});