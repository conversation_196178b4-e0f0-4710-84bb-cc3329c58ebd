{"-1 for no limit, or a positive integer for a specific limit": "-1 voor geen limiet, of een positief getal voor een specifiek limiet", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w', of '-1' for geen vervaldatum.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(bv. `sh webui.sh --api --api-auth gebruikersnaam_wachtwoord`)", "(e.g. `sh webui.sh --api`)": "(bv. `sh webui.sh --api`)", "(latest)": "(nieuwste)", "(leave blank for to use commercial endpoint)": "", "{{ models }}": "{{ modellen }}", "{{COUNT}} Available Tools": "", "{{COUNT}} hidden lines": "{{COUNT}} verb<PERSON><PERSON> regels", "{{COUNT}} Replies": "{{COUNT}} antwoorden", "{{user}}'s Chats": "{{user}}'s chats", "{{webUIName}} Backend Required": "{{webUIName}} Backend verplicht", "*Prompt node ID(s) are required for image generation": "*Prompt node ID('s) zijn vereist voor het genereren van afbeeldingen", "A new version (v{{LATEST_VERSION}}) is now available.": "<PERSON><PERSON> nieuwe versie (v{{LATEST_VERSION}}) is nu be<PERSON><PERSON><PERSON><PERSON>", "A task model is used when performing tasks such as generating titles for chats and web search queries": "<PERSON><PERSON> taakmodel wordt gebruikt bij het uitvoeren van taken zoals het genereren van titels voor chats en zoekopdrachten op het internet", "a user": "een g<PERSON><PERSON><PERSON>r", "About": "Over", "Accept autocomplete generation / Jump to prompt variable": "Accepteer het genereren van automatisch aanvullen / Spring naar promptvariabele", "Access": "<PERSON><PERSON><PERSON>", "Access Control": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Accessible to all users": "Toegankelijk voor alle gebruikers", "Account": "Account", "Account Activation Pending": "Accountactivatie in afwachting", "Accurate information": "Accurate informatie", "Actions": "Acties", "Activate": "<PERSON><PERSON>", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Activeer dit commando door \"/{{COMMAND}}\" in de chat te typen", "Active Users": "Actieve gebruikers", "Add": "Toevoegen", "Add a model ID": "Voeg een model-ID toe", "Add a short description about what this model does": "Voeg een korte beschrijving toe over wat dit model doet", "Add a tag": "Voeg een tag toe", "Add Arena Model": "Voeg arenamodel toe", "Add Connection": "Voeg verbinding toe", "Add Content": "Voeg content toe", "Add content here": "Voeg hier content toe", "Add Custom Parameter": "", "Add custom prompt": "Voeg een aangepaste prompt toe", "Add Files": "<PERSON><PERSON><PERSON> bestanden toe", "Add Group": "Voeg groep toe", "Add Memory": "<PERSON><PERSON><PERSON> geheugen toe", "Add Model": "Voeg model toe", "Add Reaction": "Voeg reactie toe", "Add Tag": "Voeg tag toe", "Add Tags": "Voeg tags toe", "Add text content": "<PERSON><PERSON><PERSON> te<PERSON><PERSON> toe", "Add User": "<PERSON><PERSON><PERSON> gebruiker toe", "Add User Group": "<PERSON><PERSON><PERSON> gebruike<PERSON><PERSON>ep toe", "Adjusting these settings will apply changes universally to all users.": "Het aanpassen van deze instellingen zal universeel worden toegepast op alle gebruikers.", "admin": "beheerder", "Admin": "<PERSON><PERSON><PERSON><PERSON>", "Admin Panel": "Be<PERSON><PERSON><PERSON>paneel", "Admin Settings": "Beheerdersinstellingen", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Beheerders hebben altijd toegang tot alle gereedschappen; gebruikers moeten gereedschap toegewezen krijgen per model in de werkruimte.", "Advanced Parameters": "Geavanceerde parameters", "Advanced Params": "Geavance<PERSON><PERSON> params", "All": "Alle", "All Documents": "Alle documenten", "All models deleted successfully": "Alle modellen zijn succesvol verwijderd", "Allow Call": "", "Allow Chat Controls": "Chatbesturing toes<PERSON><PERSON>", "Allow Chat Delete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Allow Chat Deletion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Allow Chat Edit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Allow Chat Export": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow File Upload": "Bestandenup<PERSON>", "Allow Multiple Models in Chat": "", "Allow non-local voices": "Niet-lokale stemmen <PERSON>", "Allow Speech to Text": "", "Allow Temporary Chat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Allow Text to Speech": "", "Allow User Location": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Allow Voice Interruption in Call": "Stemonderbreking tijdens gesp<PERSON>", "Allowed Endpoints": "Endpoints <PERSON><PERSON><PERSON>", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "Heb je al een account?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "Alternatief voor top_p, en streeft naar een evenwicht tussen kwaliteit en variatie. De parameter p vertegenwoordigt de minimumwaarschijnlijkheid dat een token in aanmerking wordt genomen, in verhouding tot de waarschijnlijkheid van het meest waarschijnlijke token. B<PERSON>j<PERSON>or<PERSON>ld, met p=0,05 en het meest waarschijnlijke token met een waarschijnlijkheid van 0,9, worden logits met een waarde kleiner dan 0,045 uitgefilterd.", "Always": "Altijd", "Always Collapse Code Blocks": "Codeblokken altijd inklappen", "Always Expand Details": "Details altijd uitklappen", "Always Play Notification Sound": "", "Amazing": "Geweldig", "an assistant": "een assistent", "Analyzed": "Geanalyseerd", "Analyzing...": "Aan het analysiseren...", "and": "en", "and {{COUNT}} more": "en {{COUNT}} meer", "and create a new shared link.": "en maak een nieuwe gedeelde link.", "Android": "", "API": "", "API Base URL": "API Base URL", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "API-sleutel", "API Key created.": "API-sleutel aangemaakt.", "API Key Endpoint Restrictions": "API-sleutel endpoint-beperkingen", "API keys": "API-sleutels", "API Version": "", "Application DN": "Applicatie DN", "Application DN Password": "Applicatie", "applies to all users with the \"user\" role": "wordt op alle gebruikers met de \"gebruikers<PERSON>\" toegepast", "April": "April", "Archive": "<PERSON><PERSON>", "Archive All Chats": "<PERSON>er alle chats", "Archived Chats": "Chatrecord", "archived-chat-export": "gearchiveerde-chat-export", "Are you sure you want to clear all memories? This action cannot be undone.": "Weet je zeker dat je alle herinneringen wil verwijderen? Deze actie kan niet ongedaan worden gemaakt.", "Are you sure you want to delete this channel?": "Weet je zeker dat je dit kanaal wil verwijderen?", "Are you sure you want to delete this message?": "Weet je zeker dat je dit bericht wil verwijderen?", "Are you sure you want to unarchive all archived chats?": "Weet je zeker dat je alle gearchiveerde chats wil onarchiveren?", "Are you sure?": "Weet je het zeker?", "Arena Models": "Arenamodellen", "Artifacts": "Artefacten", "Ask": "Vraag", "Ask a question": "Stel een vraag", "Assistant": "Assistent", "Attach file from knowledge": "<PERSON>oeg bestand uit kennis toe", "Attention to detail": "Attention to detail", "Attribute for Mail": "Attribuut voor mail", "Attribute for Username": "Attribuut voor gebruikersnaam", "Audio": "Audio", "August": "<PERSON>", "Auth": "", "Authenticate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Authentication": "Authenticatie", "Auto": "", "Auto-Copy Response to Clipboard": "Antwoord automatisch kopiëren naar klembord", "Auto-playback response": "Automatisch afspelen van antwoord", "Autocomplete Generation": "Automatische aanvullingsgeneratie", "Autocomplete Generation Input Max Length": "Maximale invoerlengte voor automatische aanvullingsgeneratie", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "Automatic1111 Api Auth String", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 Basis-URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 Basis-URL is verplicht", "Available list": "Beschikbare lijst", "Available Tools": "", "available!": "beschik<PERSON><PERSON>!", "Awful": "Verschrikkelijk", "Azure AI Speech": "Azure AI-spraak", "Azure Region": "Azure regio", "Back": "Terug", "Bad Response": "Ongeldig antwoord", "Banners": "Banners", "Base Model (From)": "Basismodel (Vanaf)", "before": "voor", "Being lazy": "<PERSON><PERSON>ijn", "Beta": "Beta", "Bing Search V7 Endpoint": "Bing Search V7 Endpoint", "Bing Search V7 Subscription Key": "Bing Search V7 Subscription Key", "Bocha Search API Key": "Bocha Search API-sleutel", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "Versterken of bestraffen van specifieke tokens voor beperkte reacties. Biaswaarden worden geklemd tussen -100 en 100 (inclusief). (Standaard: none)", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "Brave Search API-sleutel", "By {{name}}": "Op {{name}}", "Bypass Embedding and Retrieval": "Embedding en ophalen omzeilen ", "Bypass Web Loader": "", "Calendar": "Agenda", "Call": "Oproep", "Call feature is not supported when using Web STT engine": "Belfunctie wordt niet ondersteund bij gebruik van de Web STT engine", "Camera": "Camera", "Cancel": "<PERSON><PERSON><PERSON>", "Capabilities": "Mogelijkheden", "Capture": "<PERSON><PERSON>", "Capture Audio": "", "Certificate Path": "Certificaatpad", "Change Password": "Wijzig Wachtwoord", "Channel Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Channels": "<PERSON><PERSON><PERSON>", "Character": "<PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "Karakterlimiet voor automatische generatieinvoer", "Chart new frontiers": "Verken nieuwe grenzen", "Chat": "Cha<PERSON>", "Chat Background Image": "Chatachtergrond", "Chat Bubble UI": "Chatbubble-UI", "Chat Controls": "Chatbesturing", "Chat direction": "<PERSON><PERSON><PERSON><PERSON>", "Chat Overview": "Chatoverzicht", "Chat Permissions": "Chattoestemmingen", "Chat Tags Auto-Generation": "Chatlabels automatisch genereren", "Chats": "Chats", "Check Again": "Controleer Opnieuw", "Check for updates": "Controleer op updates", "Checking for updates...": "Controleren op updates...", "Choose a model before saving...": "<PERSON><PERSON> een model voordat je opslaat...", "Chunk Overlap": "<PERSON><PERSON><PERSON><PERSON>", "Chunk Size": "Chunkgrootte", "Ciphers": "Versleutelingen", "Citation": "Citaat", "Citations": "", "Clear memory": "Geheugen wissen", "Clear Memory": "Geheugen wissen", "click here": "klik hier", "Click here for filter guides.": "<PERSON><PERSON> hier voor filterhulp", "Click here for help.": "<PERSON><PERSON> hier voor hulp.", "Click here to": "<PERSON><PERSON> hier om", "Click here to download user import template file.": "<PERSON><PERSON> hier om het sjabloonbestand voor gebruikersimport te downloaden.", "Click here to learn more about faster-whisper and see the available models.": "<PERSON><PERSON> hier om meer te leren over faster-whisper en de beschikbare modellen te bekijken.", "Click here to see available models.": "<PERSON><PERSON> hier om beschikbare modellen te zien", "Click here to select": "<PERSON><PERSON> hier om te selecteren", "Click here to select a csv file.": "<PERSON><PERSON> hier om een csv file te selecteren.", "Click here to select a py file.": "<PERSON><PERSON> hier om een py-bestand te selecteren.", "Click here to upload a workflow.json file.": "<PERSON><PERSON> hier om een workflow.json-bestand te uploaden.", "click here.": "klik hier.", "Click on the user role button to change a user's role.": "Klik op de gebruikersrol knop om de rol van een gebruiker te wijzigen.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Klembord schrijftoestemming geweigerd. Kijk je browserinstellingen na om de benodigde toestemming te geven.", "Clone": "Kloon", "Clone Chat": "Kloon chat", "Clone of {{TITLE}}": "<PERSON><PERSON><PERSON> van {{TITLE}}", "Close": "Sluiten", "Close modal": "", "Close settings modal": "", "Code execution": "Code uitvoeren", "Code Execution": "Code-uitvoer", "Code Execution Engine": "Code-uitvoer engine", "Code Execution Timeout": "Code-uitvoer time-out", "Code formatted successfully": "Code succesvol geformateerd", "Code Interpreter": "Code-interpretatie", "Code Interpreter Engine": "Code-interpretatie engine", "Code Interpreter Prompt Template": "Code-interpretatie prompt<PERSON><PERSON><PERSON><PERSON><PERSON>", "Collapse": "Inklappen", "Collection": "<PERSON><PERSON><PERSON><PERSON>g", "Color": "<PERSON><PERSON><PERSON>", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI API-sleutel", "ComfyUI Base URL": "ComfyUI Base URL", "ComfyUI Base URL is required.": "ComfyUI Base URL is required.", "ComfyUI Workflow": "ComfyUI workflow", "ComfyUI Workflow Nodes": "ComfyUI workflowknopen", "Command": "Commando", "Completions": "Voltooiingen", "Concurrent Requests": "Gelijktijdige verzoeken", "Configure": "Configureer", "Confirm": "Bevestigen", "Confirm Password": "Bevestig wachtwoord", "Confirm your action": "Bevestig je actie", "Confirm your new password": "Bevestig je nieuwe wachtwoord", "Connect to your own OpenAI compatible API endpoints.": "<PERSON><PERSON><PERSON>d met je eigen OpenAI-compatibele API-endpoints", "Connect to your own OpenAPI compatible external tool servers.": "<PERSON><PERSON><PERSON><PERSON> met je eigen OpenAPI-compatibele externe gereedschapservers", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "Verbindingen", "Connections saved successfully": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "Beperkt de redeneerinspanning voor redeneermodellen. <PERSON><PERSON> van toepassing op redeneermodellen van specifieke providers die redeneerinspanning ondersteunen.", "Contact Admin for WebUI Access": "Neem contact op met de beheerder voor WebUI-toegang", "Content": "<PERSON><PERSON><PERSON>", "Content Extraction Engine": "Inhoudsextractie engine", "Continue Response": "<PERSON><PERSON><PERSON> met antwo<PERSON>", "Continue with {{provider}}": "Ga verder met {{provider}}", "Continue with Email": "Ga door met E-mail", "Continue with LDAP": "Ga door met LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Bepaal hoe berichttekst wordt opgesplitst voor TTS-verzoeken. 'Leestekens' splitst op in zinnen, 'al<PERSON>a's' splitst op in paragrafen en 'geen' houdt het bericht als een enkele string.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "Controleer de herhaling van tokenreeksen in de gegenereerde tekst. Een hogere waarde (bijv. 1,5) zal herhalingen sterker bestraffen, terwijl een lagere waarde (bijv. 1,1) milder zal zijn. Bij 1 is het uitgeschakeld.", "Controls": "Besturingselementen", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Copied": "Gekopieerd", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "URL van gedeelde gesprekspagina gekopieerd naar klembord!", "Copied to clipboard": "Gekopieerd naar klembord", "Copy": "<PERSON><PERSON><PERSON>", "Copy Formatted Text": "", "Copy last code block": "Kopieer laatste codeblok", "Copy last response": "<PERSON><PERSON><PERSON> la<PERSON>te antwoord", "Copy Link": "Kopieer link", "Copy to clipboard": "<PERSON><PERSON><PERSON> naar klembord", "Copying to clipboard was successful!": "Ko<PERSON><PERSON><PERSON> naar klembord was succesvol!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "CORS moet goed geconfigureerd zijn bij de provider om verzoeken van Open WebUI toe te staan", "Create": "Aanmaken", "Create a knowledge base": "Maak een kennis<PERSON>is aan", "Create a model": "Een model maken", "Create Account": "Maak account", "Create Admin Account": "Maak admin-account", "Create Channel": "<PERSON><PERSON> kanaal", "Create Group": "<PERSON><PERSON> groep", "Create Knowledge": "<PERSON><PERSON><PERSON><PERSON> kennis", "Create new key": "<PERSON><PERSON> ni<PERSON> sleutel", "Create new secret key": "<PERSON><PERSON> nieuwe geheime sleutel", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "Gemaakt op", "Created At": "Gemaakt op", "Created by": "Gemaakt door", "CSV Import": "CSV import", "Ctrl+Enter to Send": "Ctrl+Enter om te sturen", "Current Model": "Huidig model", "Current Password": "<PERSON><PERSON><PERSON> wa<PERSON>", "Custom": "Aangepast", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "Gevarenzone", "Dark": "<PERSON><PERSON>", "Database": "Database", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "December": "December", "Default": "Standaard", "Default (Open AI)": "<PERSON><PERSON><PERSON> (Open AI)", "Default (SentenceTransformers)": "Standaard (SentenceTransformers)", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "De standaard<PERSON><PERSON> werkt met een breder scala aan modellen door gereedschappen één keer aan te roepen voordat ze worden uitgevoerd. De native modus maakt gebruik van de ingebouwde mogelijkheden van het model om gereedschappen aan te roepen, maar vereist dat het model deze functie inherent ondersteunt.", "Default Model": "Standaardmodel", "Default model updated": "Standaardmodel bijgewerkt", "Default Models": "Standaardmodellen", "Default permissions": "Standaardrechten", "Default permissions updated successfully": "Standaardrechten succesvol bijgewerkt", "Default Prompt Suggestions": "Standaard Prompt Suggesties", "Default to 389 or 636 if TLS is enabled": "Standaard 389 of 636 als TLS is ingeschakeld", "Default to ALL": "Standaard op ALL", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "<PERSON>aard gesegmenteerd ophalen voor gerichte en relevante inhoudsextractie, dit wordt aanbevolen voor de meeste gevallen.", "Default User Role": "Standaard gebruikersrol", "Delete": "Verwijderen", "Delete a model": "Ver<PERSON><PERSON><PERSON> een model", "Delete All Chats": "<PERSON><PERSON><PERSON><PERSON><PERSON> alle chats", "Delete All Models": "Verwijder alle modellen", "Delete chat": "<PERSON><PERSON><PERSON><PERSON><PERSON> chat", "Delete Chat": "<PERSON><PERSON><PERSON><PERSON><PERSON> chat", "Delete chat?": "<PERSON><PERSON><PERSON><PERSON><PERSON> chat?", "Delete folder?": "Verwijder map?", "Delete function?": "Verwijder functie?", "Delete Message": "Verwijder bericht", "Delete message?": "Verwijder bericht", "Delete note?": "", "Delete prompt?": "V<PERSON><PERSON><PERSON><PERSON> prompt?", "delete this link": "ver<PERSON><PERSON><PERSON> deze link", "Delete tool?": "Verwijder tool?", "Delete User": "Verwijder gebruiker", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} is verwijderd", "Deleted {{name}}": "{{name}} verwi<PERSON><PERSON>d", "Deleted User": "Gebruiker verwijderd", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "<PERSON><PERSON><PERSON><PERSON><PERSON> je kennisbasis en doelstellingen", "Description": "Beschrijving", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "He<PERSON>t niet alle instructies gevolgt", "Direct": "Direct", "Direct Connections": "Directe verbindingen", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "Directe verbindingen stellen gebruikers in staat om met hun eigen OpenAI compatibele API-endpoints te verbinden.", "Direct Connections settings updated": "Directe verbindingsopties bijgewerkt", "Direct Tool Servers": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "Uitgeschakeld", "Discover a function": "Ontdek een functie", "Discover a model": "Ontdek een model", "Discover a prompt": "Ontdek een prompt", "Discover a tool": "Ontdek een tool", "Discover how to use Open WebUI and seek support from the community.": "Ontdek hoe je Open WebUI gebruikt en zoek ondersteuning van de community.", "Discover wonders": "<PERSON><PERSON><PERSON> wonderen", "Discover, download, and explore custom functions": "Ontdek, download en verken aangepaste functies", "Discover, download, and explore custom prompts": "Ontdek, download en verken aangepaste prompts", "Discover, download, and explore custom tools": "Ontdek, download en verken aangepaste gereedschappen", "Discover, download, and explore model presets": "Ontdek, download en verken model presets", "Dismissible": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Display": "<PERSON>n", "Display Emoji in Call": "<PERSON><PERSON>ji tonen tijdens gesprek", "Display the username instead of You in the Chat": "<PERSON><PERSON> <PERSON> geb<PERSON> in plaats van Jij in de Chat", "Displays citations in the response": "Toon citaten in het antwoord", "Dive into knowledge": "Duik in kennis", "Do not install functions from sources you do not fully trust.": "Installeer geen functies vanuit bronnen die je niet volledig vertrouwt", "Do not install tools from sources you do not fully trust.": "Installeer geen tools vanuit bronnen die je niet volledig vertrouwt.", "Docling": "<PERSON><PERSON>", "Docling Server URL required.": "Docling server-U<PERSON> benodigd", "Document": "Document", "Document Intelligence": "Document Intelligence", "Document Intelligence endpoint and key required.": "Document Intelligence-endpoint en -sleutel benodigd", "Documentation": "Documentatie", "Documents": "Documenten", "does not make any external connections, and your data stays securely on your locally hosted server.": "maakt geen externe verbindingen, en je gegevens blijven veilig op je lokaal gehoste server.", "Domain Filter List": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Don't have an account?": "Heb je geen account?", "don't install random functions from sources you don't trust.": "installeer geen willekeurige functies van bronnen die je niet vertrouwd", "don't install random tools from sources you don't trust.": "installeer geen willekeurige gereedschappen van bronnen die je niet vertrouwd", "Don't like the style": "Vind je de stijl niet mooi?", "Done": "Voltooid", "Download": "Download", "Download as SVG": "Download als SVG", "Download canceled": "Download geannuleerd", "Download Database": "Download database", "Drag and drop a file to upload or select a file to view": "Sleep een bestand om te uploaden of selecteer een bestand om te bekijken", "Draw": "Teken", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "bijv. '30s', '10m'. Geldige tijdseenheden zijn 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "bijv. \"json\" of een JSON-schema", "e.g. 60": "bijv. 60", "e.g. A filter to remove profanity from text": "bijv. Een filter om gevloek uit tekst te verwijderen", "e.g. en": "", "e.g. My Filter": "bijv. <PERSON> filter", "e.g. My Tools": "bijv. <PERSON><PERSON>", "e.g. my_filter": "bijv. mijn_filter", "e.g. my_tools": "bijv. mijn_gereedschappen", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "Gereedschappen om verschillende bewerkingen uit te voeren", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "e.g.) en,fr,de": "", "Edit": "<PERSON><PERSON><PERSON><PERSON>", "Edit Arena Model": "Bewerk arenamodel", "Edit Channel": "Bewerk kanaal", "Edit Connection": "Bewerk connectie", "Edit Default Permissions": "Bewerk standaardrechten", "Edit Memory": "Bewerk geheugen", "Edit User": "W<PERSON>jzig gebruiker", "Edit User Group": "Bewerk gebruikergroep", "Eject": "", "ElevenLabs": "ElevenLabs", "Email": "E-mail", "Embark on adventures": "Ga op avonturen", "Embedding": "Embedding", "Embedding Batch Size": "Embedding batchgrootte", "Embedding Model": "Embedding Model", "Embedding Model Engine": "Embedding Model Engine", "Embedding model set to \"{{embedding_model}}\"": "Embedding model ingesteld op \"{{embedding_model}}\"", "Enable API Key": "API-sleutel inschakelen", "Enable autocomplete generation for chat messages": "Automatische aanvullingsgeneratie voor chatberichten inschakelen", "Enable Code Execution": "Code-uitvoer inschakelen", "Enable Code Interpreter": "Code-interpretatie ins<PERSON><PERSON><PERSON>", "Enable Community Sharing": "Delen via de community inschakelen", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Schakel Memory Locking (mlock) in om te voorkomen dat modelgegevens uit het RAM worden verwisseld. Deze optie vergrendelt de werkset pagina's van het model in het RAM, zodat ze niet naar de schijf worden uitgewisseld. Dit kan helpen om de prestaties op peil te houden door paginafouten te voorkomen en snelle gegevenstoegang te garanderen.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Schakel Memory Mapping (mmap) in om modelgegevens te laden. Deze optie laat het systeem schijfopslag gebruiken als een uitbreiding van RAM door schijfbestanden te behandelen alsof ze in RAM zitten. Dit kan de prestaties van het model verbeteren door snellere gegevenstoegang mogelijk te maken. Het is echter mogelijk dat deze optie niet op alle systemen correct werkt en een aanzienlijke hoeveelheid schijfruimte in beslag kan nemen.", "Enable Message Rating": "<PERSON><PERSON><PERSON> be<PERSON> in", "Enable Mirostat sampling for controlling perplexity.": "Mirostat-sampling in om perplexiteit te controleren inschakelen.", "Enable New Sign Ups": "Schakel nieuwe registraties in", "Enabled": "Ingeschakeld", "Endpoint URL": "", "Enforce Temporary Chat": "Tijdelijke chat afdwingen", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Zorg ervoor dat uw CSV-bestand de volgende vier kolommen in deze volgorde bevat: Naam, E-mail, Wachtwoord, Rol.", "Enter {{role}} message here": "Voeg {{role}} be<PERSON>t hier toe", "Enter a detail about yourself for your LLMs to recall": "<PERSON><PERSON><PERSON> een detail over jez<PERSON> in zodat LLM's het kunnen onthouden", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "<PERSON><PERSON>r api auth string in (bv. gebruikersnaam:wachtwoord)", "Enter Application DN": "<PERSON><PERSON><PERSON> applicatie-DN in", "Enter Application DN Password": "<PERSON><PERSON>r applicatie-DN wachtwoord in", "Enter Bing Search V7 Endpoint": "<PERSON><PERSON>r Bing Search V7 Endpoint in", "Enter Bing Search V7 Subscription Key": "Voer Bing Search V7 abonnementscode in", "Enter BM25 Weight": "", "Enter Bocha Search API Key": "Voer Bocha Search API-sleutel in", "Enter Brave Search API Key": "Voer de Brave Search API-sleutel in", "Enter certificate path": "<PERSON><PERSON><PERSON> certific<PERSON> in", "Enter CFG Scale (e.g. 7.0)": "Voer CFG schaal in (bv. 7.0)", "Enter Chunk Overlap": "<PERSON><PERSON><PERSON> Overlap toe", "Enter Chunk Size": "Voeg Chunk Si<PERSON> toe", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "<PERSON><PERSON><PERSON> kommagescheiden \"token:bias_waarde\" paren in (bijv. 5432:100, 413:-100)", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter Datalab Marker API Key": "", "Enter description": "<PERSON><PERSON><PERSON> in", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "<PERSON><PERSON><PERSON>-URL in", "Enter Document Intelligence Endpoint": "Voer Document Intelligence endpoint in", "Enter Document Intelligence Key": "Voer Document Intelligence sleutel in", "Enter domains separated by commas (e.g., example.com,site.org)": "<PERSON><PERSON><PERSON> in gescheiden met komma's (bijv., voorbeeld.com,site.org)", "Enter Exa API Key": "Voer Exa API-sleutel in", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter Github Raw URL": "<PERSON><PERSON><PERSON>-URL in", "Enter Google PSE API Key": "Voer de Google PSE API-sleutel in", "Enter Google PSE Engine Id": "Voer Google PSE Engine-ID in", "Enter Image Size (e.g. 512x512)": "Voeg afbeelding formaat toe (Bijv. 512x512)", "Enter Jina API Key": "<PERSON><PERSON><PERSON> Jina API-sleutel in", "Enter Jupyter Password": "<PERSON><PERSON><PERSON>-wa<PERSON> in", "Enter Jupyter Token": "<PERSON><PERSON><PERSON>-token in", "Enter Jupyter URL": "<PERSON><PERSON><PERSON>-URL in", "Enter Kagi Search API Key": "Voer Kagi Search API-sleutel in", "Enter Key Behavior": "<PERSON><PERSON><PERSON> sle<PERSON> in", "Enter language codes": "<PERSON><PERSON><PERSON> taal<PERSON> toe", "Enter Mistral API Key": "", "Enter Model ID": "Voer model-ID in", "Enter model tag (e.g. {{modelTag}})": "Voeg model-tag toe (Bijv. {{modelTag}})", "Enter Mojeek Search API Key": "Voer Mojeek Search API-sleutel in", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "Voeg aantal stappen toe (Bijv. 50)", "Enter Perplexity API Key": "Voer Perplexity API-sleutel in", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "Voer proxy-URL in (bijv. *********************************:port)", "Enter reasoning effort": "<PERSON><PERSON><PERSON> redeneerinspanning in", "Enter Sampler (e.g. Euler a)": "<PERSON><PERSON><PERSON> in (bv. <PERSON><PERSON>r a)", "Enter Scheduler (e.g. Karras)": "<PERSON><PERSON><PERSON> Scheduler in (bv. <PERSON><PERSON><PERSON>)", "Enter Score": "Voeg score toe", "Enter SearchApi API Key": "Voer SearchApi API-sleutel in", "Enter SearchApi Engine": "Voer SearchApi-Engine in", "Enter Searxng Query URL": "<PERSON><PERSON><PERSON> de URL van de Searxng-query in", "Enter Seed": "<PERSON><PERSON><PERSON> Seed in", "Enter SerpApi API Key": "Voer SerpApi API-sleutel in", "Enter SerpApi Engine": "Voer SerpApi-engine in", "Enter Serper API Key": "<PERSON><PERSON><PERSON> de Serper API-sleutel in", "Enter Serply API Key": "Voer Serply API-sleutel in", "Enter Serpstack API Key": "<PERSON><PERSON><PERSON> de Serpstack API-sleutel in", "Enter server host": "<PERSON><PERSON><PERSON> serverhost in", "Enter server label": "<PERSON><PERSON><PERSON> serverlabel in", "Enter server port": "<PERSON><PERSON>r serverpoort in", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "<PERSON><PERSON><PERSON> stopse<PERSON>ie in", "Enter system prompt": "<PERSON><PERSON><PERSON> systeem prompt in", "Enter system prompt here": "", "Enter Tavily API Key": "<PERSON><PERSON><PERSON>-sleutel in", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "Voer de publieke URL van je WebUI in. Deze URL wordt gebruikt om links in de notificaties te maken.", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "<PERSON><PERSON><PERSON> Server URL in", "Enter timeout in seconds": "<PERSON><PERSON><PERSON> time-out in seconden in", "Enter to Send": "Enter om te sturen", "Enter Top K": "<PERSON>oeg Top K toe", "Enter Top K Reranker": "<PERSON><PERSON><PERSON>ranker in", "Enter URL (e.g. http://127.0.0.1:7860/)": "Voer URL in (Bijv. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Voer URL in (Bijv. http://localhost:11434)", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your current password": "<PERSON><PERSON>r je huidige wachtwoord in", "Enter Your Email": "<PERSON><PERSON><PERSON> je <PERSON> in", "Enter Your Full Name": "<PERSON><PERSON><PERSON> je Volledige <PERSON> in", "Enter your message": "<PERSON><PERSON><PERSON> je bericht in", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "<PERSON><PERSON>r je nieuwe wachtwoord in", "Enter Your Password": "<PERSON><PERSON><PERSON> je wachtwoord in", "Enter Your Role": "<PERSON><PERSON>r je rol in", "Enter Your Username": "<PERSON><PERSON><PERSON> je geb<PERSON> in", "Enter your webhook URL": "<PERSON><PERSON><PERSON> je webhook-URL in", "Error": "Fout", "ERROR": "ERROR", "Error accessing Google Drive: {{error}}": "Fout bij het ben<PERSON><PERSON> van <PERSON> Drive: {{error}}", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "Error bij het uploaden van bestand: {{error}}", "Evaluations": "Beoordelingen", "Exa API Key": "Exa API-sleutel", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Voorbeeld: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Voorbeeld: ALL", "Example: mail": "Voorbeeld: mail", "Example: ou=users,dc=foo,dc=example": "Voorbeeld: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Voorbeeld: sAMAccountName or uid or userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "Het aantal seats in uw licentie is overschreden. Neem contact op met support om het aantal seats te verhogen.", "Exclude": "Sluit uit", "Execute code for analysis": "Voer code uit voor analyse", "Executing **{{NAME}}**...": "", "Expand": "Uitbreiden", "Experimental": "Experimenteel", "Explain": "Leg uit", "Explore the cosmos": "Ontdek de kosmos", "Export": "Exporteren", "Export All Archived Chats": "Exporteer alle gearchiveerde chats", "Export All Chats (All Users)": "Exporteer alle chats (Alle gebruikers)", "Export chat (.json)": "Exporteer chat (.json)", "Export Chats": "Exporteer chats", "Export Config to JSON File": "Exporteer configuratie naar JSON-bestand", "Export Functions": "Exporteer functies", "Export Models": "Modellen exporteren", "Export Presets": "Exporteer voorinstellingen", "Export Prompt Suggestions": "", "Export Prompts": "Exporteer Prompts", "Export to CSV": "Exporteer naar CSV", "Export Tools": "Exporteer g<PERSON>", "External": "Extern", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Failed to add file.": "Het is niet gelukt om het bestand toe te voegen.", "Failed to connect to {{URL}} OpenAPI tool server": "Kan geen verbinding maken met {{URL}} OpenAPI gereedschapserver", "Failed to copy link": "", "Failed to create API Key.": "Kan API Key niet aanmaken.", "Failed to delete note": "", "Failed to fetch models": "Kan modellen niet ophalen", "Failed to load file content.": "", "Failed to read clipboard contents": "Kan klembord inhoud niet lezen", "Failed to save connections": "", "Failed to save models configuration": "Het is niet gelukt om de modelconfiguratie op te slaan", "Failed to update settings": "Instellingen konden niet worden bijgewerkt.", "Failed to upload file.": "Bestand kon niet worden geüpload.", "Features": "Functies", "Features Permissions": "Functietoestemmingen", "February": "<PERSON><PERSON><PERSON>", "Feedback Details": "", "Feedback History": "Feedback g<PERSON><PERSON><PERSON><PERSON>", "Feedbacks": "<PERSON><PERSON><PERSON>", "Feel free to add specific details": "Voeg specifieke details toe", "File": "Bestand", "File added successfully.": "Bestand succesvol toegevoegd.", "File content updated successfully.": "Bestandsinhoud succesvol bijgewerkt.", "File Mode": "Bestandsmodus", "File not found.": "<PERSON>and niet gevonden.", "File removed successfully.": "Bestand succesvol verwijderd.", "File size should not exceed {{maxSize}} MB.": "Bestandsgrootte mag niet groter zijn dan {{maxSize}} MB.", "File Upload": "", "File uploaded successfully": "Bestand succesvol upgeload", "Files": "<PERSON><PERSON><PERSON>", "Filter is now globally disabled": "Filter is nu globaal uitgeschakeld", "Filter is now globally enabled": "Filter is nu globaal ingeschakeld", "Filters": "Filters", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Vingerafdruk spoofing gedetecteerd: kan initialen niet gebruiken als avatar. Standaardprofielafbeelding wordt gebruikt.", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Fluidly stream large external response chunks": "Stream grote externe responsbrokken vloeiend", "Focus chat input": "Focus chat input", "Folder deleted successfully": "Map succesvol verwijderd", "Folder name cannot be empty.": "<PERSON><PERSON><PERSON> kan niet leeg zijn", "Folder name updated successfully": "Mapnaam succesvol aangepast", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "Volgde instructies perfect", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "<PERSON><PERSON><PERSON> nieuwe paden", "Form": "<PERSON><PERSON><PERSON>", "Format your variables using brackets like this:": "Formateer je variabelen met haken zoals dit:", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "Volledige contextmodus", "Function": "<PERSON><PERSON><PERSON>", "Function Calling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Function created successfully": "Functie succesvol aangemaakt", "Function deleted successfully": "Functie succesvol verwijderd", "Function Description": "Functiebeschrijving", "Function ID": "Functie-ID", "Function imported successfully": "", "Function is now globally disabled": "Functie is nu globaal uitgeschakeld", "Function is now globally enabled": "Functie is nu globaal ingeschakeld", "Function Name": "Functienaam", "Function updated successfully": "Functienaam succesvol aangepast", "Functions": "Functies", "Functions allow arbitrary code execution.": "Functies staan will<PERSON> code-uitvoering toe", "Functions imported successfully": "Functies succesvol geïmporteerd", "Gemini": "Gemini", "Gemini API Config": "Gemini API-configuratie", "Gemini API Key is required.": "Gemini API-sleutel is vereisd", "General": "<PERSON><PERSON><PERSON><PERSON>", "Generate": "", "Generate an image": "<PERSON><PERSON> een afbeelding", "Generate Image": "<PERSON><PERSON>", "Generate prompt pair": "Genereer promptpaar", "Generating search query": "Zoekopdracht genereren", "Generating...": "", "Get started": "<PERSON><PERSON>", "Get started with {{WEBUI_NAME}}": "Begin met {{WEBUI_NAME}}", "Global": "Globaal", "Good Response": "Goed antwoord", "Google Drive": "Google Drive", "Google PSE API Key": "Google PSE API-sleutel", "Google PSE Engine Id": "Google PSE-engine-ID", "Group created successfully": "G<PERSON>ep succesvol aangemaakt", "Group deleted successfully": "G<PERSON><PERSON> succesvol verwijderd", "Group Description": "Groepsbeschrijving", "Group Name": "Groepsnaam", "Group updated successfully": "Groep succesvol bijgewerkt", "Groups": "<PERSON><PERSON><PERSON><PERSON>", "Haptic Feedback": "Haptische feedback", "Hello, {{name}}": "<PERSON><PERSON>, {{name}}", "Help": "Help", "Help us create the best community leaderboard by sharing your feedback history!": "Help ons het beste community leaderboard te maken door je feedbackgeschiedenis te delen!", "Hex Color": "<PERSON><PERSON><PERSON>kleur", "Hex Color - Leave empty for default color": "Hex-kleur - laat leeg voor standaardkleur", "Hide": "Ver<PERSON>", "Hide from Sidebar": "", "Hide Model": "Verberg model", "High Contrast Mode": "", "Home": "<PERSON><PERSON><PERSON>", "Host": "Host", "How can I help you today?": "Hoe kan ik je vandaag helpen?", "How would you rate this response?": "Hoe zou je dit antwoord beoordelen?", "HTML": "", "Hybrid Search": "<PERSON><PERSON>", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Ik bevestig dat ik de implicaties van mijn actie heb gelezen en begrepen. Ik ben me bewust van de risico's die gepaard gaan met het uit<PERSON>eren van willekeurige code en ik heb de bet<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> van de bron gecontroleerd.", "ID": "ID", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "<PERSON><PERSON><PERSON> nieuwsgie<PERSON><PERSON><PERSON> aan", "Image": "Afbeelding", "Image Compression": "Afbeeldingscompressie", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "Afbeeldingsgeneratie", "Image Generation (Experimental)": "A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Experimenteel)", "Image Generation Engine": "Afbeeldingsgeneratie Engine", "Image Max Compression Size": "Maximale afbeeldingscompressiegrootte", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "Afbeeldingspromptgeneratie", "Image Prompt Generation Prompt": "A<PERSON><PERSON>ldingspromptgeneratie prompt", "Image Settings": "Afbeeldingsinstellingen", "Images": "Afbeeldingen", "Import": "", "Import Chats": "<PERSON><PERSON><PERSON><PERSON>", "Import Config from JSON File": "Importeer configuratie vanuit JSON-bestand", "Import From Link": "", "Import Functions": "Importeer Functies", "Import Models": "Modellen importeren", "Import Notes": "", "Import Presets": "Importeer voorinstellingen", "Import Prompt Suggestions": "", "Import Prompts": "Importeer Prompts", "Import Tools": "Importeer Gereedschappen", "Include": "Voeg toe", "Include `--api-auth` flag when running stable-diffusion-webui": "Voeg '--api-auth` toe bij het uitvoeren van stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Voeg `--api` vlag toe bij het uitvoeren van stable-diffusion-webui", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "Beïnv<PERSON><PERSON>t hoe snel het algoritme reageert op feedback van de gegenereerde tekst. Een lagere leersnelheid resulteert in langzamere aanpassingen, terwijl een hogere leersnelheid het algoritme responsiever maakt.", "Info": "Info", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "Injecteer de volledige inhoud als context voor uitgebreide verwerking, dit wordt aanbevolen voor complexe query's.", "Input commands": "Voer commando's in", "Install from Github URL": "Installeren vanaf Github-URL", "Instant Auto-Send After Voice Transcription": "Direct automatisch verzenden na spraaktranscriptie", "Integration": "Integratie", "Interface": "Interface", "Invalid file content": "", "Invalid file format.": "Ongeldig bestandsformaat", "Invalid JSON file": "", "Invalid Tag": "Ongeldige Tag", "is typing...": "is aan het schrijven...", "January": "<PERSON><PERSON><PERSON>", "Jina API Key": "Jina API-sleutel", "join our Discord for help.": "join onze Discord voor hulp.", "JSON": "JSON", "JSON Preview": "JSON-voorbeeld", "July": "<PERSON><PERSON>", "June": "<PERSON><PERSON>", "Jupyter Auth": "<PERSON><PERSON><PERSON>", "Jupyter URL": "Jupyter URL", "JWT Expiration": "JWT Expiration", "JWT Token": "JWT Token", "Kagi Search API Key": "Kagi Search API-sleutel", "Keep in Sidebar": "", "Key": "<PERSON><PERSON><PERSON><PERSON>", "Keyboard shortcuts": "Toetsenbord snelkoppelingen", "Knowledge": "<PERSON><PERSON>", "Knowledge Access": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Knowledge created successfully.": "<PERSON><PERSON> succesvol aangemaakt", "Knowledge deleted successfully.": "<PERSON><PERSON> succesvol verwijderd", "Knowledge Public Sharing": "Publieke kennisdeling", "Knowledge reset successfully.": "<PERSON><PERSON> succesvol gereset", "Knowledge updated successfully": "Kennis succesvol bijgewerkt", "Kokoro.js (Browser)": "Kokoro.js (Browser)", "Kokoro.js Dtype": "Kokoro.js Dtype", "Label": "Label", "Landing Page Mode": "Landingspaginamodus", "Language": "Taal", "Language Locales": "", "Languages": "", "Last Active": "Laatst Actief", "Last Modified": "Laatst aangepast", "Last reply": "Laatste antwoord", "LDAP": "LDAP", "LDAP server updated": "LDAP-server bijgewerkt", "Leaderboard": "Klassement", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "Laat leeg voor ongelimiteerd", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "Laat leeg om alle modellen van het \"{{url}}/api/tags\"-endpoint mee te nemen", "Leave empty to include all models from \"{{url}}/models\" endpoint": "Laat leeg om alle modellen van \"{{url}}/models\"-endpoint mee te nemen", "Leave empty to include all models or select specific models": "Laat leeg om alle modellen mee te nemen, of selecteer specifieke modellen", "Leave empty to use the default prompt, or enter a custom prompt": "Laat leeg om de standaard prompt te g<PERSON><PERSON><PERSON><PERSON>, of selecteer een aangep<PERSON>e prompt", "Leave model field empty to use the default model.": "Laat modelveld leeg om het standaardmodel te gebruiken.", "License": "Licentie", "Light": "Licht", "Listening...": "Aan het luisteren...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "LLMs kunnen fouten maken. Verifieer belangrijke informatie.", "Loader": "<PERSON>der", "Loading Kokoro.js...": "Kokoro.js aan het laden", "Local": "Lokaal", "Local Task Model": "", "Location access not allowed": "Locatietoegang niet <PERSON>", "Lost": "Verloren", "LTR": "LNR", "Made by Open WebUI Community": "Gemaakt door OpenWebUI Community", "Make password visible in the user interface": "", "Make sure to enclose them with": "Zorg ervoor dat je ze omringt met", "Make sure to export a workflow.json file as API format from ComfyUI.": "Zorg ervoor dat je een workflow.json-bestand als API-formaat exporteert vanuit ComfyUI.", "Manage": "<PERSON><PERSON><PERSON>", "Manage Direct Connections": "<PERSON><PERSON>r directe verbindingen", "Manage Models": "<PERSON><PERSON><PERSON> modellen", "Manage Ollama": "<PERSON><PERSON><PERSON>", "Manage Ollama API Connections": "<PERSON><PERSON><PERSON>-verbindingen", "Manage OpenAI API Connections": "Beheer OpenAI API-verbindingen", "Manage Pipelines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> beheren", "Manage Tool Servers": "<PERSON><PERSON><PERSON>", "March": "<PERSON><PERSON>", "Markdown": "", "Max Speakers": "", "Max Upload Count": "Maximale Uploadhoeveelheid", "Max Upload Size": "Maximale Uploadgrootte", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Maximaal 3 modellen kunnen tegelijkertijd worden gedownload. Probeer het later opnieuw.", "May": "<PERSON>", "Memories accessible by LLMs will be shown here.": "Geheugen toegankelijk voor LLMs wordt hier getoond.", "Memory": "Geheugen", "Memory added successfully": "Geheugen succesvol toegevoegd", "Memory cleared successfully": "Geheugen succesvol vrijgemaakt", "Memory deleted successfully": "Geheugen succesvol verwijderd", "Memory updated successfully": "Geheugen succesvol bijgewerkt", "Merge Responses": "<PERSON>oeg antwoorden samen", "Merged Response": "Samengevoegd antwoord", "Message rating should be enabled to use this feature": "Berichtbeoordeling moet ingeschakeld zijn om deze functie te gebruiken", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Berich<PERSON> die je verzendt nadat je jouw link hebt gemaakt, worden niet gedeeld. Gebruikers met de URL kunnen de gedeelde chat bekijken.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "Model", "Model '{{modelName}}' has been successfully downloaded.": "Model '{{modelName}}' is succesvol gedownload.", "Model '{{modelTag}}' is already in queue for downloading.": "Model '{{modelTag}}' staat al in de wachtrij voor downloaden.", "Model {{modelId}} not found": "Model {{modelId}} niet g<PERSON><PERSON>", "Model {{modelName}} is not vision capable": "Model {{modelName}} is niet geschikt voor visie", "Model {{name}} is now {{status}}": "Model {{name}} is nu {{status}}", "Model {{name}} is now hidden": "Model {{naam}} is nu verborgen", "Model {{name}} is now visible": "Model {{naam}} is nu zicht<PERSON>ar", "Model accepts file inputs": "", "Model accepts image inputs": "Model accepteerd afbeeldingsinvoer", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "Model succesvol gecreëerd", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Model filesystem path gedetecteerd. Model shortname is vereist voor update, kan niet doorgaan.", "Model Filtering": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Model ID": "Model-ID", "Model IDs": "Model-IDs", "Model Name": "<PERSON><PERSON><PERSON>", "Model not selected": "Model niet geselecteerd", "Model Params": "<PERSON><PERSON><PERSON>", "Model Permissions": "Modeltoestemmingen", "Model unloaded successfully": "", "Model updated successfully": "Model succesvol bijgewerkt", "Model(s) do not support file upload": "", "Modelfile Content": "Modelfile-inhoud", "Models": "<PERSON><PERSON>", "Models Access": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Models configuration saved successfully": "Modellenconfiguratie succesvol opgeslagen", "Models Public Sharing": "Modellen publiek delen", "Mojeek Search API Key": "Mojeek Search API-sleutel", "more": "<PERSON><PERSON>", "More": "<PERSON><PERSON>", "My Notes": "", "Name": "<PERSON><PERSON>", "Name your knowledge base": "<PERSON><PERSON> je kennisbasis een naam", "Native": "Native", "New Chat": "<PERSON><PERSON><PERSON>", "New Folder": "Nieuwe map", "New Function": "", "New Note": "", "New Password": "Nieuw Wachtwoord", "New Tool": "", "new-channel": "nieuw-kanaal", "Next message": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "<PERSON>n <PERSON> gevonden", "No content found in file.": "", "No content to speak": "<PERSON><PERSON> inhoud om over te spreken", "No distance available": "<PERSON><PERSON> a<PERSON>tand be<PERSON>", "No feedbacks found": "<PERSON><PERSON> gevonden", "No file selected": "<PERSON><PERSON> bestand gese<PERSON>eerd", "No groups with access, add a group to grant access": "<PERSON><PERSON> groepen met toegang, voeg een groep toe om toegang te geven", "No HTML, CSS, or JavaScript content found.": "<PERSON><PERSON>, CSS, of JavaScript inhoud gevonden", "No inference engine with management support found": "<PERSON>n inferentie-engine met beheerondersteuning gevonden", "No knowledge found": "<PERSON><PERSON> kennis gevonden", "No memories to clear": "<PERSON>n her<PERSON> om op te ruimen", "No model IDs": "<PERSON><PERSON> model-ID's", "No models found": "<PERSON><PERSON> modellen gevo<PERSON>", "No models selected": "<PERSON><PERSON> modellen g<PERSON>d", "No Notes": "", "No results found": "<PERSON>n resultaten gevonden", "No search query generated": "<PERSON><PERSON> z<PERSON>kopdracht gegenereerd", "No source available": "<PERSON><PERSON> bron be<PERSON>", "No users were found.": "Geen gebruikers gevonden", "No valves to update": "<PERSON><PERSON> kleppen om bij te werken", "None": "<PERSON><PERSON>", "Not factually correct": "<PERSON><PERSON> feit<PERSON> jui<PERSON>", "Not helpful": "<PERSON><PERSON> nuttig", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Opmerking: <PERSON><PERSON> je een minimumscore instelt, levert de zoekopdracht alleen documenten op met een score groter dan of gelijk aan de minimumscore.", "Notes": "Aantekeningen", "Notification Sound": "Notificatiegeluid", "Notification Webhook": "Notificatie-webhook", "Notifications": "Notificaties", "November": "November", "OAuth ID": "OAuth ID", "October": "Oktober", "Off": "Uit", "Okay, Let's Go!": "<PERSON><PERSON>, laten we gaan!", "OLED Dark": "OLED <PERSON>", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "Ollama API-instellingen bijgewerkt", "Ollama Version": "Ollama Versie", "On": "<PERSON><PERSON>", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "Alleen alfanumerieke tekens en koppeltekens zijn toegestaan", "Only alphanumeric characters and hyphens are allowed in the command string.": "Alleen alfanumerieke karakters en streepjes zijn toeges<PERSON>an in de commando string.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "<PERSON>een verzamelinge kunnen gewij<PERSON>d worden, maak een nieuwe kennisbank aan om bestanden aan te passen/toe te voegen", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "<PERSON>een geselecteerde gebruikers en groepen met toes<PERSON><PERSON> hebben toegang", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Oeps! Het lijkt erop dat de URL ongeldig is. Controleer het nogmaals en probeer opnieuw.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Oeps! Er zijn nog bestanden aan het uploaden. Wacht tot het uploaden voltooid is.", "Oops! There was an error in the previous response.": "Oeps! E<PERSON> was een fout in de vorige reactie.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Oeps! Je gebruikt een niet-ondersteunde methode (alleen frontend). Serveer de WebUI vanuit de backend.", "Open file": "Open bestand", "Open in full screen": "Open in volledig scherm", "Open modal to configure connection": "", "Open new chat": "Open nieuwe chat", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "Open WebUI gebruikt faster-whisper intern", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI gebruikt SpeechT5 en CMU Arctic spreker-embeddings", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Open WebUI versie (v{{OPEN_WEBUI_VERSION}}) is kleiner dan de benodigde versie (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API-configuratie", "OpenAI API Key is required.": "OpenAI API-sleutel is verplicht", "OpenAI API settings updated": "OpenAI API-sleutel bijgewerkt", "OpenAI URL/Key required.": "OpenAI URL/Sleutel vereist.", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "of", "Organize your users": "Orden je gebruikers", "Other": "<PERSON><PERSON>", "OUTPUT": "UITVOER", "Output format": "Uitvoerformaat", "Output Format": "", "Overview": "Overzicht", "page": "pagina", "Paginate": "", "Parameters": "", "Password": "Wachtwoord", "Paste Large Text as File": "<PERSON><PERSON> grote tekst als bestand", "PDF document (.pdf)": "PDF document (.pdf)", "PDF Extract Images (OCR)": "PDF extraheer afbeeldingen (OCR)", "pending": "wachtend", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "Toegang geweigerd bij het toegang krijgen tot media-apparaten", "Permission denied when accessing microphone": "Toegang geweigerd bij toegang tot de microfoon", "Permission denied when accessing microphone: {{error}}": "Toestemming geweigerd bij toegang tot microfoon: {{error}}", "Permissions": "Toestemmingen", "Perplexity API Key": "Perplexity API-sleutel", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "<PERSON><PERSON><PERSON>", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "Zet vast", "Pinned": "Vastgezet", "Pioneer insights": "Verken inzichten", "Pipeline deleted successfully": "Pijpleiding succesvol verwijderd", "Pipeline downloaded successfully": "Pijpleiding succesvol gedownload", "Pipelines": "Pijpleidingen", "Pipelines Not Detected": "Pijple<PERSON> niet gedetecteerd", "Pipelines Valves": "Pijpleidingen Kleppen", "Plain text (.md)": "", "Plain text (.txt)": "Platte tekst (.txt)", "Playground": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "Beoordeel de volgende waarschuwingen nauwkeurig:", "Please do not close the settings page while loading the model.": "Sluit de instellingenpagina niet terwijl het model geladen wordt.", "Please enter a prompt": "<PERSON><PERSON><PERSON> een prompt in", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "<PERSON>oer alle velden in", "Please select a model first.": "Selecteer eerst een model", "Please select a model.": "Selecteer een model", "Please select a reason": "<PERSON><PERSON>r een reden in", "Port": "Poort", "Positive attitude": "Positieve positie", "Prefix ID": "Voorvoegsel-ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Voorvoegsel-ID wordt gebruikt om conflicten met andere verbindingen te vermijden door een voorvoegsel aan het model-ID toe te voegen - laat leeg om uit te schakelen", "Prevent file creation": "", "Preview": "", "Previous 30 days": "Afgelopen 30 dagen", "Previous 7 days": "Afgelopen 7 dagen", "Previous message": "", "Private": "Priv<PERSON>", "Profile Image": "Profielafbeelding", "Prompt": "Prompt", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (bv. <PERSON><PERSON><PERSON> me een leuke g<PERSON> over het Romeinse Rijk)", "Prompt Autocompletion": "Automatische promptaanvulling", "Prompt Content": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Prompt created successfully": "Prompt succesvol aangemaakt", "Prompt suggestions": "Promptsuggesties", "Prompt updated successfully": "Prompt succesvol bijgewerkt", "Prompts": "Prompts", "Prompts Access": "Pro<PERSON><PERSON><PERSON><PERSON>", "Prompts Public Sharing": "Publiek prompts delen", "Public": "Publiek", "Pull \"{{searchValue}}\" from Ollama.com": "<PERSON>al \"{{searchValue}}\" uit Ollama.com", "Pull a model from Ollama.com": "<PERSON><PERSON> een model van Ollama.com", "Query Generation Prompt": "Vraaggeneratieprompt", "RAG Template": "RAG-sjabloon", "Rating": "Beoordeling", "Re-rank models by topic similarity": "Herrangschik modellen op basis van onderwerpsovereenkomst", "Read": "<PERSON><PERSON><PERSON><PERSON>", "Read Aloud": "<PERSON><PERSON><PERSON><PERSON>", "Reason": "", "Reasoning Effort": "Redeneerinspanning", "Record": "", "Record voice": "Neem stem op", "Redirecting you to Open WebUI Community": "Je wordt doorgestuurd naar OpenWebUI Community", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "<PERSON>er<PERSON><PERSON> de kans op het genereren van onzin. <PERSON>en hogere waarde (bijv. 100) zal meer diverse antwoorden geven, terwijl een lagere waarde (bijv. 10) conservatiever zal zijn.", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Refereer naar j<PERSON><PERSON> als \"user\" (bv. \"User is <PERSON><PERSON> aan het leren\")", "References from": "Referenties van", "Refused when it shouldn't have": "Gewei<PERSON><PERSON> terwi<PERSON>l het niet had moeten", "Regenerate": "Regenereren", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "Release-opmerkingen", "Releases": "", "Relevance": "Relev<PERSON><PERSON>", "Relevance Threshold": "", "Remove": "Verwijderen", "Remove {{MODELID}} from list.": "", "Remove Model": "Verwijder model", "Remove this tag from list": "", "Rename": "<PERSON><PERSON><PERSON><PERSON>", "Reorder Models": "Herschik modellen", "Reply in Thread": "Antwoord in draad", "Reranking Engine": "", "Reranking Model": "Reranking Model", "Reset": "Herstellen", "Reset All Models": "<PERSON>stel alle modellen", "Reset Upload Directory": "Herstel Uploadmap", "Reset Vector Storage/Knowledge": "Herstel Vectoropslag/-kennis", "Reset view": "<PERSON><PERSON><PERSON> zicht", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Antwoordmeldingen kunnen niet worden geactiveerd omdat de rechten voor de website zijn geweigerd. Ga naar de instellingen van uw browser om de benodigde toegang te verlenen.", "Response splitting": "Antwoord splitsing", "Response Watermark": "", "Result": "Resultaat", "Retrieval": "<PERSON><PERSON><PERSON>", "Retrieval Query Generation": "Ophaalqueriegeneratie", "Rich Text Input for Chat": "Rijke tekstinvoer voor chatten", "RK": "RK", "Role": "Rol", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RNL", "Run": "Uitvoeren", "Running": "Aan het uit<PERSON>eren", "Save": "Opsla<PERSON>", "Save & Create": "Opslaan & Creëren", "Save & Update": "Opslaan & Bijwerken", "Save As Copy": "<PERSON><PERSON><PERSON> als kopie", "Save Tag": "Bewaar Tag", "Saved": "Opgeslagen", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Chat logs direct opslaan in de opslag van je browser wordt niet langer ondersteund. Neem even de tijd om je chat logs te downloaden en te verwijderen door op de knop hieronder te klikken. Maak je geen zorgen, je kunt je chat logs eenvoudig opnieuw importeren naar de backend via", "Scroll On Branch Change": "", "Search": "<PERSON><PERSON>", "Search a model": "<PERSON><PERSON> een model", "Search Base": "Zoeken naar basis", "Search Chats": "<PERSON>ts zoeken", "Search Collection": "Zoek naar verzamelingen", "Search Filters": "Zoek naar filters", "search for tags": "<PERSON><PERSON> naar <PERSON>", "Search Functions": "<PERSON><PERSON> naar functie", "Search Knowledge": "<PERSON><PERSON>", "Search Models": "<PERSON><PERSON>", "Search options": "Opties zoeken", "Search Prompts": "Prompts zoeken", "Search Result Count": "Aantal zoekresultaten", "Search the internet": "Zoek op het internet", "Search Tools": "<PERSON><PERSON>", "SearchApi API Key": "SearchApi API-sleutel", "SearchApi Engine": "SearchApi Engine", "Searched {{count}} sites": "Zocht op {{count}} sites", "Searching \"{{searchQuery}}\"": "\"{{searchQ<PERSON>y}}\" aan het zoeken.", "Searching Knowledge for \"{{searchQuery}}\"": "<PERSON><PERSON> kennis bij \"{{searchQuery}}\"", "Searching the web...": "", "Searxng Query URL": "Searxng Query URL", "See readme.md for instructions": "<PERSON><PERSON> readme.md voor instructies", "See what's new": "<PERSON>ie wat er nieuw is", "Seed": "Seed", "Select a base model": "Selecteer een basismodel", "Select a engine": "Selecteer een engine", "Select a function": "Selecteer een functie", "Select a group": "Selecteer een groep", "Select a model": "Selecteer een model", "Select a pipeline": "Selecteer een pij<PERSON><PERSON>jn", "Select a pipeline url": "Selecteer een p<PERSON><PERSON><PERSON><PERSON><PERSON>-URL", "Select a tool": "Selecteer een tool", "Select an auth method": "Selecteer een authenticati<PERSON>", "Select an Ollama instance": "Selecteer een <PERSON>-instantie", "Select Engine": "Selecteer Engine", "Select Knowledge": "Selecteer kennis", "Select only one model to call": "Selecteer maar <PERSON> model om aan te roepen", "Selected model(s) do not support image inputs": "Geselecteerde modellen ondersteunen geen beeldinvoer", "Semantic distance to query": "Semantische afstand tot query", "Send": "Verzenden", "Send a Message": "<PERSON><PERSON><PERSON> een bericht", "Send message": "<PERSON><PERSON><PERSON>", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Stuurt `stream_options: { include_usage: true }` in het verzoek. \nOndersteunde providers zullen informatie over tokengebruik in het antwoord terugsturen als dit aan staat.", "September": "September", "SerpApi API Key": "SerpApi API-sleutel", "SerpApi Engine": "SerpApi-engine", "Serper API Key": "Serper API-sleutel", "Serply API Key": "Serply API-sleutel", "Serpstack API Key": "Serpstack API-sleutel", "Server connection verified": "Server verbinding geverifieerd", "Set as default": "Stel in als standaard", "Set CFG Scale": "Stel CFG-schaal in", "Set Default Model": "Stel Standaardmodel in", "Set embedding model": "Stel embedding-model in", "Set embedding model (e.g. {{model}})": "Stel embedding-model in (bv. {{model}})", "Set Image Size": "Stel afbeeldingsgrootte in", "Set reranking model (e.g. {{model}})": "Stel reranking-model in (bv. {{model}})", "Set Sampler": "<PERSON><PERSON> Sam<PERSON> in", "Set Scheduler": "Stel planner in", "Set Steps": "<PERSON><PERSON> stappen in", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "<PERSON>el het aantal lagen in dat wordt overgeheveld naar de GPU. Het verhogen van deze waarde kan de prestaties aanzienlijk verbeteren voor modellen die geoptimaliseerd zijn voor GPU-versnelling, maar kan ook meer stroom en GPU-bronnen verbruiken.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Stel het aantal threads in dat wordt gebruikt voor berekeningen. Deze optie bepaalt hoeveel threads worden gebruikt om gelijktijdig binnenkomende verzoeken te verwerken. Het verhogen van deze waarde kan de prestaties verbeteren onder hoge concurrency werklasten, maar kan ook meer CPU-bronnen verbruiken.", "Set Voice": "Stel stem in", "Set whisper model": "<PERSON><PERSON> Whisper-model in", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Stelt een vlakke bias in tegen tokens die minstens één keer zijn voorgekomen. <PERSON><PERSON> hogere waarde (bijv. 1,5) straft her<PERSON>en sterker af, terwijl een lagere waarde (bijv. 0,9) toegeeflijker is. Bij 0 is het uitgeschakeld.", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "Stelt een schaalvo<PERSON>eel in tegen tokens om herhalingen te bestraffen, gebase<PERSON> op hoe vaak ze zijn voorgekomen. <PERSON>en hogere waarde (bijv. 1,5) straft herhalingen sterker af, terwijl een lagere waarde (bijv. 0,9) toegeeflijker is. Bij 0 is het uitgeschakeld.", "Sets how far back for the model to look back to prevent repetition.": "<PERSON><PERSON><PERSON> in hoe ver het model terug moet kijken om herhaling te voorkomen.", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "<PERSON><PERSON><PERSON> de willekeurigheid in om te gebruiken voor het genereren. Als je dit op een specifiek getal instelt, genereert het model dezelfde tekst voor dezelfde prompt.", "Sets the size of the context window used to generate the next token.": "<PERSON><PERSON><PERSON> de grootte van het contextvenster in dat gebruikt wordt om het volgende token te genereren.", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Stelt de te gebruiken stopsequentie in. <PERSON>s dit patroon wordt gevonden, stopt de LLM met het genereren van tekst en keert terug. Er kunnen meerdere stoppatronen worden ingesteld door meerdere afzonderlijke stopparameters op te geven in een modelbestand.", "Settings": "Instellingen", "Settings saved successfully!": "Instellingen succesvol opgeslagen!", "Share": "<PERSON><PERSON>", "Share Chat": "Deel chat", "Share to Open WebUI Community": "<PERSON><PERSON> naar OpenWebUI-community", "Sharing Permissions": "Deeltoestemmingen", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "<PERSON>n", "Show \"What's New\" modal on login": "Toon \"Wat is nieuw\" bij inloggen", "Show Admin Details in Account Pending Overlay": "Admin-details weergeven in overlay in afwachting van account", "Show All": "", "Show Less": "", "Show Model": "Toon model", "Show shortcuts": "Toon snelkoppelingen", "Show your support!": "Toon je steun", "Showcased creativity": "Toonde creativiteit", "Sign in": "Inloggen", "Sign in to {{WEBUI_NAME}}": "Log in bij {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "Log in bij {{WEBUI_NAME}} met LDAP", "Sign Out": "Uitloggen", "Sign up": "Registreren", "Sign up to {{WEBUI_NAME}}": "Meld je aan bij {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "", "Signing in to {{WEBUI_NAME}}": "Aan het inloggen bij {{WEBUI_NAME}}", "sk-1234": "sk-1234", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "<PERSON><PERSON>", "Speech Playback Speed": "<PERSON>fs<PERSON><PERSON><PERSON><PERSON><PERSON> spraak", "Speech recognition error: {{error}}": "Spraakherkenning fout: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "Spraak-naar-tekst Engine", "Stop": "Stop", "Stop Generating": "", "Stop Sequence": "Stopsequentie", "Stream Chat Response": "Stream chat-antwoord", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "STT Model", "STT Settings": "STT Instellingen", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "Ondertitel (bijv. over de Romeinse Empire)", "Success": "Succes", "Successfully updated.": "Succesvol bijgewerkt.", "Suggested": "Suggestie", "Support": "Ondersteuning", "Support this plugin:": "ondersteun deze plugin", "Supported MIME Types": "", "Sync directory": "Synchroniseer map", "System": "Systeem", "System Instructions": "Systeem instructies", "System Prompt": "Systeem prompt", "Tags": "Tags", "Tags Generation": "Tag<PERSON><PERSON><PERSON>", "Tags Generation Prompt": "Prompt voor taggeneratie", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "Tail free sampling wordt gebruikt om de impact van minder waarschijnlijke tokens uit de uitvoer te verminderen. Een hogere waarde (bijvoorbeeld 2,0) zal de impact meer verminderen, terwijl een waarde van 1,0 deze instelling uitschakelt.", "Talk to model": "Praat met model", "Tap to interrupt": "Tik om te onderbreken", "Task Model": "", "Tasks": "Taken", "Tavily API Key": "Tavily <PERSON>-sleutel", "Tavily Extract Depth": "", "Tell us more:": "V<PERSON><PERSON> ons meer:", "Temperature": "Temperatuur", "Temporary Chat": "Tijdelijke chat", "Text Splitter": "Tekst splitser", "Text-to-Speech": "", "Text-to-Speech Engine": "Tekst-naar-Spraak Engine", "Thanks for your feedback!": "Bedankt voor je feedback!", "The Application Account DN you bind with for search": "Het applicatieaccount DN waarmee je zoekt", "The base to search for users": "De basis om gebruikers te zoeken", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "De batchgrootte bepaalt hoeveel tekstverzoeken tegelijk worden verwerkt. Een hogere batchgrootte kan de prestaties en snelheid van het model verhogen, maar vereist ook meer geheugen.", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "De ontwikkelaars achter deze plugin zijn gepassioneerde vrijwilligers uit de gemeenschap. Als je deze plugin nuttig vindt, overweeg dan om bij te dragen aan de ontwikkeling ervan.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Het beoordelingsklassement is gebaseerd op het Elo-classificatiesysteem en wordt in realtime bijgewerkt.", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "Het LDAP-attribuut dat verwijst naar de e-mail waarmee gebruikers zich aanmelden.", "The LDAP attribute that maps to the username that users use to sign in.": "Het LDAP-attribuut dat verwijst naar de gebruikersnaam die gebruikers gebruiken om in te loggen.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Het leaderboard is momenteel in bèta en we kunnen de ratingberekeningen aanpassen naarmate we het algoritme verfijnen.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "De maximale bestandsgrootte in MB. Als het bestand groter is dan deze limiet, wordt het bestand niet geüpload.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Het maximum aantal bestanden dat in <PERSON><PERSON> keer kan worden gebruikt in de chat. Als het aantal bestanden deze limiet overschrijdt, worden de bestanden niet geüpload.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "De score moet een waarde zijn tussen 0.0 (0%) en 1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "De temperatuur van het model. De temperatuur groter maken zal het model creatiever laten antwoorden.", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "<PERSON>a", "Thinking...": "Aan het denken...", "This action cannot be undone. Do you wish to continue?": "Deze actie kan niet ongedaan worden gemaakt. Wilt u doorgaan?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "<PERSON>t kanaal was g<PERSON><PERSON><PERSON><PERSON> op {{createdAt}}. Dit het begin van het {{channelName}} kanaal.", "This chat won't appear in history and your messages will not be saved.": "", "This chat won’t appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Dit zorgt ervoor dat je waardevolle gesprekken veilig worden opgeslagen in je backend database. Dank je wel!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Dit is een experimentele functie, het kan functioneren zoals verwacht en kan op elk moment veranderen.", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "Deze optie bepaalt hoeveel tokens bewaard blijven bij het verversen van de context. Als deze bijvoorbeeld op 2 staat, worden de laatste 2 tekens van de context van het gesprek bewaard. Het behouden van de context kan helpen om de continuïteit van een gesprek te behouden, maar het kan de mogelijkheid om te reageren op nieuwe onderwerpen verminderen.", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "Deze optie stelt het maximum aantal tokens in dat het model kan genereren in zijn antwoord. Door dit limiet te verhogen, kan het model langere antwoorden geven, maar het kan ook de kans vergroten dat er onbehulpzame of irrelevante inhoud wordt gegenereerd.", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Deze optie verwijdert alle bestaande bestanden in de collectie en vervangt ze door nieuw geüploade bestanden.", "This response was generated by \"{{model}}\"": "Dit antwoord is gegenereerd door  \"{{model}}\"", "This will delete": "Dit zal verwijderen", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "Dit zal <strong>{{NAME}}</strong> verwijderen en <strong>al zijn inhoud</strong>.", "This will delete all models including custom models": "Dit zal alle modellen, ook aangepaste modellen, verwijderen", "This will delete all models including custom models and cannot be undone.": "<PERSON>t zal alle modellen, ook aangepaste modellen, verwijderen en kan niet ontdaan worden", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Dit zal de kennisdatabase resetten en alle bestanden synchroniseren. Wilt u doorgaan?", "Thorough explanation": "Gevorderde uitleg", "Thought for {{DURATION}}": "<PERSON>cht {{DURATION}}", "Thought for {{DURATION}} seconds": "<PERSON>cht {{DURATION}} seconden", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Tika <PERSON>-URL vereist", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Tip: Werk meerdere variabele slots achtereenvolgens bij door op de tab-toets te drukken in de chat input na elke vervanging.", "Title": "Titel", "Title (e.g. Tell me a fun fact)": "Titel (bv. <PERSON><PERSON><PERSON> me een leuke geb<PERSON>)", "Title Auto-Generation": "Titel Auto-Generatie", "Title cannot be an empty string.": "<PERSON>itel kan niet leeg zijn.", "Title Generation": "Titelgenerati<PERSON>", "Title Generation Prompt": "Titel Generatie Prompt", "TLS": "TLS", "To access the available model names for downloading,": "Om de beschikbare modelnamen voor downloaden te openen,", "To access the GGUF models available for downloading,": "<PERSON>m toegang te krijgen tot de GGUF-modellen die beschikbaar zijn voor downloaden,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "<PERSON>m toegang te krijgen tot de WebUI, neem contact op met de administrator. Beheerders kunnen de gebruikersstatussen beheren vanuit het Beheerderspaneel.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Om hier een kennisbron bij te voegen, voeg ze eerst aan de \"Kennis\" werkplaats toe.", "To learn more about available endpoints, visit our documentation.": "<PERSON><PERSON> meer over be<PERSON><PERSON><PERSON><PERSON> endpoints te leren, bezoek onze documentatie.", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Om je privacy te beschermen, worden alleen beoordelingen, model-ID's, tags en metadata van je feedback gedeeld - je chatlogs blijven privé en worden niet opgenomen.", "To select actions here, add them to the \"Functions\" workspace first.": "Om hier acties te selecteren, voeg ze eerst aan de \"Functies\" Werkplaats toe.", "To select filters here, add them to the \"Functions\" workspace first.": "<PERSON>m hier filters te selecteren, voeg ze eerst aan de \"Functies\" Werkplaats toe.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Om hier gereedschapssets te selecteren, voeg ze eerst aan de \"Gereedschappen\" Werkplaats toe.", "Toast notifications for new updates": "Toon notificaties voor nieuwe updates", "Today": "Vandaag", "Toggle search": "", "Toggle settings": "Wissel instellingen", "Toggle sidebar": "Wissel sidebar", "Toggle whether current connection is active.": "", "Token": "Token", "Too verbose": "Te langdradig", "Tool created successfully": "Gereedschap succesvol aangemaakt", "Tool deleted successfully": "Gereedschap succesvol verwijderd", "Tool Description": "Gereedschapbeschrijving", "Tool ID": "Gereedschaps-ID", "Tool imported successfully": "Gereedschap succesvol geïmporteerd", "Tool Name": "Gereedschapsnaam", "Tool Servers": "", "Tool updated successfully": "Gereedschap succesvol bijgewerkt", "Tools": "Gereedschappen", "Tools Access": "Gereedschaptoegang", "Tools are a function calling system with arbitrary code execution": "Gereedschappen zijn een systeem voor het aanroep<PERSON> van functies met willekeurige code-uitvoering", "Tools Function Calling Prompt": "Gereedschapsfunctie aanroepprompt", "Tools have a function calling system that allows arbitrary code execution.": "Gereedschappen hebben een systeem voor het aanroepen van functies waarmee willekeurige code kan worden uitgevoerd", "Tools Public Sharing": "Gereedschappen publiek delen", "Top K": "Top K", "Top K Reranker": "Top K herranker", "Transformers": "Transformers", "Trouble accessing Ollama?": "<PERSON><PERSON> met toe<PERSON>g tot <PERSON>?", "Trust Proxy Environment": "Vertrouwelijk proxyomgeving", "TTS Model": "TTS Model", "TTS Settings": "TTS instellingen", "TTS Voice": "TTS Stem", "Type": "Type", "Type Hugging Face Resolve (Download) URL": "Type Hugging Face Resolve (Download) URL", "Uh-oh! There was an issue with the response.": "Oh-oh! Er was een probleem met het antwoord.", "UI": "UI", "Unarchive All": "Onarchiveer alles", "Unarchive All Archived Chats": "Onarchiveer alle gearchiveerde chats", "Unarchive Chat": "Onarchiveer chat", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "Ontsleutel mysteries", "Unpin": "Losmaken", "Unravel secrets": "Ontrafel geheimen", "Untagged": "Ongemarkeerd", "Untitled": "", "Update": "Bijwerken", "Update and Copy Link": "Bijwerken en kopieer link", "Update for the latest features and improvements.": "Bijwerken voor de nieuwste functies en verbeteringen", "Update password": "W<PERSON><PERSON>zig wachtwoord", "Updated": "Bijgewerkt", "Updated at": "Bijgewerkt om", "Updated At": "Bijgewerkt om", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "Upgrade naar een licentie voor meer mogelijkheden, waaronder aangepaste thematisering en branding, en speciale ondersteuning.", "Upload": "Uploaden", "Upload a GGUF model": "Upload een GGUF-model", "Upload Audio": "", "Upload directory": "Upload map", "Upload files": "Bestanden uploaden", "Upload Files": "Bestanden uploaden", "Upload Pipeline": "Upload Pijpleiding", "Upload Progress": "Upload Voortgang", "URL": "URL", "URL Mode": "URL-modus", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "Gebruik '#' in de promptinvoer om je kennis te laden en op te nemen.", "Use Gravatar": "Gebruik Gravatar", "Use groups to group your users and assign permissions.": "Gebruik groepen om gebruikers te groeperen en rechten aan te wijzen", "Use Initials": "Gebruik initialen", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "g<PERSON><PERSON><PERSON><PERSON>", "User": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "User location successfully retrieved.": "Gebruikerslocatie succesvol opgehaald", "User Webhooks": "Gebruiker-webhooks", "Username": "Gebruikersnaam", "Users": "Gebruikers", "Using the default arena model with all models. Click the plus button to add custom models.": "<PERSON><PERSON> standaard arena-model <PERSON><PERSON><PERSON><PERSON><PERSON> met alle modellen. Klik op de plusknop om aangepaste modellen toe te voegen.", "Utilize": "Utilize", "Valid time units:": "Geldige tijdseenheden:", "Valves": "<PERSON><PERSON><PERSON><PERSON>", "Valves updated": "Kleppen bijgewerkt", "Valves updated successfully": "Kleppen succesvol bijgewerkt", "variable": "variabele", "variable to have them replaced with clipboard content.": "variabele om ze te laten vervangen door klembord inhoud.", "Verify Connection": "Controleer verbinding", "Verify SSL Certificate": "", "Version": "<PERSON><PERSON><PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "Versie {{selectedVersion}} van {{totalVersions}}", "View Replies": "Bekijke resultaten", "View Result from **{{NAME}}**": "", "Visibility": "<PERSON><PERSON><PERSON>ba<PERSON><PERSON><PERSON>", "Vision": "", "Voice": "<PERSON><PERSON>", "Voice Input": "St<PERSON>in<PERSON><PERSON>", "Voice mode": "", "Warning": "Waarschuwing", "Warning:": "Waarschuwing", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Waarschuwing: Door dit in te schakelen kunnen gebruikers willekeurige code uploaden naar de server.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Warning: Als je de embedding model bijwerkt of wij<PERSON>t, moet je alle documenten opnieuw importeren.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "Waarschuwing: <PERSON><PERSON><PERSON> kan willekeurige code uitvoeren, wat ernstige veiligheidsrisico's met zich meebrengt - ga uiterst voorzichtig te werk. ", "Web": "Web", "Web API": "Web-API", "Web Loader Engine": "", "Web Search": "Zoeken op het web", "Web Search Engine": "Zoekmachine op het web", "Web Search in Chat": "<PERSON><PERSON><PERSON><PERSON><PERSON> in chat", "Web Search Query Generation": "Zoekopdracht generatie", "Webhook URL": "Webhook URL", "WebUI Settings": "WebUI Instellingen", "WebUI URL": "WebUI-URL", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI zal verzoeken doen aan \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI zal verzoeken doen aan \"{{url}}/chat/completions\"", "Weight of BM25 Retrieval": "", "What are you trying to achieve?": "Wat probeer je te bereiken?", "What are you working on?": "Waar werk je aan?", "What's New in": "Wat is nieuw in", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "<PERSON>s dit is ingeschakeld, reageert het model op elk chatbericht in real-time, waarbij een reactie wordt gegenereerd zodra de gebruiker een bericht stuurt. Deze modus is handig voor live chat-toepassingen, maar kan de prestaties op langzamere hardware beïnvloeden.", "wherever you are": "waar je ook bent", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "<PERSON><PERSON><PERSON> (Lokaal)", "Why?": "Waarom?", "Widescreen Mode": "Breedschermmodus", "Won": "Gewonnen", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "<PERSON><PERSON><PERSON> samen met top-k<PERSON> <PERSON><PERSON> hogere waarde (bijv. 0,95) leidt tot meer diverse tekst, terwijl een lagere waarde (bijv. 0,5) meer gerichte en conservatieve tekst genereert.", "Workspace": "Werkruimte", "Workspace Permissions": "Werkruimtemachtigingen", "Write": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Write a prompt suggestion (e.g. Who are you?)": "<PERSON>hrij<PERSON> een prompt suggestie (bijv. Wie ben je?)", "Write a summary in 50 words that summarizes [topic or keyword].": "<PERSON><PERSON><PERSON><PERSON><PERSON> een samenvatting in 50 woorden die [onderwerp of trefwoord] samenvat.", "Write something...": "<PERSON><PERSON><PERSON><PERSON><PERSON> iets...", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "Gisteren", "You": "<PERSON><PERSON>", "You are currently using a trial license. Please contact support to upgrade your license.": "Je gebruikt momenteel een proeflicentie. Neem contact op met de ondersteuning om je licentie te upgraden.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Je kunt slechts met maximaal {{maxCount}} bestand(en) tegelijk chatten", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Je kunt je interacties met LLM's personaliseren door herinneringen toe te voegen via de '<PERSON>heer'-knop hieronder, waardoor ze nuttiger en voor jou op maat gemaakt worden.", "You cannot upload an empty file.": "Je kunt een leeg bestand niet uploaden.", "You do not have permission to upload files.": "Je hebt geen toestemming om bestanden up te loaden", "You have no archived conversations.": "Je hebt geen gearchiveerde gesprekken.", "You have shared this chat": "Je hebt dit gesp<PERSON> gedeeld", "You're a helpful assistant.": "Je bent een behulpzame assistent.", "You're now logged in.": "Je bent nu ingelogd.", "Your account status is currently pending activation.": "Je accountstatus wacht nu op activatie", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Je volledige bijdrage gaat direct naar de ontwikkelaar van de plugin; Open WebUI neemt hier geen deel van. Het gekozen financieringsplatform kan echter wel zijn eigen kosten hebben.", "Youtube": "Youtube", "Youtube Language": "Youtube-taal", "Youtube Proxy URL": "Youtube-proxy-URL"}