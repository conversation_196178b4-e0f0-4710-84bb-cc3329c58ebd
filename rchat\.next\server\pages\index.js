/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "__barrel_optimize__?names=Check,ChevronRight,Circle!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Check,ChevronRight,Circle!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Check: () => (/* reexport safe */ _icons_check_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   ChevronRight: () => (/* reexport safe */ _icons_chevron_right_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Circle: () => (/* reexport safe */ _icons_circle_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_check_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/check.js */ \"./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _icons_chevron_right_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/chevron-right.js */ \"./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _icons_circle_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/circle.js */ \"./node_modules/lucide-react/dist/esm/icons/circle.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGVjayxDaGV2cm9uUmlnaHQsQ2lyY2xlIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQ21EO0FBQ2UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yY2hhdC8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzPzNhNTQiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoZWNrIH0gZnJvbSBcIi4vaWNvbnMvY2hlY2suanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGV2cm9uUmlnaHQgfSBmcm9tIFwiLi9pY29ucy9jaGV2cm9uLXJpZ2h0LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2lyY2xlIH0gZnJvbSBcIi4vaWNvbnMvY2lyY2xlLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Check,ChevronRight,Circle!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Check,Copy,Edit2,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!****************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Check,Copy,Edit2,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Check: () => (/* reexport safe */ _icons_check_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Copy: () => (/* reexport safe */ _icons_copy_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Edit2: () => (/* reexport safe */ _icons_pen_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Trash2: () => (/* reexport safe */ _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_check_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/check.js */ \"./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _icons_copy_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/copy.js */ \"./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _icons_pen_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/pen.js */ \"./node_modules/lucide-react/dist/esm/icons/pen.js\");\n/* harmony import */ var _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/trash-2.js */ \"./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGVjayxDb3B5LEVkaXQyLFRyYXNoMiE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQ21EO0FBQ0Y7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3JjaGF0Ly4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/NmU4NSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hlY2sgfSBmcm9tIFwiLi9pY29ucy9jaGVjay5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENvcHkgfSBmcm9tIFwiLi9pY29ucy9jb3B5LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRWRpdDIgfSBmcm9tIFwiLi9pY29ucy9wZW4uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUcmFzaDIgfSBmcm9tIFwiLi9pY29ucy90cmFzaC0yLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Check,Copy,Edit2,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,MessageSquare,Plus,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,MessageSquare,Plus,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChevronLeft: () => (/* reexport safe */ _icons_chevron_left_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   ChevronRight: () => (/* reexport safe */ _icons_chevron_right_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Clock: () => (/* reexport safe */ _icons_clock_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   MessageSquare: () => (/* reexport safe */ _icons_message_square_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Plus: () => (/* reexport safe */ _icons_plus_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Trash2: () => (/* reexport safe */ _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_chevron_left_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/chevron-left.js */ \"./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _icons_chevron_right_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/chevron-right.js */ \"./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _icons_clock_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/clock.js */ \"./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _icons_message_square_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/message-square.js */ \"./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _icons_plus_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/plus.js */ \"./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/trash-2.js */ \"./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGV2cm9uTGVmdCxDaGV2cm9uUmlnaHQsQ2xvY2ssTWVzc2FnZVNxdWFyZSxQbHVzLFRyYXNoMiE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUNnRTtBQUNFO0FBQ2Y7QUFDaUI7QUFDbkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yY2hhdC8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzP2I0MjUiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoZXZyb25MZWZ0IH0gZnJvbSBcIi4vaWNvbnMvY2hldnJvbi1sZWZ0LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hldnJvblJpZ2h0IH0gZnJvbSBcIi4vaWNvbnMvY2hldnJvbi1yaWdodC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENsb2NrIH0gZnJvbSBcIi4vaWNvbnMvY2xvY2suanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNZXNzYWdlU3F1YXJlIH0gZnJvbSBcIi4vaWNvbnMvbWVzc2FnZS1zcXVhcmUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBQbHVzIH0gZnJvbSBcIi4vaWNvbnMvcGx1cy5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFRyYXNoMiB9IGZyb20gXCIuL2ljb25zL3RyYXNoLTIuanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,MessageSquare,Plus,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=LogOut,Menu,Settings,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************************!*\
  !*** __barrel_optimize__?names=LogOut,Menu,Settings,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogOut: () => (/* reexport safe */ _icons_log_out_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Menu: () => (/* reexport safe */ _icons_menu_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Settings: () => (/* reexport safe */ _icons_settings_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   User: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_log_out_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/log-out.js */ \"./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _icons_menu_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/menu.js */ \"./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _icons_settings_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/settings.js */ \"./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/user.js */ \"./node_modules/lucide-react/dist/esm/icons/user.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Mb2dPdXQsTWVudSxTZXR0aW5ncyxVc2VyIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDc0Q7QUFDTDtBQUNRIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmNoYXQvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz85NGFmIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBMb2dPdXQgfSBmcm9tIFwiLi9pY29ucy9sb2ctb3V0LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTWVudSB9IGZyb20gXCIuL2ljb25zL21lbnUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTZXR0aW5ncyB9IGZyb20gXCIuL2ljb25zL3NldHRpbmdzLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVXNlciB9IGZyb20gXCIuL2ljb25zL3VzZXIuanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=LogOut,Menu,Settings,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Mic,MicOff,Paperclip,Send!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Mic,MicOff,Paperclip,Send!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Mic: () => (/* reexport safe */ _icons_mic_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   MicOff: () => (/* reexport safe */ _icons_mic_off_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Paperclip: () => (/* reexport safe */ _icons_paperclip_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Send: () => (/* reexport safe */ _icons_send_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_mic_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/mic.js */ \"./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _icons_mic_off_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/mic-off.js */ \"./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _icons_paperclip_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/paperclip.js */ \"./node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _icons_send_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/send.js */ \"./node_modules/lucide-react/dist/esm/icons/send.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1NaWMsTWljT2ZmLFBhcGVyY2xpcCxTZW5kIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDK0M7QUFDTztBQUNLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmNoYXQvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz9mMjFjIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNaWMgfSBmcm9tIFwiLi9pY29ucy9taWMuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNaWNPZmYgfSBmcm9tIFwiLi9pY29ucy9taWMtb2ZmLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGFwZXJjbGlwIH0gZnJvbSBcIi4vaWNvbnMvcGFwZXJjbGlwLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2VuZCB9IGZyb20gXCIuL2ljb25zL3NlbmQuanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Mic,MicOff,Paperclip,Send!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./src/pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\index.tsx */ \"./src/pages/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/chat/ChatHeader.tsx":
/*!********************************************!*\
  !*** ./src/components/chat/ChatHeader.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Settings,User!=!lucide-react */ \"__barrel_optimize__?names=LogOut,Menu,Settings,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/UserContext */ \"./src/context/UserContext.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/avatar */ \"./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"./src/components/ui/dropdown-menu.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__, _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__]);\n([_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__, _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst ChatHeader = ({ title = \"RChat\", onSettingsClick, onSidebarToggle, sidebarOpen = false })=>{\n    const { authState, logout } = (0,_context_UserContext__WEBPACK_IMPORTED_MODULE_2__.useUserContext)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const handleLogout = ()=>{\n        logout();\n        router.push(\"/auth/login\");\n    };\n    const handleProfileClick = ()=>{\n        // Navigate to profile/account page when implemented\n        console.log(\"Profile clicked\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"h-16 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between h-full px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        onSidebarToggle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `${sidebarOpen ? \"md:hidden\" : \"\"} flex flex-none items-center`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onSidebarToggle,\n                                className: \"p-1.5 rounded-xl text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\",\n                                title: \"Toggle Sidebar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Menu, {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white font-bold text-sm\",\n                                children: \"R\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        onSettingsClick && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onSettingsClick,\n                            className: \"p-2 rounded-lg text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\",\n                            title: \"Settings\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Settings, {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, undefined),\n                        authState.user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"flex items-center space-x-2 p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                className: \"w-8 h-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarImage, {\n                                                        src: authState.user.profile_image_url\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                        className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white text-sm\",\n                                                        children: (authState.user.name || authState.user.email).charAt(0).toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-900 dark:text-white hidden sm:block\",\n                                                children: authState.user.name || authState.user.email\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                                    align: \"end\",\n                                    className: \"w-56\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                            onClick: handleProfileClick,\n                                            className: \"cursor-pointer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__.User, {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Profile\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuSeparator, {}, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                            onClick: handleLogout,\n                                            className: \"cursor-pointer text-red-600 dark:text-red-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__.LogOut, {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatHeader);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/chat/ChatHeader.tsx\n");

/***/ }),

/***/ "./src/components/chat/ChatHistorySidebar.tsx":
/*!****************************************************!*\
  !*** ./src/components/chat/ChatHistorySidebar.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_MessageSquare_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,MessageSquare,Plus,Trash2!=!lucide-react */ \"__barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,MessageSquare,Plus,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\n/* harmony import */ var _hooks_useConversationHistory__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useConversationHistory */ \"./src/hooks/useConversationHistory.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\nconst ChatHistorySidebar = ({ isOpen, onToggle, onConversationSelect, onNewChat, selectedConversationId, className = \"\" })=>{\n    const { conversations, isLoading, error, createNewConversation, deleteConversation, refreshConversations } = (0,_hooks_useConversationHistory__WEBPACK_IMPORTED_MODULE_3__.useConversationHistory)();\n    const [loadingConversationId, setLoadingConversationId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleConversationClick = async (conversation)=>{\n        setLoadingConversationId(conversation.id);\n        // Simulate loading delay\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        onConversationSelect(conversation.id);\n        setLoadingConversationId(null);\n    };\n    const handleNewChat = async ()=>{\n        try {\n            const conversationId = await createNewConversation();\n            if (conversationId) {\n                onNewChat();\n            }\n        } catch (error) {\n            console.error(\"Error creating new conversation:\", error);\n        }\n    };\n    const handleTestConversations = async ()=>{\n        console.log(\"Testing conversation retrieval...\");\n        await refreshConversations();\n        console.log(\"Current conversations:\", conversations);\n    };\n    const handleDeleteConversation = async (conversationId, e)=>{\n        e.stopPropagation();\n        try {\n            await deleteConversation(conversationId);\n        } catch (error) {\n            console.error(\"Error deleting conversation:\", error);\n        }\n    };\n    const groupConversationsByDate = (conversations)=>{\n        const now = new Date();\n        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n        const lastWeek = new Date(today);\n        lastWeek.setDate(lastWeek.getDate() - 7);\n        return {\n            today: conversations.filter((conv)=>{\n                const date = new Date(conv.updated_at || conv.created_at);\n                return date >= today;\n            }),\n            yesterday: conversations.filter((conv)=>{\n                const date = new Date(conv.updated_at || conv.created_at);\n                return date >= yesterday && date < today;\n            }),\n            thisWeek: conversations.filter((conv)=>{\n                const date = new Date(conv.updated_at || conv.created_at);\n                return date >= lastWeek && date < yesterday;\n            }),\n            older: conversations.filter((conv)=>{\n                const date = new Date(conv.updated_at || conv.created_at);\n                return date < lastWeek;\n            })\n        };\n    };\n    const groupedConversations = groupConversationsByDate(conversations);\n    const renderConversationGroup = (title, conversations)=>{\n        if (conversations.length === 0) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2 px-2\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: conversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>handleConversationClick(conversation),\n                            className: `group relative p-3 rounded-lg cursor-pointer transition-all duration-200 ${selectedConversationId === conversation.id ? \"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700\" : \"hover:bg-gray-100 dark:hover:bg-gray-800 border border-transparent\"} ${loadingConversationId === conversation.id ? \"opacity-50\" : \"\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_MessageSquare_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__.MessageSquare, {\n                                                            className: \"w-4 h-4 text-gray-400 dark:text-gray-500 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.truncateText)(conversation.name || \"Untitled Chat\", 30)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_MessageSquare_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Clock, {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatTimestamp)(conversation.updated_at)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"opacity-0 group-hover:opacity-100 transition-opacity\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>handleDeleteConversation(conversation.id, e),\n                                                className: \"p-1 rounded hover:bg-red-100 dark:hover:bg-red-900/20 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors\",\n                                                title: \"Delete conversation\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_MessageSquare_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Trash2, {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, undefined),\n                                loadingConversationId === conversation.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center justify-center bg-white/50 dark:bg-gray-900/50 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, conversation.id, true, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            !isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onToggle,\n                className: \"fixed top-20 left-4 z-50 p-2 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-all duration-200\",\n                title: \"Open chat history\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_MessageSquare_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__.ChevronRight, {\n                    className: \"w-5 h-5 text-gray-600 dark:text-gray-300\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                lineNumber: 161,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed top-16 left-0 h-[calc(100vh-4rem)] w-80 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 transform transition-transform duration-300 ease-in-out z-40 ${isOpen ? \"translate-x-0\" : \"-translate-x-full\"} ${className}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                            children: \"Chat History\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onToggle,\n                                            className: \"p-1.5 rounded-lg text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\",\n                                            title: \"Close chat history\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_MessageSquare_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__.ChevronLeft, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleNewChat,\n                                    className: \"w-full flex items-center justify-center gap-2 text-white font-medium py-2.5 px-4 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-200 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_MessageSquare_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Plus, {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"New Chat\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleTestConversations,\n                                    className: \"w-full flex items-center justify-center gap-2 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200\",\n                                    children: \"Test API\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-4 scrollbar-thin\",\n                            children: [\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-600 dark:text-red-400 text-sm\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: refreshConversations,\n                                            className: \"mt-2 text-xs text-red-800 dark:text-red-200 hover:underline\",\n                                            children: \"Try again\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, undefined),\n                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center py-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, undefined) : conversations.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_MessageSquare_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__.MessageSquare, {\n                                            className: \"w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                            children: \"No conversations yet\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 dark:text-gray-500 text-xs mt-1\",\n                                            children: \"Start a new chat to begin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        renderConversationGroup(\"Today\", groupedConversations.today),\n                                        renderConversationGroup(\"Yesterday\", groupedConversations.yesterday),\n                                        renderConversationGroup(\"This Week\", groupedConversations.thisWeek),\n                                        renderConversationGroup(\"Older\", groupedConversations.older)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-t border-gray-200 dark:border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 dark:text-gray-400 text-center\",\n                                children: \"Chat history powered by R2R\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/20 z-30 lg:hidden\",\n                onClick: onToggle\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatHistorySidebar.tsx\",\n                lineNumber: 253,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatHistorySidebar);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/chat/ChatHistorySidebar.tsx\n");

/***/ }),

/***/ "./src/components/chat/ChatInput.tsx":
/*!*******************************************!*\
  !*** ./src/components/chat/ChatInput.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Paperclip,Send!=!lucide-react */ \"__barrel_optimize__?names=Mic,MicOff,Paperclip,Send!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\n\n\nconst ChatInput = ({ onSendMessage, disabled = false, placeholder = \"Type your message...\" })=>{\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-resize textarea\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (textareaRef.current) {\n            textareaRef.current.style.height = \"auto\";\n            textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;\n        }\n    }, [\n        message\n    ]);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (message.trim() && !disabled) {\n            onSendMessage(message.trim());\n            setMessage(\"\");\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleVoiceClick = ()=>{\n        // Voice functionality placeholder - to be implemented later\n        if (isRecording) {\n            setIsRecording(false);\n            console.log(\"Stop recording\");\n        } else {\n            setIsRecording(true);\n            console.log(\"Start recording\");\n            // Auto-stop after 5 seconds for demo\n            setTimeout(()=>{\n                setIsRecording(false);\n            }, 5000);\n        }\n    };\n    const handleFileClick = ()=>{\n        // File upload functionality placeholder - to be implemented later\n        console.log(\"File upload clicked\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-end space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: handleFileClick,\n                            className: \"p-2 rounded-lg text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\",\n                            title: \"Attach file\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_2__.Paperclip, {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    ref: textareaRef,\n                                    value: message,\n                                    onChange: (e)=>setMessage(e.target.value),\n                                    onKeyDown: handleKeyDown,\n                                    placeholder: placeholder,\n                                    disabled: disabled,\n                                    rows: 1,\n                                    className: \"w-full px-4 py-3 pr-12 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\",\n                                    style: {\n                                        minHeight: \"48px\",\n                                        maxHeight: \"120px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, undefined),\n                                message.trim() === \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleVoiceClick,\n                                    className: `absolute right-3 top-1/2 transform -translate-y-1/2 p-1.5 rounded-full transition-colors ${isRecording ? \"text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20\" : \"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700\"}`,\n                                    title: isRecording ? \"Stop recording\" : \"Record voice\",\n                                    children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_2__.MicOff, {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_2__.Mic, {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: !message.trim() || disabled,\n                            className: \"p-3 rounded-lg bg-blue-600 text-white hover:bg-blue-700 disabled:bg-gray-300 dark:disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors\",\n                            title: \"Send message\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_2__.Send, {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, undefined),\n                isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2 flex items-center justify-center space-x-2 text-red-600 dark:text-red-400\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-red-600 dark:bg-red-400 rounded-full animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: \"Recording... (placeholder)\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInput);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/chat/ChatInput.tsx\n");

/***/ }),

/***/ "./src/components/chat/ChatInterface.tsx":
/*!***********************************************!*\
  !*** ./src/components/chat/ChatInterface.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/UserContext */ \"./src/context/UserContext.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\n/* harmony import */ var _lib_chatService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/chatService */ \"./src/lib/chatService.ts\");\n/* harmony import */ var _ChatHeader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ChatHeader */ \"./src/components/chat/ChatHeader.tsx\");\n/* harmony import */ var _MessageList__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./MessageList */ \"./src/components/chat/MessageList.tsx\");\n/* harmony import */ var _ChatInput__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ChatInput */ \"./src/components/chat/ChatInput.tsx\");\n/* harmony import */ var _ChatHistorySidebar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ChatHistorySidebar */ \"./src/components/chat/ChatHistorySidebar.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_3__, _ChatHeader__WEBPACK_IMPORTED_MODULE_5__, _MessageList__WEBPACK_IMPORTED_MODULE_6__, _ChatHistorySidebar__WEBPACK_IMPORTED_MODULE_8__]);\n([_lib_utils__WEBPACK_IMPORTED_MODULE_3__, _ChatHeader__WEBPACK_IMPORTED_MODULE_5__, _MessageList__WEBPACK_IMPORTED_MODULE_6__, _ChatHistorySidebar__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nconst ChatInterface = ({ onSettingsClick })=>{\n    const { authState, getClient } = (0,_context_UserContext__WEBPACK_IMPORTED_MODULE_2__.useUserContext)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentConversationId, setCurrentConversationId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load initial messages or conversation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // For now, start with an empty conversation\n        // Later this will load from conversation history\n        setMessages([]);\n    }, []);\n    const handleSendMessage = async (content)=>{\n        if (!authState.isAuthenticated) {\n            setError(\"Please log in to send messages\");\n            return;\n        }\n        const userMessage = {\n            id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.generateUUID)(),\n            role: \"user\",\n            content,\n            timestamp: new Date().toISOString()\n        };\n        // Add user message immediately\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setIsLoading(true);\n        setError(null);\n        try {\n            const client = await getClient();\n            if (!client) {\n                throw new Error(\"Failed to get authenticated client\");\n            }\n            const chatService = new _lib_chatService__WEBPACK_IMPORTED_MODULE_4__.ChatService(client);\n            // Create conversation if none exists\n            let conversationId = currentConversationId;\n            if (!conversationId) {\n                conversationId = await chatService.createConversation();\n                setCurrentConversationId(conversationId);\n            }\n            // Send message to R2R agent\n            const response = await chatService.sendMessage(content, conversationId, messages);\n            const assistantMessage = {\n                id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.generateUUID)(),\n                role: \"assistant\",\n                content: response.message,\n                timestamp: new Date().toISOString(),\n                metadata: {\n                    sources: response.sources,\n                    ...response.metadata\n                }\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            setError(error instanceof Error ? error.message : \"Failed to send message\");\n            // Remove the user message if there was an error\n            setMessages((prev)=>prev.filter((msg)=>msg.id !== userMessage.id));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleMessageEdit = (messageId, newContent)=>{\n        setMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                    ...msg,\n                    content: newContent\n                } : msg));\n    };\n    const handleMessageDelete = (messageId)=>{\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== messageId));\n    };\n    const handleConversationSelect = async (conversationId)=>{\n        try {\n            setIsLoading(true);\n            const client = await getClient();\n            if (!client) {\n                throw new Error(\"Failed to get authenticated client\");\n            }\n            const chatService = new _lib_chatService__WEBPACK_IMPORTED_MODULE_4__.ChatService(client);\n            const history = await chatService.getConversationHistory(conversationId);\n            setMessages(history);\n            setCurrentConversationId(conversationId);\n        } catch (error) {\n            console.error(\"Error loading conversation:\", error);\n            setError(\"Failed to load conversation\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleNewChat = ()=>{\n        setMessages([]);\n        setCurrentConversationId(null);\n        setSidebarOpen(false);\n    };\n    const toggleSidebar = ()=>{\n        setSidebarOpen(!sidebarOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex flex-col bg-white dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatHistorySidebar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: sidebarOpen,\n                onToggle: toggleSidebar,\n                onConversationSelect: handleConversationSelect,\n                onNewChat: handleNewChat,\n                selectedConversationId: currentConversationId\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `flex flex-col h-full relative transition-all duration-300 ease-in-out ${sidebarOpen ? \"lg:ml-80\" : \"\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed top-0 left-0 right-0 z-20 transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `transition-all duration-300 ease-in-out ${sidebarOpen ? \"lg:ml-80\" : \"\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatHeader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                onSettingsClick: onSettingsClick,\n                                onSidebarToggle: toggleSidebar,\n                                sidebarOpen: sidebarOpen\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 pt-16 pb-20 overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageList__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            messages: messages,\n                            isLoading: isLoading,\n                            onMessageEdit: handleMessageEdit,\n                            onMessageDelete: handleMessageDelete\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-0 left-0 right-0 z-20 transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `transition-all duration-300 ease-in-out ${sidebarOpen ? \"lg:ml-80\" : \"\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInput__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                onSendMessage: handleSendMessage,\n                                disabled: isLoading,\n                                placeholder: \"Type your message...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-20 left-1/2 transform -translate-x-1/2 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 text-red-600 dark:text-red-400 px-4 py-2 rounded-lg shadow-lg z-50\",\n                children: [\n                    error,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setError(null),\n                        className: \"ml-2 text-red-800 dark:text-red-200 hover:text-red-900 dark:hover:text-red-100\",\n                        children: \"\\xd7\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                lineNumber: 190,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInterface);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/chat/ChatInterface.tsx\n");

/***/ }),

/***/ "./src/components/chat/MessageBubble.tsx":
/*!***********************************************!*\
  !*** ./src/components/chat/MessageBubble.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Edit2_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Edit2,Trash2!=!lucide-react */ \"__barrel_optimize__?names=Check,Copy,Edit2,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-markdown */ \"react-markdown\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_markdown__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__]);\n([react_markdown__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst MessageBubble = ({ message, isLast = false, onEdit, onDelete })=>{\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editContent, setEditContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(message.content);\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isUser = message.role === \"user\";\n    const isAssistant = message.role === \"assistant\";\n    const handleCopy = async ()=>{\n        try {\n            await navigator.clipboard.writeText(message.content);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        } catch (error) {\n            console.error(\"Failed to copy message:\", error);\n        }\n    };\n    const handleEdit = ()=>{\n        if (onEdit && editContent.trim() !== message.content) {\n            onEdit(message.id, editContent.trim());\n        }\n        setIsEditing(false);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleEdit();\n        } else if (e.key === \"Escape\") {\n            setEditContent(message.content);\n            setIsEditing(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex ${isUser ? \"justify-end\" : \"justify-start\"} group`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `max-w-xs lg:max-w-md px-4 py-2 rounded-lg relative ${isUser ? \"bg-blue-600 text-white\" : \"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100\"}`,\n            children: [\n                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    value: editContent,\n                    onChange: (e)=>setEditContent(e.target.value),\n                    onKeyDown: handleKeyDown,\n                    onBlur: handleEdit,\n                    className: \"w-full bg-transparent border-none outline-none resize-none\",\n                    autoFocus: true,\n                    rows: Math.min(editContent.split(\"\\n\").length, 10)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"prose prose-sm max-w-none\",\n                    children: isAssistant ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        components: {\n                            p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-2 last:mb-0\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 40\n                                }, void 0),\n                            code: ({ children, className })=>{\n                                const isInline = !className;\n                                return isInline ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    className: \"bg-gray-200 dark:bg-gray-700 px-1 py-0.5 rounded text-sm\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 23\n                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"bg-gray-200 dark:bg-gray-700 p-2 rounded text-sm overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        children: children\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 25\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 23\n                                }, void 0);\n                            }\n                        },\n                        children: message.content\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 15\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"whitespace-pre-wrap\",\n                        children: message.content\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `text-xs mt-1 ${isUser ? \"text-blue-100\" : \"text-gray-500 dark:text-gray-400\"}`,\n                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatTimestamp)(message.timestamp)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `absolute top-1 ${isUser ? \"left-1\" : \"right-1\"} opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleCopy,\n                            className: `p-1 rounded ${isUser ? \"hover:bg-blue-700 text-blue-100\" : \"hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400\"} transition-colors`,\n                            title: \"Copy message\",\n                            children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit2_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Check, {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 23\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit2_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Copy, {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 55\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, undefined),\n                        isUser && onEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsEditing(true),\n                            className: \"p-1 rounded hover:bg-blue-700 text-blue-100 transition-colors\",\n                            title: \"Edit message\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit2_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Edit2, {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, undefined),\n                        onDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>onDelete(message.id),\n                            className: `p-1 rounded ${isUser ? \"hover:bg-red-600 text-blue-100\" : \"hover:bg-red-100 dark:hover:bg-red-900 text-gray-500 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400\"} transition-colors`,\n                            title: \"Delete message\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit2_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Trash2, {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MessageBubble);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/chat/MessageBubble.tsx\n");

/***/ }),

/***/ "./src/components/chat/MessageList.tsx":
/*!*********************************************!*\
  !*** ./src/components/chat/MessageList.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MessageBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MessageBubble */ \"./src/components/chat/MessageBubble.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_MessageBubble__WEBPACK_IMPORTED_MODULE_2__]);\n_MessageBubble__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst MessageList = ({ messages, isLoading = false, onMessageEdit, onMessageDelete })=>{\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (messagesEndRef.current) {\n            messagesEndRef.current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    }, [\n        messages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"flex-1 overflow-y-auto px-4 py-4 space-y-4 scrollbar-thin\",\n        style: {\n            height: \"calc(100vh - 8rem)\"\n        },\n        children: [\n            messages.length === 0 && !isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white font-bold text-xl\",\n                                children: \"R\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 dark:text-white mb-2\",\n                            children: \"Welcome to RChat\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400 max-w-md\",\n                            children: \"Start a conversation by typing a message below. I'm here to help you with any questions you might have.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    messages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            message: message,\n                            isLast: index === messages.length - 1,\n                            onEdit: onMessageEdit,\n                            onDelete: onMessageDelete\n                        }, message.id, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 13\n                        }, undefined)),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-gray-100 dark:bg-gray-800\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-gray-400 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-gray-400 rounded-full animate-pulse\",\n                                        style: {\n                                            animationDelay: \"0.1s\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-gray-400 rounded-full animate-pulse\",\n                                        style: {\n                                            animationDelay: \"0.2s\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: messagesEndRef\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MessageList);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/chat/MessageList.tsx\n");

/***/ }),

/***/ "./src/components/ui/avatar.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/avatar.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-avatar */ \"@radix-ui/react-avatar\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__]);\n([_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined));\nAvatar.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Root.displayName;\nconst AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Image, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"aspect-square h-full w-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 25,\n        columnNumber: 3\n    }, undefined));\nAvatarImage.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Image.displayName;\nconst AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Fallback, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex h-full w-full items-center justify-center rounded-full bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 37,\n        columnNumber: 3\n    }, undefined));\nAvatarFallback.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Fallback.displayName;\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy91aS9hdmF0YXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDMkI7QUFFekI7QUFFaEMsTUFBTUcsdUJBQVNILDZDQUFnQixDQUc3QixDQUFDLEVBQUVLLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUMxQiw4REFBQ04sd0RBQW9CO1FBQ25CTSxLQUFLQTtRQUNMRixXQUFXSCw4Q0FBRUEsQ0FDWCxpRUFDQUc7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHYkgsT0FBT00sV0FBVyxHQUFHUix3REFBb0IsQ0FBQ1EsV0FBVztBQUVyRCxNQUFNQyw0QkFBY1YsNkNBQWdCLENBR2xDLENBQUMsRUFBRUssU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDTix5REFBcUI7UUFDcEJNLEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUFDLCtCQUErQkc7UUFDNUMsR0FBR0MsS0FBSzs7Ozs7O0FBR2JJLFlBQVlELFdBQVcsR0FBR1IseURBQXFCLENBQUNRLFdBQVc7QUFFM0QsTUFBTUcsK0JBQWlCWiw2Q0FBZ0IsQ0FHckMsQ0FBQyxFQUFFSyxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNOLDREQUF3QjtRQUN2Qk0sS0FBS0E7UUFDTEYsV0FBV0gsOENBQUVBLENBQ1gsd0VBQ0FHO1FBRUQsR0FBR0MsS0FBSzs7Ozs7O0FBR2JNLGVBQWVILFdBQVcsR0FBR1IsNERBQXdCLENBQUNRLFdBQVc7QUFFbkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yY2hhdC8uL3NyYy9jb21wb25lbnRzL3VpL2F2YXRhci50c3g/MWQwMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0ICogYXMgQXZhdGFyUHJpbWl0aXZlIGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtYXZhdGFyXCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBBdmF0YXIgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBBdmF0YXJQcmltaXRpdmUuUm9vdD4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgQXZhdGFyUHJpbWl0aXZlLlJvb3Q+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxBdmF0YXJQcmltaXRpdmUuUm9vdFxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcInJlbGF0aXZlIGZsZXggaC0xMCB3LTEwIHNocmluay0wIG92ZXJmbG93LWhpZGRlbiByb3VuZGVkLWZ1bGxcIixcbiAgICAgIGNsYXNzTmFtZVxuICAgICl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkF2YXRhci5kaXNwbGF5TmFtZSA9IEF2YXRhclByaW1pdGl2ZS5Sb290LmRpc3BsYXlOYW1lXG5cbmNvbnN0IEF2YXRhckltYWdlID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgQXZhdGFyUHJpbWl0aXZlLkltYWdlPixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBBdmF0YXJQcmltaXRpdmUuSW1hZ2U+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxBdmF0YXJQcmltaXRpdmUuSW1hZ2VcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFwiYXNwZWN0LXNxdWFyZSBoLWZ1bGwgdy1mdWxsXCIsIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkF2YXRhckltYWdlLmRpc3BsYXlOYW1lID0gQXZhdGFyUHJpbWl0aXZlLkltYWdlLmRpc3BsYXlOYW1lXG5cbmNvbnN0IEF2YXRhckZhbGxiYWNrID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgQXZhdGFyUHJpbWl0aXZlLkZhbGxiYWNrPixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBBdmF0YXJQcmltaXRpdmUuRmFsbGJhY2s+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxBdmF0YXJQcmltaXRpdmUuRmFsbGJhY2tcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgXCJmbGV4IGgtZnVsbCB3LWZ1bGwgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHJvdW5kZWQtZnVsbCBiZy1tdXRlZFwiLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQXZhdGFyRmFsbGJhY2suZGlzcGxheU5hbWUgPSBBdmF0YXJQcmltaXRpdmUuRmFsbGJhY2suZGlzcGxheU5hbWVcblxuZXhwb3J0IHsgQXZhdGFyLCBBdmF0YXJJbWFnZSwgQXZhdGFyRmFsbGJhY2sgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQXZhdGFyUHJpbWl0aXZlIiwiY24iLCJBdmF0YXIiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJSb290IiwiZGlzcGxheU5hbWUiLCJBdmF0YXJJbWFnZSIsIkltYWdlIiwiQXZhdGFyRmFsbGJhY2siLCJGYWxsYmFjayJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/ui/avatar.tsx\n");

/***/ }),

/***/ "./src/components/ui/dropdown-menu.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/dropdown-menu.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuShortcut: () => (/* binding */ DropdownMenuShortcut),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"@radix-ui/react-dropdown-menu\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"__barrel_optimize__?names=Check,ChevronRight,Circle!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__]);\n([_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst DropdownMenu = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Root;\nconst DropdownMenuTrigger = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Trigger;\nconst DropdownMenuGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Group;\nconst DropdownMenuPortal = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Portal;\nconst DropdownMenuSub = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Sub;\nconst DropdownMenuRadioGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.RadioGroup;\nconst DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.SubTrigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\", inset && \"pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__.ChevronRight, {\n                className: \"ml-auto h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 35,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 25,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubTrigger.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.SubTrigger.displayName;\nconst DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.SubContent, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 45,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.SubContent.displayName;\nconst DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n            lineNumber: 62,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 61,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Content.displayName;\nconst DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 81,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Item.displayName;\nconst DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, checked, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.CheckboxItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Check, {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 106,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 97,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuCheckboxItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.CheckboxItem.displayName;\nconst DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.RadioItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Circle, {\n                        className: \"h-2 w-2 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 129,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 121,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuRadioItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.RadioItem.displayName;\nconst DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 145,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuLabel.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Label.displayName;\nconst DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 161,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSeparator.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Separator.displayName;\nconst DropdownMenuShortcut = ({ className, ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"ml-auto text-xs tracking-widest opacity-60\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, undefined);\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/dropdown-menu.tsx\n");

/***/ }),

/***/ "./src/config/chatConfig.ts":
/*!**********************************!*\
  !*** ./src/config/chatConfig.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultChatConfig: () => (/* binding */ defaultChatConfig),\n/* harmony export */   getDeploymentUrl: () => (/* binding */ getDeploymentUrl),\n/* harmony export */   loadChatConfig: () => (/* binding */ loadChatConfig),\n/* harmony export */   loadPublicConfig: () => (/* binding */ loadPublicConfig),\n/* harmony export */   saveChatConfig: () => (/* binding */ saveChatConfig)\n/* harmony export */ });\n/**\n * Chat Configuration for RChat Frontend\n *\n * This file contains all configuration options for the chat application,\n * including search settings, server connection, and UI preferences.\n */ /**\n * Default configuration values\n */ const defaultChatConfig = {\n    server: {\n        apiUrl: \"http://localhost:7272\",\n        port: 7272,\n        useHttps: false,\n        apiVersion: \"v3\",\n        timeout: 30000\n    },\n    app: {\n        appName: \"RChat\",\n        appDescription: \"R2R-powered chat application\",\n        version: \"1.0.0\",\n        defaultMode: \"rag_agent\"\n    },\n    vectorSearch: {\n        enabled: true,\n        searchLimit: 10,\n        searchFilters: \"{}\",\n        indexMeasure: \"cosine_distance\",\n        includeMetadatas: false,\n        probes: undefined,\n        efSearch: undefined\n    },\n    hybridSearch: {\n        enabled: false,\n        fullTextWeight: undefined,\n        semanticWeight: undefined,\n        fullTextLimit: undefined,\n        rrfK: undefined\n    },\n    graphSearch: {\n        enabled: true,\n        kgSearchLevel: null,\n        maxCommunityDescriptionLength: 100,\n        localSearchLimits: {},\n        maxLlmQueries: undefined\n    },\n    ragGeneration: {\n        temperature: 0.1,\n        topP: 1,\n        topK: 100,\n        maxTokensToSample: 1024,\n        kgTemperature: 0.1,\n        kgTopP: 1,\n        kgTopK: 100,\n        kgMaxTokensToSample: 1024\n    }\n};\n/**\n * Load configuration from localStorage with fallback to defaults\n */ function loadChatConfig() {\n    if (true) {\n        return defaultChatConfig;\n    }\n    try {\n        const stored = localStorage.getItem(\"rchatConfig\");\n        if (stored) {\n            const parsed = JSON.parse(stored);\n            // Merge with defaults to ensure all properties exist\n            return {\n                ...defaultChatConfig,\n                ...parsed,\n                server: {\n                    ...defaultChatConfig.server,\n                    ...parsed.server\n                },\n                app: {\n                    ...defaultChatConfig.app,\n                    ...parsed.app\n                },\n                vectorSearch: {\n                    ...defaultChatConfig.vectorSearch,\n                    ...parsed.vectorSearch\n                },\n                hybridSearch: {\n                    ...defaultChatConfig.hybridSearch,\n                    ...parsed.hybridSearch\n                },\n                graphSearch: {\n                    ...defaultChatConfig.graphSearch,\n                    ...parsed.graphSearch\n                },\n                ragGeneration: {\n                    ...defaultChatConfig.ragGeneration,\n                    ...parsed.ragGeneration\n                }\n            };\n        }\n    } catch (error) {\n        console.warn(\"Failed to load chat config from localStorage:\", error);\n    }\n    return defaultChatConfig;\n}\n/**\n * Save configuration to localStorage\n */ function saveChatConfig(config) {\n    if (true) {\n        return;\n    }\n    try {\n        localStorage.setItem(\"rchatConfig\", JSON.stringify(config));\n    } catch (error) {\n        console.error(\"Failed to save chat config to localStorage:\", error);\n    }\n}\n/**\n * Load configuration from public config.json file\n */ async function loadPublicConfig() {\n    if (true) {\n        return null;\n    }\n    try {\n        const response = await fetch(\"/config.json\");\n        if (!response.ok) {\n            console.warn(\"Could not load public config.json\");\n            return null;\n        }\n        const config = await response.json();\n        return {\n            server: {\n                apiUrl: config.apiUrl,\n                useHttps: config.useHttps,\n                timeout: config.timeout\n            },\n            app: {\n                appName: config.appName,\n                appDescription: config.appDescription,\n                version: config.version,\n                defaultMode: \"rag_agent\"\n            }\n        };\n    } catch (error) {\n        console.warn(\"Error loading public config.json:\", error);\n        return null;\n    }\n}\n/**\n * Get the deployment URL for the API\n */ function getDeploymentUrl(config) {\n    const chatConfig = config || loadChatConfig();\n    // Check for runtime config first (for Docker deployments)\n    if (false) {}\n    return chatConfig.server.apiUrl;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/config/chatConfig.ts\n");

/***/ }),

/***/ "./src/context/UserContext.tsx":
/*!*************************************!*\
  !*** ./src/context/UserContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: () => (/* binding */ UserProvider),\n/* harmony export */   useUserContext: () => (/* binding */ useUserContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var r2r_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! r2r-js */ \"r2r-js\");\n/* harmony import */ var r2r_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(r2r_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _config_chatConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../config/chatConfig */ \"./src/config/chatConfig.ts\");\n\n\n\n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst UserProvider = ({ children })=>{\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isAuthenticated: false,\n        user: null,\n        token: null\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [client, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Initialize client and check for existing session\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            try {\n                const deploymentUrl = (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_3__.getDeploymentUrl)();\n                const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(deploymentUrl);\n                setClient(newClient);\n                // Check for stored token (try both rchat and chatfrontend token keys)\n                const storedToken = localStorage.getItem(\"chatAccessToken\") || localStorage.getItem(\"r2r_token\");\n                const storedRefreshToken = localStorage.getItem(\"chatRefreshToken\");\n                const storedUser = localStorage.getItem(\"r2r_user\");\n                if (storedToken && storedUser) {\n                    try {\n                        const user = JSON.parse(storedUser);\n                        // Set up client with stored tokens\n                        if (storedRefreshToken) {\n                            newClient.setTokens(storedToken, storedRefreshToken);\n                        }\n                        setAuthState({\n                            isAuthenticated: true,\n                            user,\n                            token: storedToken\n                        });\n                    } catch (error) {\n                        console.error(\"Error parsing stored user data:\", error);\n                        localStorage.removeItem(\"r2r_token\");\n                        localStorage.removeItem(\"r2r_user\");\n                        localStorage.removeItem(\"chatAccessToken\");\n                        localStorage.removeItem(\"chatRefreshToken\");\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeAuth();\n    }, []);\n    const login = async (email, password, deploymentUrl)=>{\n        try {\n            const apiUrl = deploymentUrl || (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_3__.getDeploymentUrl)();\n            const loginClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(apiUrl);\n            const tokens = await loginClient.users.login({\n                email: email,\n                password: password\n            });\n            if (!tokens.results) {\n                throw new Error(\"Login failed: No results returned\");\n            }\n            // Store tokens like chatfrontend does\n            localStorage.setItem(\"chatAccessToken\", tokens.results.accessToken.token);\n            localStorage.setItem(\"chatRefreshToken\", tokens.results.refreshToken.token);\n            // Set tokens on client\n            loginClient.setTokens(tokens.results.accessToken.token, tokens.results.refreshToken.token);\n            setClient(loginClient);\n            // Get user info\n            const userInfo = await loginClient.users.me();\n            if (!userInfo.results) {\n                throw new Error(\"Failed to get user information\");\n            }\n            // Check user role like chatfrontend does\n            let userRole = \"user\";\n            try {\n                await loginClient.system.settings();\n                userRole = \"admin\";\n            } catch (error) {\n                if (error instanceof Error && \"status\" in error && error.status === 403) {\n                // User doesn't have admin access, keep as \"user\"\n                } else {\n                    console.error(\"Unexpected error when checking user role:\", error);\n                }\n            }\n            const user = {\n                id: userInfo.results.id,\n                email: userInfo.results.email || email,\n                name: userInfo.results.name,\n                role: userRole\n            };\n            // Store auth data\n            localStorage.setItem(\"r2r_token\", tokens.results.accessToken.token);\n            localStorage.setItem(\"r2r_user\", JSON.stringify(user));\n            // Update state\n            setAuthState({\n                isAuthenticated: true,\n                user,\n                token: tokens.results.accessToken.token\n            });\n            return user;\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            // Provide more detailed error information\n            if (error instanceof Error) {\n                if (error.message.includes(\"401\") || error.message.includes(\"Unauthorized\")) {\n                    throw new Error(\"Invalid email or password. Please check your credentials.\");\n                } else if (error.message.includes(\"404\") || error.message.includes(\"Not Found\")) {\n                    throw new Error(\"User not found. Please check your email address.\");\n                } else if (error.message.includes(\"500\") || error.message.includes(\"Internal Server Error\")) {\n                    throw new Error(\"Server error. Please try again later.\");\n                } else if (error.message.includes(\"Network Error\") || error.message.includes(\"fetch\")) {\n                    throw new Error(\"Cannot connect to server. Please check if R2R backend is running.\");\n                }\n            }\n            throw error;\n        }\n    };\n    const logout = ()=>{\n        localStorage.removeItem(\"r2r_token\");\n        localStorage.removeItem(\"r2r_user\");\n        localStorage.removeItem(\"chatAccessToken\");\n        localStorage.removeItem(\"chatRefreshToken\");\n        setAuthState({\n            isAuthenticated: false,\n            user: null,\n            token: null\n        });\n        setClient(null);\n    };\n    const getClient = async ()=>{\n        if (!authState.isAuthenticated || !authState.token) {\n            return null;\n        }\n        if (!client) {\n            const deploymentUrl = (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_3__.getDeploymentUrl)();\n            const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(deploymentUrl);\n            // Set tokens if available\n            const accessToken = localStorage.getItem(\"chatAccessToken\") || localStorage.getItem(\"r2r_token\");\n            const refreshToken = localStorage.getItem(\"chatRefreshToken\");\n            if (accessToken) {\n                newClient.setTokens(accessToken, refreshToken || \"\");\n            }\n            setClient(newClient);\n            return newClient;\n        }\n        return client;\n    };\n    const value = {\n        authState,\n        login,\n        logout,\n        getClient,\n        isLoading\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\context\\\\UserContext.tsx\",\n        lineNumber: 204,\n        columnNumber: 10\n    }, undefined);\n};\nconst useUserContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (context === undefined) {\n        throw new Error(\"useUserContext must be used within a UserProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/context/UserContext.tsx\n");

/***/ }),

/***/ "./src/hooks/useConversationHistory.ts":
/*!*********************************************!*\
  !*** ./src/hooks/useConversationHistory.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useConversationHistory: () => (/* binding */ useConversationHistory)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/context/UserContext */ \"./src/context/UserContext.tsx\");\n/* harmony import */ var _lib_chatService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/chatService */ \"./src/lib/chatService.ts\");\n\n\n\nconst useConversationHistory = ()=>{\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const { getClient, authState } = (0,_context_UserContext__WEBPACK_IMPORTED_MODULE_1__.useUserContext)();\n    const fetchConversations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        if (!authState.isAuthenticated) {\n            setConversations([]);\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        try {\n            const client = await getClient();\n            if (!client) {\n                throw new Error(\"Failed to get authenticated client\");\n            }\n            const chatService = new _lib_chatService__WEBPACK_IMPORTED_MODULE_2__.ChatService(client);\n            const response = await chatService.listConversations(0, 100);\n            // Convert R2R conversation format to our format\n            // Note: R2R SDK uses camelCase (createdAt) while our types use snake_case (created_at)\n            const formattedConversations = response.map((conv)=>({\n                    id: conv.id,\n                    name: conv.name || `Conversation ${new Date(conv.createdAt || conv.created_at).toLocaleDateString()}`,\n                    messages: [],\n                    created_at: conv.createdAt || conv.created_at,\n                    updated_at: conv.updatedAt || conv.updated_at,\n                    metadata: conv.metadata || {}\n                }));\n            setConversations(formattedConversations);\n        } catch (err) {\n            console.error(\"Error fetching conversations:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to fetch conversations\");\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        getClient,\n        authState.isAuthenticated\n    ]);\n    const createNewConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (name)=>{\n        if (!authState.isAuthenticated) {\n            setError(\"Please log in to create conversations\");\n            return null;\n        }\n        try {\n            const client = await getClient();\n            if (!client) {\n                throw new Error(\"Failed to get authenticated client\");\n            }\n            const chatService = new _lib_chatService__WEBPACK_IMPORTED_MODULE_2__.ChatService(client);\n            const conversationId = await chatService.createConversation(name);\n            // Refresh conversations list\n            await fetchConversations();\n            return conversationId;\n        } catch (err) {\n            console.error(\"Error creating conversation:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to create conversation\");\n            return null;\n        }\n    }, [\n        getClient,\n        authState.isAuthenticated,\n        fetchConversations\n    ]);\n    const loadConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (conversationId)=>{\n        if (!authState.isAuthenticated) {\n            setError(\"Please log in to load conversations\");\n            return [];\n        }\n        try {\n            const client = await getClient();\n            if (!client) {\n                throw new Error(\"Failed to get authenticated client\");\n            }\n            const chatService = new _lib_chatService__WEBPACK_IMPORTED_MODULE_2__.ChatService(client);\n            const messages = await chatService.getConversationHistory(conversationId);\n            return messages;\n        } catch (err) {\n            console.error(\"Error loading conversation:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to load conversation\");\n            return [];\n        }\n    }, [\n        getClient,\n        authState.isAuthenticated\n    ]);\n    const deleteConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (conversationId)=>{\n        if (!authState.isAuthenticated) {\n            setError(\"Please log in to delete conversations\");\n            return;\n        }\n        try {\n            const client = await getClient();\n            if (!client) {\n                throw new Error(\"Failed to get authenticated client\");\n            }\n            const chatService = new _lib_chatService__WEBPACK_IMPORTED_MODULE_2__.ChatService(client);\n            await chatService.deleteConversation(conversationId);\n            // Remove from local state\n            setConversations((prev)=>prev.filter((conv)=>conv.id !== conversationId));\n        } catch (err) {\n            console.error(\"Error deleting conversation:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to delete conversation\");\n        }\n    }, [\n        getClient,\n        authState.isAuthenticated\n    ]);\n    const refreshConversations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        await fetchConversations();\n    }, [\n        fetchConversations\n    ]);\n    // Load conversations when user authenticates\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (authState.isAuthenticated) {\n            fetchConversations();\n        } else {\n            setConversations([]);\n            setError(null);\n        }\n    }, [\n        authState.isAuthenticated,\n        fetchConversations\n    ]);\n    return {\n        conversations,\n        isLoading,\n        error,\n        fetchConversations,\n        createNewConversation,\n        loadConversation,\n        deleteConversation,\n        refreshConversations\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useConversationHistory.ts\n");

/***/ }),

/***/ "./src/lib/chatService.ts":
/*!********************************!*\
  !*** ./src/lib/chatService.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatService: () => (/* binding */ ChatService)\n/* harmony export */ });\n/* harmony import */ var _config_chatConfig__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/chatConfig */ \"./src/config/chatConfig.ts\");\n\nclass ChatService {\n    constructor(client){\n        this.client = client;\n    }\n    /**\n   * Send a message to the R2R agent and get a response\n   */ async sendMessage(message, conversationId, messageHistory) {\n        try {\n            const config = (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_0__.loadChatConfig)();\n            // Prepare the search settings based on configuration\n            const chunkSearchSettings = {\n                indexMeasure: config.vectorSearch.indexMeasure,\n                enabled: config.vectorSearch.enabled,\n                ...config.vectorSearch.probes && {\n                    probes: config.vectorSearch.probes\n                },\n                ...config.vectorSearch.efSearch && {\n                    efSearch: config.vectorSearch.efSearch\n                }\n            };\n            const hybridSearchSettings = {\n                ...config.hybridSearch.fullTextWeight && {\n                    fulltextWeight: config.hybridSearch.fullTextWeight\n                },\n                ...config.hybridSearch.semanticWeight && {\n                    semanticWeight: config.hybridSearch.semanticWeight\n                },\n                ...config.hybridSearch.fullTextLimit && {\n                    fulltextLimit: config.hybridSearch.fullTextLimit\n                },\n                ...config.hybridSearch.rrfK && {\n                    rrfK: config.hybridSearch.rrfK\n                }\n            };\n            const graphSearchSettings = {\n                enabled: config.graphSearch.enabled,\n                ...config.graphSearch.maxCommunityDescriptionLength && {\n                    maxCommunityDescriptionLength: config.graphSearch.maxCommunityDescriptionLength\n                },\n                ...config.graphSearch.maxLlmQueries && {\n                    maxLlmQueriesForGlobalSearch: config.graphSearch.maxLlmQueries\n                },\n                ...config.graphSearch.localSearchLimits && {\n                    limits: config.graphSearch.localSearchLimits\n                }\n            };\n            const searchSettings = {\n                useSemanticSearch: config.vectorSearch.enabled,\n                useHybridSearch: config.hybridSearch.enabled,\n                useFulltextSearch: config.hybridSearch.enabled,\n                filters: config.vectorSearch.searchFilters ? JSON.parse(config.vectorSearch.searchFilters) : {},\n                limit: config.vectorSearch.searchLimit,\n                includeMetadata: config.vectorSearch.includeMetadatas,\n                chunkSettings: chunkSearchSettings,\n                hybridSettings: hybridSearchSettings,\n                graphSettings: graphSearchSettings\n            };\n            // Prepare generation settings\n            const ragGenerationConfig = {\n                stream: true,\n                temperature: config.ragGeneration.temperature,\n                topP: config.ragGeneration.topP,\n                maxTokensToSample: config.ragGeneration.maxTokensToSample\n            };\n            // Create the user message in R2R format\n            const userMessage = {\n                role: \"user\",\n                content: message\n            };\n            // Use agent mode based on configuration\n            if (config.app.defaultMode === \"rag_agent\") {\n                const streamResponse = await this.client.retrieval.agent({\n                    message: userMessage,\n                    ragGenerationConfig,\n                    searchSettings,\n                    conversationId\n                });\n                // Handle streaming response\n                return await this.processStreamResponse(streamResponse);\n            } else {\n                const streamResponse = await this.client.retrieval.rag({\n                    query: message,\n                    ragGenerationConfig,\n                    searchSettings\n                });\n                // Handle streaming response\n                return await this.processStreamResponse(streamResponse);\n            }\n        } catch (error) {\n            console.error(\"Error in chat service:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Process streaming response from R2R\n   */ async processStreamResponse(streamResponse) {\n        try {\n            const reader = streamResponse.getReader();\n            const decoder = new TextDecoder();\n            let buffer = \"\";\n            let fullContent = \"\";\n            let sources = [];\n            let metadata = {};\n            // Process the stream\n            while(true){\n                const { done, value } = await reader.read();\n                if (done) {\n                    break;\n                }\n                buffer += decoder.decode(value, {\n                    stream: true\n                });\n                // Process complete SSE events from the buffer\n                const events = buffer.split(\"\\n\\n\");\n                buffer = events.pop() || \"\"; // Keep the last potentially incomplete event in the buffer\n                for (const event of events){\n                    if (!event.trim()) {\n                        continue;\n                    }\n                    const lines = event.split(\"\\n\");\n                    const eventType = lines[0].startsWith(\"event: \") ? lines[0].slice(7) : \"\";\n                    const dataLine = lines.find((line)=>line.startsWith(\"data: \"));\n                    if (!dataLine) {\n                        continue;\n                    }\n                    const jsonStr = dataLine.slice(6);\n                    // Skip the [DONE] marker that indicates end of stream\n                    if (jsonStr.trim() === \"[DONE]\") {\n                        continue;\n                    }\n                    // Skip empty or invalid JSON strings\n                    if (!jsonStr.trim()) {\n                        continue;\n                    }\n                    try {\n                        const eventData = JSON.parse(jsonStr);\n                        if (eventType === \"search_results\") {\n                            // Handle search results\n                            if (eventData.search_results) {\n                                sources = eventData.search_results.chunk_search_results || [];\n                            }\n                        } else if (eventType === \"message\") {\n                            // Handle incremental content delta\n                            if (eventData.delta && eventData.delta.content) {\n                                const contentItems = eventData.delta.content;\n                                for (const item of contentItems){\n                                    if (item.type === \"text\" && item.payload && item.payload.value) {\n                                        fullContent += item.payload.value;\n                                    }\n                                }\n                            }\n                        }\n                    } catch (err) {\n                        // Only log errors for non-empty, non-DONE content\n                        if (jsonStr.trim() && jsonStr.trim() !== \"[DONE]\") {\n                            console.error(\"Error parsing SSE event data:\", err, jsonStr);\n                        }\n                    }\n                }\n            }\n            return {\n                message: fullContent || \"No response generated\",\n                sources,\n                metadata\n            };\n        } catch (error) {\n            console.error(\"Error processing stream response:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create a new conversation\n   */ async createConversation(name) {\n        try {\n            const response = await this.client.conversations.create(name ? {\n                name\n            } : undefined);\n            if (response.results?.id) {\n                return response.results.id;\n            } else {\n                throw new Error(\"Failed to create conversation\");\n            }\n        } catch (error) {\n            console.error(\"Error creating conversation:\", error);\n            throw error; // Don't return mock ID, let the error bubble up\n        }\n    }\n    /**\n   * Get conversation history\n   */ async getConversationHistory(conversationId) {\n        try {\n            const response = await this.client.conversations.retrieve({\n                id: conversationId\n            });\n            if (response.results) {\n                return response.results.map((msg)=>({\n                        id: msg.id || `msg-${Date.now()}-${Math.random()}`,\n                        role: msg.role || \"user\",\n                        content: msg.content || \"\",\n                        timestamp: msg.createdAt || msg.created_at || new Date().toISOString(),\n                        metadata: msg.metadata || {}\n                    }));\n            }\n            return [];\n        } catch (error) {\n            console.error(\"Error getting conversation history:\", error);\n            return [];\n        }\n    }\n    /**\n   * List conversations\n   */ async listConversations(offset = 0, limit = 100) {\n        try {\n            const response = await this.client.conversations.list({\n                offset,\n                limit\n            });\n            // The response structure is: { results: ConversationResponse[], total_entries: number }\n            return response.results || [];\n        } catch (error) {\n            console.error(\"Error listing conversations:\", error);\n            return [];\n        }\n    }\n    /**\n   * Delete a conversation\n   */ async deleteConversation(conversationId) {\n        try {\n            await this.client.conversations.delete({\n                id: conversationId\n            });\n        } catch (error) {\n            console.error(\"Error deleting conversation:\", error);\n            // Don't throw error in development mode\n            console.warn(\"Conversation deletion failed, continuing...\");\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/chatService.ts\n");

/***/ }),

/***/ "./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatTimestamp: () => (/* binding */ formatTimestamp),\n/* harmony export */   generateUUID: () => (/* binding */ generateUUID),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format timestamp to readable string\n */ function formatTimestamp(timestamp) {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffMins = Math.floor(diffMs / 60000);\n    const diffHours = Math.floor(diffMs / 3600000);\n    const diffDays = Math.floor(diffMs / 86400000);\n    if (diffMins < 1) {\n        return \"Just now\";\n    } else if (diffMins < 60) {\n        return `${diffMins}m ago`;\n    } else if (diffHours < 24) {\n        return `${diffHours}h ago`;\n    } else if (diffDays < 7) {\n        return `${diffDays}d ago`;\n    } else {\n        return date.toLocaleDateString();\n    }\n}\n/**\n * Truncate text to specified length\n */ function truncateText(text, maxLength = 50) {\n    if (text.length <= maxLength) {\n        return text;\n    }\n    return text.substring(0, maxLength) + \"...\";\n}\n/**\n * Generate a random UUID v4\n */ function generateUUID() {\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function(c) {\n        const r = Math.random() * 16 | 0;\n        const v = c === \"x\" ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n    });\n}\n/**\n * Debounce function\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Check if running in browser environment\n */ function isBrowser() {\n    return \"undefined\" !== \"undefined\";\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/utils.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/UserContext */ \"./src/context/UserContext.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n        attribute: \"class\",\n        defaultTheme: \"system\",\n        enableSystem: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_UserContext__WEBPACK_IMPORTED_MODULE_2__.UserProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 10,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQzRDO0FBQ1M7QUFDdkI7QUFFZixTQUFTRSxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFZO0lBQzVELHFCQUNFLDhEQUFDSixzREFBYUE7UUFBQ0ssV0FBVTtRQUFRQyxjQUFhO1FBQVNDLFlBQVk7a0JBQ2pFLDRFQUFDTiw4REFBWUE7c0JBQ1gsNEVBQUNFO2dCQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJaEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yY2hhdC8uL3NyYy9wYWdlcy9fYXBwLnRzeD9mOWQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCc7XG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIH0gZnJvbSAnbmV4dC10aGVtZXMnO1xuaW1wb3J0IHsgVXNlclByb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0L1VzZXJDb250ZXh0JztcbmltcG9ydCAnQC9zdHlsZXMvZ2xvYmFscy5jc3MnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxUaGVtZVByb3ZpZGVyIGF0dHJpYnV0ZT1cImNsYXNzXCIgZGVmYXVsdFRoZW1lPVwic3lzdGVtXCIgZW5hYmxlU3lzdGVtPlxuICAgICAgPFVzZXJQcm92aWRlcj5cbiAgICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgICAgPC9Vc2VyUHJvdmlkZXI+XG4gICAgPC9UaGVtZVByb3ZpZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlRoZW1lUHJvdmlkZXIiLCJVc2VyUHJvdmlkZXIiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJhdHRyaWJ1dGUiLCJkZWZhdWx0VGhlbWUiLCJlbmFibGVTeXN0ZW0iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/_document.tsx":
/*!*********************************!*\
  !*** ./src/pages/_document.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 7,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"RChat - R2R-powered chat application\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2RvY3VtZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNkQ7QUFFOUMsU0FBU0k7SUFDdEIscUJBQ0UsOERBQUNKLCtDQUFJQTtRQUFDSyxNQUFLOzswQkFDVCw4REFBQ0osK0NBQUlBOztrQ0FDSCw4REFBQ0s7d0JBQUtDLFNBQVE7Ozs7OztrQ0FDZCw4REFBQ0Q7d0JBQUtFLE1BQUs7d0JBQWNDLFNBQVE7Ozs7OztrQ0FDakMsOERBQUNIO3dCQUFLRSxNQUFLO3dCQUFXQyxTQUFROzs7Ozs7a0NBQzlCLDhEQUFDQzt3QkFBS0MsS0FBSTt3QkFBT0MsTUFBSzs7Ozs7Ozs7Ozs7OzBCQUV4Qiw4REFBQ0M7O2tDQUNDLDhEQUFDWCwrQ0FBSUE7Ozs7O2tDQUNMLDhEQUFDQyxxREFBVUE7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSW5CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmNoYXQvLi9zcmMvcGFnZXMvX2RvY3VtZW50LnRzeD8xODhlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEh0bWwsIEhlYWQsIE1haW4sIE5leHRTY3JpcHQgfSBmcm9tICduZXh0L2RvY3VtZW50JztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRG9jdW1lbnQoKSB7XG4gIHJldHVybiAoXG4gICAgPEh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8SGVhZD5cbiAgICAgICAgPG1ldGEgY2hhclNldD1cInV0Zi04XCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImRlc2NyaXB0aW9uXCIgY29udGVudD1cIlJDaGF0IC0gUjJSLXBvd2VyZWQgY2hhdCBhcHBsaWNhdGlvblwiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJ2aWV3cG9ydFwiIGNvbnRlbnQ9XCJ3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MVwiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cImljb25cIiBocmVmPVwiL2Zhdmljb24uaWNvXCIgLz5cbiAgICAgIDwvSGVhZD5cbiAgICAgIDxib2R5PlxuICAgICAgICA8TWFpbiAvPlxuICAgICAgICA8TmV4dFNjcmlwdCAvPlxuICAgICAgPC9ib2R5PlxuICAgIDwvSHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJIdG1sIiwiSGVhZCIsIk1haW4iLCJOZXh0U2NyaXB0IiwiRG9jdW1lbnQiLCJsYW5nIiwibWV0YSIsImNoYXJTZXQiLCJuYW1lIiwiY29udGVudCIsImxpbmsiLCJyZWwiLCJocmVmIiwiYm9keSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/_document.tsx\n");

/***/ }),

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/context/UserContext */ \"./src/context/UserContext.tsx\");\n/* harmony import */ var _components_chat_ChatInterface__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/chat/ChatInterface */ \"./src/components/chat/ChatInterface.tsx\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_chat_ChatInterface__WEBPACK_IMPORTED_MODULE_4__]);\n_components_chat_ChatInterface__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst HomePage = ()=>{\n    const { authState, isLoading } = (0,_context_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUserContext)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && !authState.isAuthenticated) {\n            router.push(\"/auth/login\");\n        }\n    }, [\n        authState.isAuthenticated,\n        isLoading,\n        router\n    ]);\n    const handleSettingsClick = ()=>{\n        setShowSettings(true);\n        // Settings modal/page to be implemented later\n        console.log(\"Settings clicked\");\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex items-center justify-center bg-white dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-white font-bold text-xl\",\n                            children: \"R\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-medium text-gray-900 dark:text-white mb-2\",\n                        children: \"Loading RChat...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: \"Please wait while we initialize your session\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\index.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!authState.isAuthenticated) {\n        return null; // Will redirect to login\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_5___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"RChat - R2R-powered Chat\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Chat with your R2R-powered AI assistant\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatInterface__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                onSettingsClick: handleSettingsClick\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\rchat\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomePage);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next-themes":
/*!******************************!*\
  !*** external "next-themes" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-themes");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "r2r-js":
/*!*************************!*\
  !*** external "r2r-js" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("r2r-js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "@radix-ui/react-avatar":
/*!*****************************************!*\
  !*** external "@radix-ui/react-avatar" ***!
  \*****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-avatar");;

/***/ }),

/***/ "@radix-ui/react-dropdown-menu":
/*!************************************************!*\
  !*** external "@radix-ui/react-dropdown-menu" ***!
  \************************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-dropdown-menu");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "react-markdown":
/*!*********************************!*\
  !*** external "react-markdown" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-markdown");;

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();