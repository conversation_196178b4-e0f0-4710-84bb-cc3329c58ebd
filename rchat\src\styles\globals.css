@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar styles */
.scrollbar-hidden {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hidden::-webkit-scrollbar {
  display: none;
}

/* Chat-specific styles */
.chat-container {
  height: calc(100vh - 4rem);
}

.messages-container {
  height: calc(100vh - 12rem);
}

/* Glass effect for modern UI */
.glass-effect {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Animation for loading states */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Focus styles for accessibility */
.focus-visible:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Message bubble styles */
.message-user {
  @apply bg-blue-600 text-white ml-auto;
}

.message-assistant {
  @apply bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 mr-auto;
}

/* Voice button styles */
.voice-button {
  @apply p-2 rounded-full transition-colors duration-200;
  @apply text-gray-600 dark:text-gray-300;
  @apply hover:text-gray-800 dark:hover:text-gray-100;
  @apply hover:bg-gray-100 dark:hover:bg-gray-700;
}

.voice-button.recording {
  @apply text-red-600 dark:text-red-400;
  @apply bg-red-50 dark:bg-red-900/20;
}

/* Chat history sidebar styles */
.chat-history-sidebar {
  @apply fixed left-0 top-0 h-full w-80 bg-white dark:bg-gray-900;
  @apply border-r border-gray-200 dark:border-gray-700;
  @apply transform transition-transform duration-300 ease-in-out;
  @apply z-40;
}

.chat-history-sidebar.open {
  @apply translate-x-0;
}

.chat-history-sidebar.closed {
  @apply -translate-x-full;
}

/* Chat item styles */
.chat-item {
  @apply p-3 rounded-lg cursor-pointer transition-colors duration-200;
  @apply hover:bg-gray-100 dark:hover:bg-gray-800;
}

.chat-item.selected {
  @apply bg-blue-50 dark:bg-blue-900/20;
  @apply border-blue-200 dark:border-blue-700;
}
