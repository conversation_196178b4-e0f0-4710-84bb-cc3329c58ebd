import { r2rClient, GenerationConfig, SearchSettings, ChunkSearchSettings, GraphSearchSettings, HybridSearchSettings, IndexMeasure } from 'r2r-js';
import { Message } from '@/types';
import { loadChatConfig } from '@/config/chatConfig';

export interface ChatResponse {
  message: string;
  sources?: any[];
  metadata?: Record<string, any>;
}

export class ChatService {
  private client: r2rClient;

  constructor(client: r2rClient) {
    this.client = client;
  }

  /**
   * Send a message to the R2R agent and get a response
   */
  async sendMessage(
    message: string,
    conversationId?: string,
    messageHistory?: Message[]
  ): Promise<ChatResponse> {
    try {
      const config = loadChatConfig();

      // Prepare the search settings based on configuration
      const chunkSearchSettings: ChunkSearchSettings = {
        indexMeasure: config.vectorSearch.indexMeasure as IndexMeasure,
        enabled: config.vectorSearch.enabled,
        ...(config.vectorSearch.probes && { probes: config.vectorSearch.probes }),
        ...(config.vectorSearch.efSearch && { efSearch: config.vectorSearch.efSearch }),
      };

      const hybridSearchSettings: HybridSearchSettings = {
        ...(config.hybridSearch.fullTextWeight && { fulltextWeight: config.hybridSearch.fullTextWeight }),
        ...(config.hybridSearch.semanticWeight && { semanticWeight: config.hybridSearch.semanticWeight }),
        ...(config.hybridSearch.fullTextLimit && { fulltextLimit: config.hybridSearch.fullTextLimit }),
        ...(config.hybridSearch.rrfK && { rrfK: config.hybridSearch.rrfK }),
      };

      const graphSearchSettings: GraphSearchSettings = {
        enabled: config.graphSearch.enabled,
        ...(config.graphSearch.maxCommunityDescriptionLength && {
          maxCommunityDescriptionLength: config.graphSearch.maxCommunityDescriptionLength
        }),
        ...(config.graphSearch.maxLlmQueries && {
          maxLlmQueriesForGlobalSearch: config.graphSearch.maxLlmQueries
        }),
        ...(config.graphSearch.localSearchLimits && { limits: config.graphSearch.localSearchLimits }),
      };

      const searchSettings: SearchSettings = {
        useSemanticSearch: config.vectorSearch.enabled,
        useHybridSearch: config.hybridSearch.enabled,
        useFulltextSearch: config.hybridSearch.enabled,
        filters: config.vectorSearch.searchFilters ? JSON.parse(config.vectorSearch.searchFilters) : {},
        limit: config.vectorSearch.searchLimit,
        includeMetadata: config.vectorSearch.includeMetadatas,
        chunkSettings: chunkSearchSettings,
        hybridSettings: hybridSearchSettings,
        graphSettings: graphSearchSettings,
      };

      // Prepare generation settings
      const ragGenerationConfig: GenerationConfig = {
        temperature: config.ragGeneration.temperature,
        topP: config.ragGeneration.topP,
        maxTokensToSample: config.ragGeneration.maxTokensToSample,
      };

      // Convert message history to the format expected by R2R
      const messages = messageHistory?.map(msg => ({
        role: msg.role as 'user' | 'assistant' | 'system',
        content: msg.content,
      })) || [];

      // Add the current message
      messages.push({
        role: 'user',
        content: message,
      });

      // Use agent mode based on configuration
      if (config.app.defaultMode === 'rag_agent') {
        const streamResponse = await this.client.retrieval.agent({
          message: {
            role: 'user',
            content: message,
          },
          ragGenerationConfig,
          searchSettings,
          conversationId,
        });

        // Handle streaming response
        return await this.processStreamResponse(streamResponse);
      } else {
        // Use RAG mode
        const streamResponse = await this.client.retrieval.rag({
          query: message,
          ragGenerationConfig,
          searchSettings,
        });

        // Handle streaming response
        return await this.processStreamResponse(streamResponse);
      }
    } catch (error) {
      console.error('Error in chat service:', error);
      throw error;
    }
  }

  /**
   * Process streaming response from R2R
   */
  private async processStreamResponse(streamResponse: any): Promise<ChatResponse> {
    try {
      const reader = streamResponse.getReader();
      const decoder = new TextDecoder();
      let buffer = "";
      let fullContent = "";
      let sources: any[] = [];
      let metadata: Record<string, any> = {};

      // Process the stream
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          break;
        }

        buffer += decoder.decode(value, { stream: true });

        // Process complete SSE events from the buffer
        const events = buffer.split("\n\n");
        buffer = events.pop() || ""; // Keep the last potentially incomplete event in the buffer

        for (const event of events) {
          if (!event.trim()) {
            continue;
          }

          const lines = event.split("\n");
          const eventType = lines[0].startsWith("event: ")
            ? lines[0].slice(7)
            : "";
          const dataLine = lines.find((line) => line.startsWith("data: "));

          if (!dataLine) {
            continue;
          }

          const jsonStr = dataLine.slice(6);

          try {
            const eventData = JSON.parse(jsonStr);

            if (eventType === "search_results") {
              // Handle search results
              if (eventData.search_results) {
                sources = eventData.search_results.chunk_search_results || [];
              }
            } else if (eventType === "message") {
              // Handle incremental content delta
              if (eventData.delta && eventData.delta.content) {
                const contentItems = eventData.delta.content;
                for (const item of contentItems) {
                  if (
                    item.type === "text" &&
                    item.payload &&
                    item.payload.value
                  ) {
                    fullContent += item.payload.value;
                  }
                }
              }
            }
          } catch (err) {
            console.error("Error parsing SSE event data:", err, jsonStr);
          }
        }
      }

      return {
        message: fullContent || 'No response generated',
        sources,
        metadata,
      };
    } catch (error) {
      console.error('Error processing stream response:', error);
      throw error;
    }
  }

  /**
   * Create a new conversation
   */
  async createConversation(name?: string): Promise<string> {
    try {
      const response = await this.client.conversations.create({
        name: name || `Conversation ${new Date().toLocaleString()}`,
      });

      if (response.results?.id) {
        return response.results.id;
      } else {
        throw new Error('Failed to create conversation');
      }
    } catch (error) {
      console.error('Error creating conversation:', error);
      // Return a mock ID for development
      return `conv-${Date.now()}`;
    }
  }

  /**
   * Get conversation history
   */
  async getConversationHistory(conversationId: string): Promise<Message[]> {
    try {
      const response = await this.client.conversations.retrieve({
        id: conversationId,
      });

      if (response.results) {
        return response.results.map((msg: any) => ({
          id: msg.id || `msg-${Date.now()}-${Math.random()}`,
          role: msg.metadata?.role || 'user',
          content: msg.metadata?.content || msg.content || '',
          timestamp: msg.metadata?.timestamp || new Date().toISOString(),
          metadata: msg.metadata,
        }));
      }

      return [];
    } catch (error) {
      console.error('Error getting conversation history:', error);
      return [];
    }
  }

  /**
   * List conversations
   */
  async listConversations(offset: number = 0, limit: number = 100) {
    try {
      const response = await this.client.conversations.list({
        offset,
        limit,
      });

      return response.results || [];
    } catch (error) {
      console.error('Error listing conversations:', error);
      return [];
    }
  }

  /**
   * Delete a conversation
   */
  async deleteConversation(conversationId: string): Promise<void> {
    try {
      await this.client.conversations.delete({
        id: conversationId,
      });
    } catch (error) {
      console.error('Error deleting conversation:', error);
      // Don't throw error in development mode
      console.warn('Conversation deletion failed, continuing...');
    }
  }
}
