import { r2rClient } from 'r2r-js';
import { Message } from '@/types';
import { loadChatConfig } from '@/config/chatConfig';

export interface ChatResponse {
  message: string;
  sources?: any[];
  metadata?: Record<string, any>;
}

export class ChatService {
  private client: r2rClient;

  constructor(client: r2rClient) {
    this.client = client;
  }

  /**
   * Send a message to the R2R agent and get a response
   */
  async sendMessage(
    message: string,
    conversationId?: string,
    messageHistory?: Message[]
  ): Promise<ChatResponse> {
    try {
      const config = loadChatConfig();
      
      // Prepare the search settings based on configuration
      const searchSettings = {
        use_vector_search: config.vectorSearch.enabled,
        use_hybrid_search: config.hybridSearch.enabled,
        use_kg_search: config.graphSearch.enabled,
        filters: config.vectorSearch.searchFilters ? JSON.parse(config.vectorSearch.searchFilters) : {},
        search_limit: config.vectorSearch.searchLimit,
        index_measure: config.vectorSearch.indexMeasure,
        include_metadatas: config.vectorSearch.includeMetadatas,
        ...(config.vectorSearch.probes && { probes: config.vectorSearch.probes }),
        ...(config.vectorSearch.efSearch && { ef_search: config.vectorSearch.efSearch }),
        ...(config.hybridSearch.fullTextWeight && { full_text_weight: config.hybridSearch.fullTextWeight }),
        ...(config.hybridSearch.semanticWeight && { semantic_weight: config.hybridSearch.semanticWeight }),
        ...(config.hybridSearch.fullTextLimit && { full_text_limit: config.hybridSearch.fullTextLimit }),
        ...(config.hybridSearch.rrfK && { rrf_k: config.hybridSearch.rrfK }),
        ...(config.graphSearch.kgSearchLevel !== null && { kg_search_level: config.graphSearch.kgSearchLevel }),
        max_community_description_length: config.graphSearch.maxCommunityDescriptionLength,
        local_search_limits: config.graphSearch.localSearchLimits,
        ...(config.graphSearch.maxLlmQueries && { max_llm_queries: config.graphSearch.maxLlmQueries }),
      };

      // Prepare generation settings
      const generationSettings = {
        temperature: config.ragGeneration.temperature,
        top_p: config.ragGeneration.topP,
        top_k: config.ragGeneration.topK,
        max_tokens_to_sample: config.ragGeneration.maxTokensToSample,
        kg_temperature: config.ragGeneration.kgTemperature,
        kg_top_p: config.ragGeneration.kgTopP,
        kg_top_k: config.ragGeneration.kgTopK,
        kg_max_tokens_to_sample: config.ragGeneration.kgMaxTokensToSample,
      };

      // Convert message history to the format expected by R2R
      const messages = messageHistory?.map(msg => ({
        role: msg.role,
        content: msg.content,
      })) || [];

      // Add the current message
      messages.push({
        role: 'user',
        content: message,
      });

      // Use RAG mode for now (agent mode will be implemented when R2R SDK is updated)
      try {
        const response = await (this.client as any).rag({
          query: message,
          use_vector_search: searchSettings.use_vector_search,
          use_hybrid_search: searchSettings.use_hybrid_search,
          use_kg_search: searchSettings.use_kg_search,
          search_filters: searchSettings.filters,
          search_limit: searchSettings.search_limit,
          index_measure: searchSettings.index_measure,
          include_metadatas: searchSettings.include_metadatas,
          probes: searchSettings.probes,
          ef_search: searchSettings.ef_search,
          full_text_weight: searchSettings.full_text_weight,
          semantic_weight: searchSettings.semantic_weight,
          full_text_limit: searchSettings.full_text_limit,
          rrf_k: searchSettings.rrf_k,
          kg_search_level: searchSettings.kg_search_level,
          max_community_description_length: searchSettings.max_community_description_length,
          local_search_limits: searchSettings.local_search_limits,
          max_llm_queries: searchSettings.max_llm_queries,
          rag_generation_config: generationSettings,
        });

        if (response.results) {
          return {
            message: response.results.completion?.choices?.[0]?.message?.content || 'No response generated',
            sources: response.results.search_results?.vector_search_results || [],
            metadata: response.results.metadata || {},
          };
        } else {
          throw new Error('No response from RAG');
        }
      } catch (apiError) {
        // Fallback to simple echo for development
        console.warn('R2R API call failed, using fallback response:', apiError);
        return {
          message: `Echo: ${message} (R2R integration will be completed when backend is available)`,
          sources: [],
          metadata: {},
        };
      }
    } catch (error) {
      console.error('Error in chat service:', error);
      throw error;
    }
  }

  /**
   * Create a new conversation
   */
  async createConversation(name?: string): Promise<string> {
    try {
      const response = await (this.client as any).conversations.create({
        name: name || `Conversation ${new Date().toLocaleString()}`,
      });

      if (response.results?.id) {
        return response.results.id;
      } else {
        throw new Error('Failed to create conversation');
      }
    } catch (error) {
      console.error('Error creating conversation:', error);
      // Return a mock ID for development
      return `conv-${Date.now()}`;
    }
  }

  /**
   * Get conversation history
   */
  async getConversationHistory(conversationId: string): Promise<Message[]> {
    try {
      const response = await (this.client as any).conversations.retrieve({
        id: conversationId,
      });

      if (response.results) {
        return response.results.map((msg: any) => ({
          id: msg.id || `msg-${Date.now()}-${Math.random()}`,
          role: msg.metadata?.role || 'user',
          content: msg.metadata?.content || msg.content || '',
          timestamp: msg.metadata?.timestamp || new Date().toISOString(),
          metadata: msg.metadata,
        }));
      }

      return [];
    } catch (error) {
      console.error('Error getting conversation history:', error);
      return [];
    }
  }

  /**
   * List conversations
   */
  async listConversations(offset: number = 0, limit: number = 100) {
    try {
      const response = await (this.client as any).conversations.list({
        offset,
        limit,
      });

      return response.results || [];
    } catch (error) {
      console.error('Error listing conversations:', error);
      return [];
    }
  }

  /**
   * Delete a conversation
   */
  async deleteConversation(conversationId: string): Promise<void> {
    try {
      await (this.client as any).conversations.delete({
        id: conversationId,
      });
    } catch (error) {
      console.error('Error deleting conversation:', error);
      // Don't throw error in development mode
      console.warn('Conversation deletion failed, continuing...');
    }
  }
}
