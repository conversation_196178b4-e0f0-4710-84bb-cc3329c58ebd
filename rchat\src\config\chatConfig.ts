/**
 * Chat Configuration for RChat Frontend
 *
 * This file contains all configuration options for the chat application,
 * including search settings, server connection, and UI preferences.
 */

import { ChatConfig, VectorSearchConfig, HybridSearchConfig, GraphSearchConfig, RagGenerationConfig, ServerConfig, AppConfig } from '../types';

/**
 * Default configuration values
 */
export const defaultChatConfig: ChatConfig = {
  server: {
    apiUrl: "http://localhost:7272",
    port: 7272,
    useHttps: false,
    apiVersion: "v3",
    timeout: 30000,
  },
  app: {
    appName: "RChat",
    appDescription: "R2R-powered chat application",
    version: "1.0.0",
    defaultMode: "rag_agent",
  },
  vectorSearch: {
    enabled: true,
    searchLimit: 10,
    searchFilters: "{}",
    indexMeasure: "cosine_distance",
    includeMetadatas: false,
    probes: undefined,
    efSearch: undefined,
  },
  hybridSearch: {
    enabled: false,
    fullTextWeight: undefined,
    semanticWeight: undefined,
    fullTextLimit: undefined,
    rrfK: undefined,
  },
  graphSearch: {
    enabled: true,
    kgSearchLevel: null,
    maxCommunityDescriptionLength: 100,
    localSearchLimits: {},
    maxLlmQueries: undefined,
  },
  ragGeneration: {
    temperature: 0.1,
    topP: 1,
    topK: 100,
    maxTokensToSample: 1024,
    kgTemperature: 0.1,
    kgTopP: 1,
    kgTopK: 100,
    kgMaxTokensToSample: 1024,
  },
};

/**
 * Load configuration from localStorage with fallback to defaults
 */
export function loadChatConfig(): ChatConfig {
  if (typeof window === "undefined") {
    return defaultChatConfig;
  }

  try {
    const stored = localStorage.getItem("rchatConfig");
    if (stored) {
      const parsed = JSON.parse(stored);
      // Merge with defaults to ensure all properties exist
      return {
        ...defaultChatConfig,
        ...parsed,
        server: { ...defaultChatConfig.server, ...parsed.server },
        app: { ...defaultChatConfig.app, ...parsed.app },
        vectorSearch: {
          ...defaultChatConfig.vectorSearch,
          ...parsed.vectorSearch,
        },
        hybridSearch: {
          ...defaultChatConfig.hybridSearch,
          ...parsed.hybridSearch,
        },
        graphSearch: {
          ...defaultChatConfig.graphSearch,
          ...parsed.graphSearch,
        },
        ragGeneration: {
          ...defaultChatConfig.ragGeneration,
          ...parsed.ragGeneration,
        },
      };
    }
  } catch (error) {
    console.warn("Failed to load chat config from localStorage:", error);
  }

  return defaultChatConfig;
}

/**
 * Save configuration to localStorage
 */
export function saveChatConfig(config: ChatConfig): void {
  if (typeof window === "undefined") {
    return;
  }

  try {
    localStorage.setItem("rchatConfig", JSON.stringify(config));
  } catch (error) {
    console.error("Failed to save chat config to localStorage:", error);
  }
}

/**
 * Load configuration from public config.json file
 */
export async function loadPublicConfig(): Promise<Partial<ChatConfig> | null> {
  if (typeof window === "undefined") {
    return null;
  }

  try {
    const response = await fetch("/config.json");
    if (!response.ok) {
      console.warn("Could not load public config.json");
      return null;
    }
    const config = await response.json();
    return {
      server: {
        apiUrl: config.apiUrl,
        useHttps: config.useHttps,
        timeout: config.timeout,
      },
      app: {
        appName: config.appName,
        appDescription: config.appDescription,
        version: config.version,
        defaultMode: "rag_agent",
      },
    };
  } catch (error) {
    console.warn("Error loading public config.json:", error);
    return null;
  }
}

/**
 * Get the deployment URL for the API
 */
export function getDeploymentUrl(config?: ChatConfig): string {
  const chatConfig = config || loadChatConfig();

  // Check for runtime config first (for Docker deployments)
  if (typeof window !== "undefined" && window.__RUNTIME_CONFIG__) {
    const runtimeUrl = window.__RUNTIME_CONFIG__.NEXT_PUBLIC_R2R_DEPLOYMENT_URL;
    if (
      runtimeUrl &&
      !runtimeUrl.includes("__NEXT_PUBLIC_R2R_DEPLOYMENT_URL__")
    ) {
      return runtimeUrl;
    }
  }

  return chatConfig.server.apiUrl;
}
