import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { r2rClient } from 'r2r-js';
import { User, AuthState } from '../types';
import { getDeploymentUrl } from '../config/chatConfig';

interface UserContextType {
  authState: AuthState;
  login: (email: string, password: string, deploymentUrl?: string) => Promise<User>;
  logout: () => void;
  getClient: () => Promise<r2rClient | null>;
  isLoading: boolean;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
}

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    token: null,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [client, setClient] = useState<r2rClient | null>(null);

  // Initialize client and check for existing session
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const deploymentUrl = getDeploymentUrl();
        const newClient = new r2rClient(deploymentUrl);
        setClient(newClient);

        // Check for stored token
        const storedToken = localStorage.getItem('r2r_token');
        const storedUser = localStorage.getItem('r2r_user');

        if (storedToken && storedUser) {
          try {
            const user = JSON.parse(storedUser);
            setAuthState({
              isAuthenticated: true,
              user,
              token: storedToken,
            });
          } catch (error) {
            console.error('Error parsing stored user data:', error);
            localStorage.removeItem('r2r_token');
            localStorage.removeItem('r2r_user');
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (email: string, password: string, deploymentUrl?: string): Promise<User> => {
    try {
      const apiUrl = deploymentUrl || getDeploymentUrl();
      const loginClient = new r2rClient(apiUrl);

      const response = await loginClient.users.login({
        email,
        password,
      });

      if (!response.results) {
        throw new Error('Login failed: No results returned');
      }

      const { access_token, user_id } = response.results;

      if (!access_token) {
        throw new Error('Login failed: No access token received');
      }

      // Get user info
      const userResponse = await loginClient.users.me();
      
      if (!userResponse.results) {
        throw new Error('Failed to get user information');
      }

      const user: User = {
        id: userResponse.results.id || user_id,
        email: userResponse.results.email || email,
        name: userResponse.results.name,
        role: userResponse.results.role,
      };

      // Store auth data
      localStorage.setItem('r2r_token', access_token);
      localStorage.setItem('r2r_user', JSON.stringify(user));

      // Update state
      setAuthState({
        isAuthenticated: true,
        user,
        token: access_token,
      });

      // Update client with new URL if provided
      if (deploymentUrl) {
        const newClient = new r2rClient(deploymentUrl);
        setClient(newClient);
      }

      return user;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  const logout = () => {
    localStorage.removeItem('r2r_token');
    localStorage.removeItem('r2r_user');
    setAuthState({
      isAuthenticated: false,
      user: null,
      token: null,
    });
  };

  const getClient = async (): Promise<r2rClient | null> => {
    if (!authState.isAuthenticated || !authState.token) {
      return null;
    }

    if (!client) {
      const deploymentUrl = getDeploymentUrl();
      const newClient = new r2rClient(deploymentUrl);
      setClient(newClient);
      return newClient;
    }

    return client;
  };

  const value: UserContextType = {
    authState,
    login,
    logout,
    getClient,
    isLoading,
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};

export const useUserContext = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUserContext must be used within a UserProvider');
  }
  return context;
};
