import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { r2rClient } from 'r2r-js';
import { User, AuthState } from '../types';
import { getDeploymentUrl } from '../config/chatConfig';

interface UserContextType {
  authState: AuthState;
  login: (email: string, password: string, deploymentUrl?: string) => Promise<User>;
  logout: () => void;
  getClient: () => Promise<r2rClient | null>;
  isLoading: boolean;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
}

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    token: null,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [client, setClient] = useState<r2rClient | null>(null);

  // Initialize client and check for existing session
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const deploymentUrl = getDeploymentUrl();
        const newClient = new r2rClient(deploymentUrl);
        setClient(newClient);

        // Check for stored token (try both rchat and chatfrontend token keys)
        const storedToken = localStorage.getItem('chatAccessToken') || localStorage.getItem('r2r_token');
        const storedRefreshToken = localStorage.getItem('chatRefreshToken');
        const storedUser = localStorage.getItem('r2r_user');

        if (storedToken && storedUser) {
          try {
            const user = JSON.parse(storedUser);

            // Set up client with stored tokens
            if (storedRefreshToken) {
              newClient.setTokens(storedToken, storedRefreshToken);
            }

            setAuthState({
              isAuthenticated: true,
              user,
              token: storedToken,
            });
          } catch (error) {
            console.error('Error parsing stored user data:', error);
            localStorage.removeItem('r2r_token');
            localStorage.removeItem('r2r_user');
            localStorage.removeItem('chatAccessToken');
            localStorage.removeItem('chatRefreshToken');
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (email: string, password: string, deploymentUrl?: string): Promise<User> => {
    try {
      const apiUrl = deploymentUrl || getDeploymentUrl();
      const loginClient = new r2rClient(apiUrl);

      const tokens = await loginClient.users.login({
        email: email,
        password: password,
      });

      if (!tokens.results) {
        throw new Error('Login failed: No results returned');
      }

      // Store tokens like chatfrontend does
      localStorage.setItem('chatAccessToken', tokens.results.accessToken.token);
      localStorage.setItem('chatRefreshToken', tokens.results.refreshToken.token);

      // Set tokens on client
      loginClient.setTokens(
        tokens.results.accessToken.token,
        tokens.results.refreshToken.token,
      );

      setClient(loginClient);

      // Get user info
      const userInfo = await loginClient.users.me();

      if (!userInfo.results) {
        throw new Error('Failed to get user information');
      }

      // Check user role like chatfrontend does
      let userRole: "admin" | "user" = "user";
      try {
        await loginClient.system.settings();
        userRole = "admin";
      } catch (error) {
        if (error instanceof Error && 'status' in error && (error as any).status === 403) {
          // User doesn't have admin access, keep as "user"
        } else {
          console.error("Unexpected error when checking user role:", error);
        }
      }

      const user: User = {
        id: userInfo.results.id,
        email: userInfo.results.email || email,
        name: userInfo.results.name,
        role: userRole,
      };

      // Store auth data
      localStorage.setItem('r2r_token', tokens.results.accessToken.token);
      localStorage.setItem('r2r_user', JSON.stringify(user));

      // Update state
      setAuthState({
        isAuthenticated: true,
        user,
        token: tokens.results.accessToken.token,
      });

      return user;
    } catch (error) {
      console.error('Login error:', error);

      // Provide more detailed error information
      if (error instanceof Error) {
        if (error.message.includes('401') || error.message.includes('Unauthorized')) {
          throw new Error('Invalid email or password. Please check your credentials.');
        } else if (error.message.includes('404') || error.message.includes('Not Found')) {
          throw new Error('User not found. Please check your email address.');
        } else if (error.message.includes('500') || error.message.includes('Internal Server Error')) {
          throw new Error('Server error. Please try again later.');
        } else if (error.message.includes('Network Error') || error.message.includes('fetch')) {
          throw new Error('Cannot connect to server. Please check if R2R backend is running.');
        }
      }

      throw error;
    }
  };

  const logout = () => {
    localStorage.removeItem('r2r_token');
    localStorage.removeItem('r2r_user');
    localStorage.removeItem('chatAccessToken');
    localStorage.removeItem('chatRefreshToken');
    setAuthState({
      isAuthenticated: false,
      user: null,
      token: null,
    });
    setClient(null);
  };

  const getClient = async (): Promise<r2rClient | null> => {
    if (!authState.isAuthenticated || !authState.token) {
      return null;
    }

    if (!client) {
      const deploymentUrl = getDeploymentUrl();
      const newClient = new r2rClient(deploymentUrl);

      // Set tokens if available
      const accessToken = localStorage.getItem('chatAccessToken') || localStorage.getItem('r2r_token');
      const refreshToken = localStorage.getItem('chatRefreshToken');

      if (accessToken) {
        newClient.setTokens(accessToken, refreshToken || '');
      }

      setClient(newClient);
      return newClient;
    }

    return client;
  };

  const value: UserContextType = {
    authState,
    login,
    logout,
    getClient,
    isLoading,
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};

export const useUserContext = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUserContext must be used within a UserProvider');
  }
  return context;
};
