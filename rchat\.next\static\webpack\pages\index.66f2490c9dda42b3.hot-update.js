"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/lib/chatService.ts":
/*!********************************!*\
  !*** ./src/lib/chatService.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatService: function() { return /* binding */ ChatService; }\n/* harmony export */ });\n/* harmony import */ var _config_chatConfig__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config/chatConfig */ \"./src/config/chatConfig.ts\");\n\nclass ChatService {\n    /**\n   * Send a message to the R2R agent and get a response\n   */ async sendMessage(message, conversationId, messageHistory) {\n        try {\n            const config = (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_0__.loadChatConfig)();\n            // Prepare the search settings based on configuration\n            const chunkSearchSettings = {\n                indexMeasure: config.vectorSearch.indexMeasure,\n                enabled: config.vectorSearch.enabled,\n                ...config.vectorSearch.probes && {\n                    probes: config.vectorSearch.probes\n                },\n                ...config.vectorSearch.efSearch && {\n                    efSearch: config.vectorSearch.efSearch\n                }\n            };\n            const hybridSearchSettings = {\n                ...config.hybridSearch.fullTextWeight && {\n                    fulltextWeight: config.hybridSearch.fullTextWeight\n                },\n                ...config.hybridSearch.semanticWeight && {\n                    semanticWeight: config.hybridSearch.semanticWeight\n                },\n                ...config.hybridSearch.fullTextLimit && {\n                    fulltextLimit: config.hybridSearch.fullTextLimit\n                },\n                ...config.hybridSearch.rrfK && {\n                    rrfK: config.hybridSearch.rrfK\n                }\n            };\n            const graphSearchSettings = {\n                enabled: config.graphSearch.enabled,\n                ...config.graphSearch.maxCommunityDescriptionLength && {\n                    maxCommunityDescriptionLength: config.graphSearch.maxCommunityDescriptionLength\n                },\n                ...config.graphSearch.maxLlmQueries && {\n                    maxLlmQueriesForGlobalSearch: config.graphSearch.maxLlmQueries\n                },\n                ...config.graphSearch.localSearchLimits && {\n                    limits: config.graphSearch.localSearchLimits\n                }\n            };\n            const searchSettings = {\n                useSemanticSearch: config.vectorSearch.enabled,\n                useHybridSearch: config.hybridSearch.enabled,\n                useFulltextSearch: config.hybridSearch.enabled,\n                filters: config.vectorSearch.searchFilters ? JSON.parse(config.vectorSearch.searchFilters) : {},\n                limit: config.vectorSearch.searchLimit,\n                includeMetadata: config.vectorSearch.includeMetadatas,\n                chunkSettings: chunkSearchSettings,\n                hybridSettings: hybridSearchSettings,\n                graphSettings: graphSearchSettings\n            };\n            // Prepare generation settings\n            const ragGenerationConfig = {\n                stream: true,\n                temperature: config.ragGeneration.temperature,\n                topP: config.ragGeneration.topP,\n                maxTokensToSample: config.ragGeneration.maxTokensToSample\n            };\n            // Create the user message in R2R format\n            const userMessage = {\n                role: \"user\",\n                content: message\n            };\n            // Use agent mode based on configuration\n            if (config.app.defaultMode === \"rag_agent\") {\n                console.log(\"Using agent mode with config:\", {\n                    message: userMessage,\n                    ragGenerationConfig,\n                    searchSettings,\n                    conversationId\n                });\n                const streamResponse = await this.client.retrieval.agent({\n                    message: userMessage,\n                    ragGenerationConfig,\n                    searchSettings,\n                    conversationId\n                });\n                console.log(\"Agent response received:\", streamResponse);\n                // Handle streaming response\n                return await this.processStreamResponse(streamResponse);\n            } else {\n                console.log(\"Using RAG mode with config:\", {\n                    query: message,\n                    ragGenerationConfig,\n                    searchSettings\n                });\n                const streamResponse = await this.client.retrieval.rag({\n                    query: message,\n                    ragGenerationConfig,\n                    searchSettings\n                });\n                console.log(\"RAG response received:\", streamResponse);\n                // Handle streaming response\n                return await this.processStreamResponse(streamResponse);\n            }\n        } catch (error) {\n            console.error(\"Error in chat service:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Process streaming response from R2R\n   */ async processStreamResponse(streamResponse) {\n        try {\n            console.log(\"Processing stream response:\", streamResponse);\n            console.log(\"Stream response type:\", typeof streamResponse);\n            console.log(\"Stream response has getReader:\", typeof streamResponse.getReader);\n            const reader = streamResponse.getReader();\n            const decoder = new TextDecoder();\n            let buffer = \"\";\n            let fullContent = \"\";\n            let sources = [];\n            let metadata = {};\n            // Process the stream\n            while(true){\n                const { done, value } = await reader.read();\n                if (done) {\n                    break;\n                }\n                buffer += decoder.decode(value, {\n                    stream: true\n                });\n                // Process complete SSE events from the buffer\n                const events = buffer.split(\"\\n\\n\");\n                buffer = events.pop() || \"\"; // Keep the last potentially incomplete event in the buffer\n                for (const event of events){\n                    if (!event.trim()) {\n                        continue;\n                    }\n                    const lines = event.split(\"\\n\");\n                    const eventType = lines[0].startsWith(\"event: \") ? lines[0].slice(7) : \"\";\n                    const dataLine = lines.find((line)=>line.startsWith(\"data: \"));\n                    if (!dataLine) {\n                        continue;\n                    }\n                    const jsonStr = dataLine.slice(6);\n                    try {\n                        const eventData = JSON.parse(jsonStr);\n                        if (eventType === \"search_results\") {\n                            // Handle search results\n                            if (eventData.search_results) {\n                                sources = eventData.search_results.chunk_search_results || [];\n                            }\n                        } else if (eventType === \"message\") {\n                            // Handle incremental content delta\n                            if (eventData.delta && eventData.delta.content) {\n                                const contentItems = eventData.delta.content;\n                                for (const item of contentItems){\n                                    if (item.type === \"text\" && item.payload && item.payload.value) {\n                                        fullContent += item.payload.value;\n                                    }\n                                }\n                            }\n                        }\n                    } catch (err) {\n                        console.error(\"Error parsing SSE event data:\", err, jsonStr);\n                    }\n                }\n            }\n            return {\n                message: fullContent || \"No response generated\",\n                sources,\n                metadata\n            };\n        } catch (error) {\n            console.error(\"Error processing stream response:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create a new conversation\n   */ async createConversation(name) {\n        try {\n            var _response_results;\n            const response = await this.client.conversations.create({\n                name: name || \"Conversation \".concat(new Date().toLocaleString())\n            });\n            if ((_response_results = response.results) === null || _response_results === void 0 ? void 0 : _response_results.id) {\n                return response.results.id;\n            } else {\n                throw new Error(\"Failed to create conversation\");\n            }\n        } catch (error) {\n            console.error(\"Error creating conversation:\", error);\n            // Return a mock ID for development\n            return \"conv-\".concat(Date.now());\n        }\n    }\n    /**\n   * Get conversation history\n   */ async getConversationHistory(conversationId) {\n        try {\n            const response = await this.client.conversations.retrieve({\n                id: conversationId\n            });\n            if (response.results) {\n                return response.results.map((msg)=>{\n                    var _msg_metadata, _msg_metadata1, _msg_metadata2;\n                    return {\n                        id: msg.id || \"msg-\".concat(Date.now(), \"-\").concat(Math.random()),\n                        role: ((_msg_metadata = msg.metadata) === null || _msg_metadata === void 0 ? void 0 : _msg_metadata.role) || \"user\",\n                        content: ((_msg_metadata1 = msg.metadata) === null || _msg_metadata1 === void 0 ? void 0 : _msg_metadata1.content) || msg.content || \"\",\n                        timestamp: ((_msg_metadata2 = msg.metadata) === null || _msg_metadata2 === void 0 ? void 0 : _msg_metadata2.timestamp) || new Date().toISOString(),\n                        metadata: msg.metadata\n                    };\n                });\n            }\n            return [];\n        } catch (error) {\n            console.error(\"Error getting conversation history:\", error);\n            return [];\n        }\n    }\n    /**\n   * List conversations\n   */ async listConversations() {\n        let offset = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 100;\n        try {\n            const response = await this.client.conversations.list({\n                offset,\n                limit\n            });\n            return response.results || [];\n        } catch (error) {\n            console.error(\"Error listing conversations:\", error);\n            return [];\n        }\n    }\n    /**\n   * Delete a conversation\n   */ async deleteConversation(conversationId) {\n        try {\n            await this.client.conversations.delete({\n                id: conversationId\n            });\n        } catch (error) {\n            console.error(\"Error deleting conversation:\", error);\n            // Don't throw error in development mode\n            console.warn(\"Conversation deletion failed, continuing...\");\n        }\n    }\n    constructor(client){\n        this.client = client;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvbGliL2NoYXRTZXJ2aWNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBRXFEO0FBUTlDLE1BQU1DO0lBT1g7O0dBRUMsR0FDRCxNQUFNQyxZQUNKQyxPQUFlLEVBQ2ZDLGNBQXVCLEVBQ3ZCQyxjQUEwQixFQUNIO1FBQ3ZCLElBQUk7WUFDRixNQUFNQyxTQUFTTixrRUFBY0E7WUFFN0IscURBQXFEO1lBQ3JELE1BQU1PLHNCQUEyQztnQkFDL0NDLGNBQWNGLE9BQU9HLFlBQVksQ0FBQ0QsWUFBWTtnQkFDOUNFLFNBQVNKLE9BQU9HLFlBQVksQ0FBQ0MsT0FBTztnQkFDcEMsR0FBSUosT0FBT0csWUFBWSxDQUFDRSxNQUFNLElBQUk7b0JBQUVBLFFBQVFMLE9BQU9HLFlBQVksQ0FBQ0UsTUFBTTtnQkFBQyxDQUFDO2dCQUN4RSxHQUFJTCxPQUFPRyxZQUFZLENBQUNHLFFBQVEsSUFBSTtvQkFBRUEsVUFBVU4sT0FBT0csWUFBWSxDQUFDRyxRQUFRO2dCQUFDLENBQUM7WUFDaEY7WUFFQSxNQUFNQyx1QkFBNkM7Z0JBQ2pELEdBQUlQLE9BQU9RLFlBQVksQ0FBQ0MsY0FBYyxJQUFJO29CQUFFQyxnQkFBZ0JWLE9BQU9RLFlBQVksQ0FBQ0MsY0FBYztnQkFBQyxDQUFDO2dCQUNoRyxHQUFJVCxPQUFPUSxZQUFZLENBQUNHLGNBQWMsSUFBSTtvQkFBRUEsZ0JBQWdCWCxPQUFPUSxZQUFZLENBQUNHLGNBQWM7Z0JBQUMsQ0FBQztnQkFDaEcsR0FBSVgsT0FBT1EsWUFBWSxDQUFDSSxhQUFhLElBQUk7b0JBQUVDLGVBQWViLE9BQU9RLFlBQVksQ0FBQ0ksYUFBYTtnQkFBQyxDQUFDO2dCQUM3RixHQUFJWixPQUFPUSxZQUFZLENBQUNNLElBQUksSUFBSTtvQkFBRUEsTUFBTWQsT0FBT1EsWUFBWSxDQUFDTSxJQUFJO2dCQUFDLENBQUM7WUFDcEU7WUFFQSxNQUFNQyxzQkFBMkM7Z0JBQy9DWCxTQUFTSixPQUFPZ0IsV0FBVyxDQUFDWixPQUFPO2dCQUNuQyxHQUFJSixPQUFPZ0IsV0FBVyxDQUFDQyw2QkFBNkIsSUFBSTtvQkFDdERBLCtCQUErQmpCLE9BQU9nQixXQUFXLENBQUNDLDZCQUE2QjtnQkFDakYsQ0FBQztnQkFDRCxHQUFJakIsT0FBT2dCLFdBQVcsQ0FBQ0UsYUFBYSxJQUFJO29CQUN0Q0MsOEJBQThCbkIsT0FBT2dCLFdBQVcsQ0FBQ0UsYUFBYTtnQkFDaEUsQ0FBQztnQkFDRCxHQUFJbEIsT0FBT2dCLFdBQVcsQ0FBQ0ksaUJBQWlCLElBQUk7b0JBQUVDLFFBQVFyQixPQUFPZ0IsV0FBVyxDQUFDSSxpQkFBaUI7Z0JBQUMsQ0FBQztZQUM5RjtZQUVBLE1BQU1FLGlCQUFpQztnQkFDckNDLG1CQUFtQnZCLE9BQU9HLFlBQVksQ0FBQ0MsT0FBTztnQkFDOUNvQixpQkFBaUJ4QixPQUFPUSxZQUFZLENBQUNKLE9BQU87Z0JBQzVDcUIsbUJBQW1CekIsT0FBT1EsWUFBWSxDQUFDSixPQUFPO2dCQUM5Q3NCLFNBQVMxQixPQUFPRyxZQUFZLENBQUN3QixhQUFhLEdBQUdDLEtBQUtDLEtBQUssQ0FBQzdCLE9BQU9HLFlBQVksQ0FBQ3dCLGFBQWEsSUFBSSxDQUFDO2dCQUM5RkcsT0FBTzlCLE9BQU9HLFlBQVksQ0FBQzRCLFdBQVc7Z0JBQ3RDQyxpQkFBaUJoQyxPQUFPRyxZQUFZLENBQUM4QixnQkFBZ0I7Z0JBQ3JEQyxlQUFlakM7Z0JBQ2ZrQyxnQkFBZ0I1QjtnQkFDaEI2QixlQUFlckI7WUFDakI7WUFFQSw4QkFBOEI7WUFDOUIsTUFBTXNCLHNCQUF3QztnQkFDNUNDLFFBQVE7Z0JBQ1JDLGFBQWF2QyxPQUFPd0MsYUFBYSxDQUFDRCxXQUFXO2dCQUM3Q0UsTUFBTXpDLE9BQU93QyxhQUFhLENBQUNDLElBQUk7Z0JBQy9CQyxtQkFBbUIxQyxPQUFPd0MsYUFBYSxDQUFDRSxpQkFBaUI7WUFDM0Q7WUFFQSx3Q0FBd0M7WUFDeEMsTUFBTUMsY0FBYztnQkFDbEJDLE1BQU07Z0JBQ05DLFNBQVNoRDtZQUNYO1lBRUEsd0NBQXdDO1lBQ3hDLElBQUlHLE9BQU84QyxHQUFHLENBQUNDLFdBQVcsS0FBSyxhQUFhO2dCQUMxQ0MsUUFBUUMsR0FBRyxDQUFDLGlDQUFpQztvQkFDM0NwRCxTQUFTOEM7b0JBQ1ROO29CQUNBZjtvQkFDQXhCO2dCQUNGO2dCQUVBLE1BQU1vRCxpQkFBaUIsTUFBTSxJQUFJLENBQUNDLE1BQU0sQ0FBQ0MsU0FBUyxDQUFDQyxLQUFLLENBQUM7b0JBQ3ZEeEQsU0FBUzhDO29CQUNUTjtvQkFDQWY7b0JBQ0F4QjtnQkFDRjtnQkFFQWtELFFBQVFDLEdBQUcsQ0FBQyw0QkFBNEJDO2dCQUV4Qyw0QkFBNEI7Z0JBQzVCLE9BQU8sTUFBTSxJQUFJLENBQUNJLHFCQUFxQixDQUFDSjtZQUMxQyxPQUFPO2dCQUNMRixRQUFRQyxHQUFHLENBQUMsK0JBQStCO29CQUN6Q00sT0FBTzFEO29CQUNQd0M7b0JBQ0FmO2dCQUNGO2dCQUVBLE1BQU00QixpQkFBaUIsTUFBTSxJQUFJLENBQUNDLE1BQU0sQ0FBQ0MsU0FBUyxDQUFDSSxHQUFHLENBQUM7b0JBQ3JERCxPQUFPMUQ7b0JBQ1B3QztvQkFDQWY7Z0JBQ0Y7Z0JBRUEwQixRQUFRQyxHQUFHLENBQUMsMEJBQTBCQztnQkFFdEMsNEJBQTRCO2dCQUM1QixPQUFPLE1BQU0sSUFBSSxDQUFDSSxxQkFBcUIsQ0FBQ0o7WUFDMUM7UUFDRixFQUFFLE9BQU9PLE9BQU87WUFDZFQsUUFBUVMsS0FBSyxDQUFDLDBCQUEwQkE7WUFDeEMsTUFBTUE7UUFDUjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFjSCxzQkFBc0JKLGNBQW1CLEVBQXlCO1FBQzlFLElBQUk7WUFDRkYsUUFBUUMsR0FBRyxDQUFDLCtCQUErQkM7WUFDM0NGLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUIsT0FBT0M7WUFDNUNGLFFBQVFDLEdBQUcsQ0FBQyxrQ0FBa0MsT0FBT0MsZUFBZVEsU0FBUztZQUU3RSxNQUFNQyxTQUFTVCxlQUFlUSxTQUFTO1lBQ3ZDLE1BQU1FLFVBQVUsSUFBSUM7WUFDcEIsSUFBSUMsU0FBUztZQUNiLElBQUlDLGNBQWM7WUFDbEIsSUFBSUMsVUFBaUIsRUFBRTtZQUN2QixJQUFJQyxXQUFnQyxDQUFDO1lBRXJDLHFCQUFxQjtZQUNyQixNQUFPLEtBQU07Z0JBQ1gsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1SLE9BQU9TLElBQUk7Z0JBQ3pDLElBQUlGLE1BQU07b0JBQ1I7Z0JBQ0Y7Z0JBRUFKLFVBQVVGLFFBQVFTLE1BQU0sQ0FBQ0YsT0FBTztvQkFBRTdCLFFBQVE7Z0JBQUs7Z0JBRS9DLDhDQUE4QztnQkFDOUMsTUFBTWdDLFNBQVNSLE9BQU9TLEtBQUssQ0FBQztnQkFDNUJULFNBQVNRLE9BQU9FLEdBQUcsTUFBTSxJQUFJLDJEQUEyRDtnQkFFeEYsS0FBSyxNQUFNQyxTQUFTSCxPQUFRO29CQUMxQixJQUFJLENBQUNHLE1BQU1DLElBQUksSUFBSTt3QkFDakI7b0JBQ0Y7b0JBRUEsTUFBTUMsUUFBUUYsTUFBTUYsS0FBSyxDQUFDO29CQUMxQixNQUFNSyxZQUFZRCxLQUFLLENBQUMsRUFBRSxDQUFDRSxVQUFVLENBQUMsYUFDbENGLEtBQUssQ0FBQyxFQUFFLENBQUNHLEtBQUssQ0FBQyxLQUNmO29CQUNKLE1BQU1DLFdBQVdKLE1BQU1LLElBQUksQ0FBQyxDQUFDQyxPQUFTQSxLQUFLSixVQUFVLENBQUM7b0JBRXRELElBQUksQ0FBQ0UsVUFBVTt3QkFDYjtvQkFDRjtvQkFFQSxNQUFNRyxVQUFVSCxTQUFTRCxLQUFLLENBQUM7b0JBRS9CLElBQUk7d0JBQ0YsTUFBTUssWUFBWXZELEtBQUtDLEtBQUssQ0FBQ3FEO3dCQUU3QixJQUFJTixjQUFjLGtCQUFrQjs0QkFDbEMsd0JBQXdCOzRCQUN4QixJQUFJTyxVQUFVQyxjQUFjLEVBQUU7Z0NBQzVCcEIsVUFBVW1CLFVBQVVDLGNBQWMsQ0FBQ0Msb0JBQW9CLElBQUksRUFBRTs0QkFDL0Q7d0JBQ0YsT0FBTyxJQUFJVCxjQUFjLFdBQVc7NEJBQ2xDLG1DQUFtQzs0QkFDbkMsSUFBSU8sVUFBVUcsS0FBSyxJQUFJSCxVQUFVRyxLQUFLLENBQUN6QyxPQUFPLEVBQUU7Z0NBQzlDLE1BQU0wQyxlQUFlSixVQUFVRyxLQUFLLENBQUN6QyxPQUFPO2dDQUM1QyxLQUFLLE1BQU0yQyxRQUFRRCxhQUFjO29DQUMvQixJQUNFQyxLQUFLQyxJQUFJLEtBQUssVUFDZEQsS0FBS0UsT0FBTyxJQUNaRixLQUFLRSxPQUFPLENBQUN2QixLQUFLLEVBQ2xCO3dDQUNBSixlQUFleUIsS0FBS0UsT0FBTyxDQUFDdkIsS0FBSztvQ0FDbkM7Z0NBQ0Y7NEJBQ0Y7d0JBQ0Y7b0JBQ0YsRUFBRSxPQUFPd0IsS0FBSzt3QkFDWjNDLFFBQVFTLEtBQUssQ0FBQyxpQ0FBaUNrQyxLQUFLVDtvQkFDdEQ7Z0JBQ0Y7WUFDRjtZQUVBLE9BQU87Z0JBQ0xyRixTQUFTa0UsZUFBZTtnQkFDeEJDO2dCQUNBQztZQUNGO1FBQ0YsRUFBRSxPQUFPUixPQUFPO1lBQ2RULFFBQVFTLEtBQUssQ0FBQyxxQ0FBcUNBO1lBQ25ELE1BQU1BO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTW1DLG1CQUFtQkMsSUFBYSxFQUFtQjtRQUN2RCxJQUFJO2dCQUtFQztZQUpKLE1BQU1BLFdBQVcsTUFBTSxJQUFJLENBQUMzQyxNQUFNLENBQUM0QyxhQUFhLENBQUNDLE1BQU0sQ0FBQztnQkFDdERILE1BQU1BLFFBQVEsZ0JBQTRDLE9BQTVCLElBQUlJLE9BQU9DLGNBQWM7WUFDekQ7WUFFQSxLQUFJSixvQkFBQUEsU0FBU0ssT0FBTyxjQUFoQkwsd0NBQUFBLGtCQUFrQk0sRUFBRSxFQUFFO2dCQUN4QixPQUFPTixTQUFTSyxPQUFPLENBQUNDLEVBQUU7WUFDNUIsT0FBTztnQkFDTCxNQUFNLElBQUlDLE1BQU07WUFDbEI7UUFDRixFQUFFLE9BQU81QyxPQUFPO1lBQ2RULFFBQVFTLEtBQUssQ0FBQyxnQ0FBZ0NBO1lBQzlDLG1DQUFtQztZQUNuQyxPQUFPLFFBQW1CLE9BQVh3QyxLQUFLSyxHQUFHO1FBQ3pCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1DLHVCQUF1QnpHLGNBQXNCLEVBQXNCO1FBQ3ZFLElBQUk7WUFDRixNQUFNZ0csV0FBVyxNQUFNLElBQUksQ0FBQzNDLE1BQU0sQ0FBQzRDLGFBQWEsQ0FBQ1MsUUFBUSxDQUFDO2dCQUN4REosSUFBSXRHO1lBQ047WUFFQSxJQUFJZ0csU0FBU0ssT0FBTyxFQUFFO2dCQUNwQixPQUFPTCxTQUFTSyxPQUFPLENBQUNNLEdBQUcsQ0FBQyxDQUFDQzt3QkFFckJBLGVBQ0dBLGdCQUNFQTsyQkFKOEI7d0JBQ3pDTixJQUFJTSxJQUFJTixFQUFFLElBQUksT0FBcUJPLE9BQWRWLEtBQUtLLEdBQUcsSUFBRyxLQUFpQixPQUFkSyxLQUFLQyxNQUFNO3dCQUM5Q2hFLE1BQU04RCxFQUFBQSxnQkFBQUEsSUFBSXpDLFFBQVEsY0FBWnlDLG9DQUFBQSxjQUFjOUQsSUFBSSxLQUFJO3dCQUM1QkMsU0FBUzZELEVBQUFBLGlCQUFBQSxJQUFJekMsUUFBUSxjQUFaeUMscUNBQUFBLGVBQWM3RCxPQUFPLEtBQUk2RCxJQUFJN0QsT0FBTyxJQUFJO3dCQUNqRGdFLFdBQVdILEVBQUFBLGlCQUFBQSxJQUFJekMsUUFBUSxjQUFaeUMscUNBQUFBLGVBQWNHLFNBQVMsS0FBSSxJQUFJWixPQUFPYSxXQUFXO3dCQUM1RDdDLFVBQVV5QyxJQUFJekMsUUFBUTtvQkFDeEI7O1lBQ0Y7WUFFQSxPQUFPLEVBQUU7UUFDWCxFQUFFLE9BQU9SLE9BQU87WUFDZFQsUUFBUVMsS0FBSyxDQUFDLHVDQUF1Q0E7WUFDckQsT0FBTyxFQUFFO1FBQ1g7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTXNELG9CQUEyRDtZQUF6Q0MsU0FBQUEsaUVBQWlCLEdBQUdsRixRQUFBQSxpRUFBZ0I7UUFDMUQsSUFBSTtZQUNGLE1BQU1nRSxXQUFXLE1BQU0sSUFBSSxDQUFDM0MsTUFBTSxDQUFDNEMsYUFBYSxDQUFDa0IsSUFBSSxDQUFDO2dCQUNwREQ7Z0JBQ0FsRjtZQUNGO1lBRUEsT0FBT2dFLFNBQVNLLE9BQU8sSUFBSSxFQUFFO1FBQy9CLEVBQUUsT0FBTzFDLE9BQU87WUFDZFQsUUFBUVMsS0FBSyxDQUFDLGdDQUFnQ0E7WUFDOUMsT0FBTyxFQUFFO1FBQ1g7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTXlELG1CQUFtQnBILGNBQXNCLEVBQWlCO1FBQzlELElBQUk7WUFDRixNQUFNLElBQUksQ0FBQ3FELE1BQU0sQ0FBQzRDLGFBQWEsQ0FBQ29CLE1BQU0sQ0FBQztnQkFDckNmLElBQUl0RztZQUNOO1FBQ0YsRUFBRSxPQUFPMkQsT0FBTztZQUNkVCxRQUFRUyxLQUFLLENBQUMsZ0NBQWdDQTtZQUM5Qyx3Q0FBd0M7WUFDeENULFFBQVFvRSxJQUFJLENBQUM7UUFDZjtJQUNGO0lBbFJBQyxZQUFZbEUsTUFBaUIsQ0FBRTtRQUM3QixJQUFJLENBQUNBLE1BQU0sR0FBR0E7SUFDaEI7QUFpUkYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2xpYi9jaGF0U2VydmljZS50cz9kNDcyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHIyckNsaWVudCwgR2VuZXJhdGlvbkNvbmZpZywgU2VhcmNoU2V0dGluZ3MsIENodW5rU2VhcmNoU2V0dGluZ3MsIEdyYXBoU2VhcmNoU2V0dGluZ3MsIEh5YnJpZFNlYXJjaFNldHRpbmdzLCBJbmRleE1lYXN1cmUgfSBmcm9tICdyMnItanMnO1xuaW1wb3J0IHsgTWVzc2FnZSB9IGZyb20gJ0AvdHlwZXMnO1xuaW1wb3J0IHsgbG9hZENoYXRDb25maWcgfSBmcm9tICdAL2NvbmZpZy9jaGF0Q29uZmlnJztcblxuZXhwb3J0IGludGVyZmFjZSBDaGF0UmVzcG9uc2Uge1xuICBtZXNzYWdlOiBzdHJpbmc7XG4gIHNvdXJjZXM/OiBhbnlbXTtcbiAgbWV0YWRhdGE/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+O1xufVxuXG5leHBvcnQgY2xhc3MgQ2hhdFNlcnZpY2Uge1xuICBwcml2YXRlIGNsaWVudDogcjJyQ2xpZW50O1xuXG4gIGNvbnN0cnVjdG9yKGNsaWVudDogcjJyQ2xpZW50KSB7XG4gICAgdGhpcy5jbGllbnQgPSBjbGllbnQ7XG4gIH1cblxuICAvKipcbiAgICogU2VuZCBhIG1lc3NhZ2UgdG8gdGhlIFIyUiBhZ2VudCBhbmQgZ2V0IGEgcmVzcG9uc2VcbiAgICovXG4gIGFzeW5jIHNlbmRNZXNzYWdlKFxuICAgIG1lc3NhZ2U6IHN0cmluZyxcbiAgICBjb252ZXJzYXRpb25JZD86IHN0cmluZyxcbiAgICBtZXNzYWdlSGlzdG9yeT86IE1lc3NhZ2VbXVxuICApOiBQcm9taXNlPENoYXRSZXNwb25zZT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBjb25maWcgPSBsb2FkQ2hhdENvbmZpZygpO1xuXG4gICAgICAvLyBQcmVwYXJlIHRoZSBzZWFyY2ggc2V0dGluZ3MgYmFzZWQgb24gY29uZmlndXJhdGlvblxuICAgICAgY29uc3QgY2h1bmtTZWFyY2hTZXR0aW5nczogQ2h1bmtTZWFyY2hTZXR0aW5ncyA9IHtcbiAgICAgICAgaW5kZXhNZWFzdXJlOiBjb25maWcudmVjdG9yU2VhcmNoLmluZGV4TWVhc3VyZSBhcyBJbmRleE1lYXN1cmUsXG4gICAgICAgIGVuYWJsZWQ6IGNvbmZpZy52ZWN0b3JTZWFyY2guZW5hYmxlZCxcbiAgICAgICAgLi4uKGNvbmZpZy52ZWN0b3JTZWFyY2gucHJvYmVzICYmIHsgcHJvYmVzOiBjb25maWcudmVjdG9yU2VhcmNoLnByb2JlcyB9KSxcbiAgICAgICAgLi4uKGNvbmZpZy52ZWN0b3JTZWFyY2guZWZTZWFyY2ggJiYgeyBlZlNlYXJjaDogY29uZmlnLnZlY3RvclNlYXJjaC5lZlNlYXJjaCB9KSxcbiAgICAgIH07XG5cbiAgICAgIGNvbnN0IGh5YnJpZFNlYXJjaFNldHRpbmdzOiBIeWJyaWRTZWFyY2hTZXR0aW5ncyA9IHtcbiAgICAgICAgLi4uKGNvbmZpZy5oeWJyaWRTZWFyY2guZnVsbFRleHRXZWlnaHQgJiYgeyBmdWxsdGV4dFdlaWdodDogY29uZmlnLmh5YnJpZFNlYXJjaC5mdWxsVGV4dFdlaWdodCB9KSxcbiAgICAgICAgLi4uKGNvbmZpZy5oeWJyaWRTZWFyY2guc2VtYW50aWNXZWlnaHQgJiYgeyBzZW1hbnRpY1dlaWdodDogY29uZmlnLmh5YnJpZFNlYXJjaC5zZW1hbnRpY1dlaWdodCB9KSxcbiAgICAgICAgLi4uKGNvbmZpZy5oeWJyaWRTZWFyY2guZnVsbFRleHRMaW1pdCAmJiB7IGZ1bGx0ZXh0TGltaXQ6IGNvbmZpZy5oeWJyaWRTZWFyY2guZnVsbFRleHRMaW1pdCB9KSxcbiAgICAgICAgLi4uKGNvbmZpZy5oeWJyaWRTZWFyY2gucnJmSyAmJiB7IHJyZks6IGNvbmZpZy5oeWJyaWRTZWFyY2gucnJmSyB9KSxcbiAgICAgIH07XG5cbiAgICAgIGNvbnN0IGdyYXBoU2VhcmNoU2V0dGluZ3M6IEdyYXBoU2VhcmNoU2V0dGluZ3MgPSB7XG4gICAgICAgIGVuYWJsZWQ6IGNvbmZpZy5ncmFwaFNlYXJjaC5lbmFibGVkLFxuICAgICAgICAuLi4oY29uZmlnLmdyYXBoU2VhcmNoLm1heENvbW11bml0eURlc2NyaXB0aW9uTGVuZ3RoICYmIHtcbiAgICAgICAgICBtYXhDb21tdW5pdHlEZXNjcmlwdGlvbkxlbmd0aDogY29uZmlnLmdyYXBoU2VhcmNoLm1heENvbW11bml0eURlc2NyaXB0aW9uTGVuZ3RoXG4gICAgICAgIH0pLFxuICAgICAgICAuLi4oY29uZmlnLmdyYXBoU2VhcmNoLm1heExsbVF1ZXJpZXMgJiYge1xuICAgICAgICAgIG1heExsbVF1ZXJpZXNGb3JHbG9iYWxTZWFyY2g6IGNvbmZpZy5ncmFwaFNlYXJjaC5tYXhMbG1RdWVyaWVzXG4gICAgICAgIH0pLFxuICAgICAgICAuLi4oY29uZmlnLmdyYXBoU2VhcmNoLmxvY2FsU2VhcmNoTGltaXRzICYmIHsgbGltaXRzOiBjb25maWcuZ3JhcGhTZWFyY2gubG9jYWxTZWFyY2hMaW1pdHMgfSksXG4gICAgICB9O1xuXG4gICAgICBjb25zdCBzZWFyY2hTZXR0aW5nczogU2VhcmNoU2V0dGluZ3MgPSB7XG4gICAgICAgIHVzZVNlbWFudGljU2VhcmNoOiBjb25maWcudmVjdG9yU2VhcmNoLmVuYWJsZWQsXG4gICAgICAgIHVzZUh5YnJpZFNlYXJjaDogY29uZmlnLmh5YnJpZFNlYXJjaC5lbmFibGVkLFxuICAgICAgICB1c2VGdWxsdGV4dFNlYXJjaDogY29uZmlnLmh5YnJpZFNlYXJjaC5lbmFibGVkLFxuICAgICAgICBmaWx0ZXJzOiBjb25maWcudmVjdG9yU2VhcmNoLnNlYXJjaEZpbHRlcnMgPyBKU09OLnBhcnNlKGNvbmZpZy52ZWN0b3JTZWFyY2guc2VhcmNoRmlsdGVycykgOiB7fSxcbiAgICAgICAgbGltaXQ6IGNvbmZpZy52ZWN0b3JTZWFyY2guc2VhcmNoTGltaXQsXG4gICAgICAgIGluY2x1ZGVNZXRhZGF0YTogY29uZmlnLnZlY3RvclNlYXJjaC5pbmNsdWRlTWV0YWRhdGFzLFxuICAgICAgICBjaHVua1NldHRpbmdzOiBjaHVua1NlYXJjaFNldHRpbmdzLFxuICAgICAgICBoeWJyaWRTZXR0aW5nczogaHlicmlkU2VhcmNoU2V0dGluZ3MsXG4gICAgICAgIGdyYXBoU2V0dGluZ3M6IGdyYXBoU2VhcmNoU2V0dGluZ3MsXG4gICAgICB9O1xuXG4gICAgICAvLyBQcmVwYXJlIGdlbmVyYXRpb24gc2V0dGluZ3NcbiAgICAgIGNvbnN0IHJhZ0dlbmVyYXRpb25Db25maWc6IEdlbmVyYXRpb25Db25maWcgPSB7XG4gICAgICAgIHN0cmVhbTogdHJ1ZSwgLy8gRW5hYmxlIHN0cmVhbWluZyAtIHRoaXMgaXMgY3J1Y2lhbCFcbiAgICAgICAgdGVtcGVyYXR1cmU6IGNvbmZpZy5yYWdHZW5lcmF0aW9uLnRlbXBlcmF0dXJlLFxuICAgICAgICB0b3BQOiBjb25maWcucmFnR2VuZXJhdGlvbi50b3BQLFxuICAgICAgICBtYXhUb2tlbnNUb1NhbXBsZTogY29uZmlnLnJhZ0dlbmVyYXRpb24ubWF4VG9rZW5zVG9TYW1wbGUsXG4gICAgICB9O1xuXG4gICAgICAvLyBDcmVhdGUgdGhlIHVzZXIgbWVzc2FnZSBpbiBSMlIgZm9ybWF0XG4gICAgICBjb25zdCB1c2VyTWVzc2FnZSA9IHtcbiAgICAgICAgcm9sZTogJ3VzZXInIGFzIGNvbnN0LFxuICAgICAgICBjb250ZW50OiBtZXNzYWdlLFxuICAgICAgfTtcblxuICAgICAgLy8gVXNlIGFnZW50IG1vZGUgYmFzZWQgb24gY29uZmlndXJhdGlvblxuICAgICAgaWYgKGNvbmZpZy5hcHAuZGVmYXVsdE1vZGUgPT09ICdyYWdfYWdlbnQnKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdVc2luZyBhZ2VudCBtb2RlIHdpdGggY29uZmlnOicsIHtcbiAgICAgICAgICBtZXNzYWdlOiB1c2VyTWVzc2FnZSxcbiAgICAgICAgICByYWdHZW5lcmF0aW9uQ29uZmlnLFxuICAgICAgICAgIHNlYXJjaFNldHRpbmdzLFxuICAgICAgICAgIGNvbnZlcnNhdGlvbklkLFxuICAgICAgICB9KTtcblxuICAgICAgICBjb25zdCBzdHJlYW1SZXNwb25zZSA9IGF3YWl0IHRoaXMuY2xpZW50LnJldHJpZXZhbC5hZ2VudCh7XG4gICAgICAgICAgbWVzc2FnZTogdXNlck1lc3NhZ2UsXG4gICAgICAgICAgcmFnR2VuZXJhdGlvbkNvbmZpZyxcbiAgICAgICAgICBzZWFyY2hTZXR0aW5ncyxcbiAgICAgICAgICBjb252ZXJzYXRpb25JZCxcbiAgICAgICAgfSk7XG5cbiAgICAgICAgY29uc29sZS5sb2coJ0FnZW50IHJlc3BvbnNlIHJlY2VpdmVkOicsIHN0cmVhbVJlc3BvbnNlKTtcblxuICAgICAgICAvLyBIYW5kbGUgc3RyZWFtaW5nIHJlc3BvbnNlXG4gICAgICAgIHJldHVybiBhd2FpdCB0aGlzLnByb2Nlc3NTdHJlYW1SZXNwb25zZShzdHJlYW1SZXNwb25zZSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmxvZygnVXNpbmcgUkFHIG1vZGUgd2l0aCBjb25maWc6Jywge1xuICAgICAgICAgIHF1ZXJ5OiBtZXNzYWdlLFxuICAgICAgICAgIHJhZ0dlbmVyYXRpb25Db25maWcsXG4gICAgICAgICAgc2VhcmNoU2V0dGluZ3MsXG4gICAgICAgIH0pO1xuXG4gICAgICAgIGNvbnN0IHN0cmVhbVJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQucmV0cmlldmFsLnJhZyh7XG4gICAgICAgICAgcXVlcnk6IG1lc3NhZ2UsXG4gICAgICAgICAgcmFnR2VuZXJhdGlvbkNvbmZpZyxcbiAgICAgICAgICBzZWFyY2hTZXR0aW5ncyxcbiAgICAgICAgfSk7XG5cbiAgICAgICAgY29uc29sZS5sb2coJ1JBRyByZXNwb25zZSByZWNlaXZlZDonLCBzdHJlYW1SZXNwb25zZSk7XG5cbiAgICAgICAgLy8gSGFuZGxlIHN0cmVhbWluZyByZXNwb25zZVxuICAgICAgICByZXR1cm4gYXdhaXQgdGhpcy5wcm9jZXNzU3RyZWFtUmVzcG9uc2Uoc3RyZWFtUmVzcG9uc2UpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbiBjaGF0IHNlcnZpY2U6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFByb2Nlc3Mgc3RyZWFtaW5nIHJlc3BvbnNlIGZyb20gUjJSXG4gICAqL1xuICBwcml2YXRlIGFzeW5jIHByb2Nlc3NTdHJlYW1SZXNwb25zZShzdHJlYW1SZXNwb25zZTogYW55KTogUHJvbWlzZTxDaGF0UmVzcG9uc2U+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ1Byb2Nlc3Npbmcgc3RyZWFtIHJlc3BvbnNlOicsIHN0cmVhbVJlc3BvbnNlKTtcbiAgICAgIGNvbnNvbGUubG9nKCdTdHJlYW0gcmVzcG9uc2UgdHlwZTonLCB0eXBlb2Ygc3RyZWFtUmVzcG9uc2UpO1xuICAgICAgY29uc29sZS5sb2coJ1N0cmVhbSByZXNwb25zZSBoYXMgZ2V0UmVhZGVyOicsIHR5cGVvZiBzdHJlYW1SZXNwb25zZS5nZXRSZWFkZXIpO1xuXG4gICAgICBjb25zdCByZWFkZXIgPSBzdHJlYW1SZXNwb25zZS5nZXRSZWFkZXIoKTtcbiAgICAgIGNvbnN0IGRlY29kZXIgPSBuZXcgVGV4dERlY29kZXIoKTtcbiAgICAgIGxldCBidWZmZXIgPSBcIlwiO1xuICAgICAgbGV0IGZ1bGxDb250ZW50ID0gXCJcIjtcbiAgICAgIGxldCBzb3VyY2VzOiBhbnlbXSA9IFtdO1xuICAgICAgbGV0IG1ldGFkYXRhOiBSZWNvcmQ8c3RyaW5nLCBhbnk+ID0ge307XG5cbiAgICAgIC8vIFByb2Nlc3MgdGhlIHN0cmVhbVxuICAgICAgd2hpbGUgKHRydWUpIHtcbiAgICAgICAgY29uc3QgeyBkb25lLCB2YWx1ZSB9ID0gYXdhaXQgcmVhZGVyLnJlYWQoKTtcbiAgICAgICAgaWYgKGRvbmUpIHtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuXG4gICAgICAgIGJ1ZmZlciArPSBkZWNvZGVyLmRlY29kZSh2YWx1ZSwgeyBzdHJlYW06IHRydWUgfSk7XG5cbiAgICAgICAgLy8gUHJvY2VzcyBjb21wbGV0ZSBTU0UgZXZlbnRzIGZyb20gdGhlIGJ1ZmZlclxuICAgICAgICBjb25zdCBldmVudHMgPSBidWZmZXIuc3BsaXQoXCJcXG5cXG5cIik7XG4gICAgICAgIGJ1ZmZlciA9IGV2ZW50cy5wb3AoKSB8fCBcIlwiOyAvLyBLZWVwIHRoZSBsYXN0IHBvdGVudGlhbGx5IGluY29tcGxldGUgZXZlbnQgaW4gdGhlIGJ1ZmZlclxuXG4gICAgICAgIGZvciAoY29uc3QgZXZlbnQgb2YgZXZlbnRzKSB7XG4gICAgICAgICAgaWYgKCFldmVudC50cmltKCkpIHtcbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIGNvbnN0IGxpbmVzID0gZXZlbnQuc3BsaXQoXCJcXG5cIik7XG4gICAgICAgICAgY29uc3QgZXZlbnRUeXBlID0gbGluZXNbMF0uc3RhcnRzV2l0aChcImV2ZW50OiBcIilcbiAgICAgICAgICAgID8gbGluZXNbMF0uc2xpY2UoNylcbiAgICAgICAgICAgIDogXCJcIjtcbiAgICAgICAgICBjb25zdCBkYXRhTGluZSA9IGxpbmVzLmZpbmQoKGxpbmUpID0+IGxpbmUuc3RhcnRzV2l0aChcImRhdGE6IFwiKSk7XG5cbiAgICAgICAgICBpZiAoIWRhdGFMaW5lKSB7XG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBjb25zdCBqc29uU3RyID0gZGF0YUxpbmUuc2xpY2UoNik7XG5cbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgZXZlbnREYXRhID0gSlNPTi5wYXJzZShqc29uU3RyKTtcblxuICAgICAgICAgICAgaWYgKGV2ZW50VHlwZSA9PT0gXCJzZWFyY2hfcmVzdWx0c1wiKSB7XG4gICAgICAgICAgICAgIC8vIEhhbmRsZSBzZWFyY2ggcmVzdWx0c1xuICAgICAgICAgICAgICBpZiAoZXZlbnREYXRhLnNlYXJjaF9yZXN1bHRzKSB7XG4gICAgICAgICAgICAgICAgc291cmNlcyA9IGV2ZW50RGF0YS5zZWFyY2hfcmVzdWx0cy5jaHVua19zZWFyY2hfcmVzdWx0cyB8fCBbXTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBlbHNlIGlmIChldmVudFR5cGUgPT09IFwibWVzc2FnZVwiKSB7XG4gICAgICAgICAgICAgIC8vIEhhbmRsZSBpbmNyZW1lbnRhbCBjb250ZW50IGRlbHRhXG4gICAgICAgICAgICAgIGlmIChldmVudERhdGEuZGVsdGEgJiYgZXZlbnREYXRhLmRlbHRhLmNvbnRlbnQpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBjb250ZW50SXRlbXMgPSBldmVudERhdGEuZGVsdGEuY29udGVudDtcbiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IGl0ZW0gb2YgY29udGVudEl0ZW1zKSB7XG4gICAgICAgICAgICAgICAgICBpZiAoXG4gICAgICAgICAgICAgICAgICAgIGl0ZW0udHlwZSA9PT0gXCJ0ZXh0XCIgJiZcbiAgICAgICAgICAgICAgICAgICAgaXRlbS5wYXlsb2FkICYmXG4gICAgICAgICAgICAgICAgICAgIGl0ZW0ucGF5bG9hZC52YWx1ZVxuICAgICAgICAgICAgICAgICAgKSB7XG4gICAgICAgICAgICAgICAgICAgIGZ1bGxDb250ZW50ICs9IGl0ZW0ucGF5bG9hZC52YWx1ZTtcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBwYXJzaW5nIFNTRSBldmVudCBkYXRhOlwiLCBlcnIsIGpzb25TdHIpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBtZXNzYWdlOiBmdWxsQ29udGVudCB8fCAnTm8gcmVzcG9uc2UgZ2VuZXJhdGVkJyxcbiAgICAgICAgc291cmNlcyxcbiAgICAgICAgbWV0YWRhdGEsXG4gICAgICB9O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBwcm9jZXNzaW5nIHN0cmVhbSByZXNwb25zZTonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQ3JlYXRlIGEgbmV3IGNvbnZlcnNhdGlvblxuICAgKi9cbiAgYXN5bmMgY3JlYXRlQ29udmVyc2F0aW9uKG5hbWU/OiBzdHJpbmcpOiBQcm9taXNlPHN0cmluZz4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY2xpZW50LmNvbnZlcnNhdGlvbnMuY3JlYXRlKHtcbiAgICAgICAgbmFtZTogbmFtZSB8fCBgQ29udmVyc2F0aW9uICR7bmV3IERhdGUoKS50b0xvY2FsZVN0cmluZygpfWAsXG4gICAgICB9KTtcblxuICAgICAgaWYgKHJlc3BvbnNlLnJlc3VsdHM/LmlkKSB7XG4gICAgICAgIHJldHVybiByZXNwb25zZS5yZXN1bHRzLmlkO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gY3JlYXRlIGNvbnZlcnNhdGlvbicpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyBjb252ZXJzYXRpb246JywgZXJyb3IpO1xuICAgICAgLy8gUmV0dXJuIGEgbW9jayBJRCBmb3IgZGV2ZWxvcG1lbnRcbiAgICAgIHJldHVybiBgY29udi0ke0RhdGUubm93KCl9YDtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogR2V0IGNvbnZlcnNhdGlvbiBoaXN0b3J5XG4gICAqL1xuICBhc3luYyBnZXRDb252ZXJzYXRpb25IaXN0b3J5KGNvbnZlcnNhdGlvbklkOiBzdHJpbmcpOiBQcm9taXNlPE1lc3NhZ2VbXT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY2xpZW50LmNvbnZlcnNhdGlvbnMucmV0cmlldmUoe1xuICAgICAgICBpZDogY29udmVyc2F0aW9uSWQsXG4gICAgICB9KTtcblxuICAgICAgaWYgKHJlc3BvbnNlLnJlc3VsdHMpIHtcbiAgICAgICAgcmV0dXJuIHJlc3BvbnNlLnJlc3VsdHMubWFwKChtc2c6IGFueSkgPT4gKHtcbiAgICAgICAgICBpZDogbXNnLmlkIHx8IGBtc2ctJHtEYXRlLm5vdygpfS0ke01hdGgucmFuZG9tKCl9YCxcbiAgICAgICAgICByb2xlOiBtc2cubWV0YWRhdGE/LnJvbGUgfHwgJ3VzZXInLFxuICAgICAgICAgIGNvbnRlbnQ6IG1zZy5tZXRhZGF0YT8uY29udGVudCB8fCBtc2cuY29udGVudCB8fCAnJyxcbiAgICAgICAgICB0aW1lc3RhbXA6IG1zZy5tZXRhZGF0YT8udGltZXN0YW1wIHx8IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICBtZXRhZGF0YTogbXNnLm1ldGFkYXRhLFxuICAgICAgICB9KSk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBbXTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZ2V0dGluZyBjb252ZXJzYXRpb24gaGlzdG9yeTonLCBlcnJvcik7XG4gICAgICByZXR1cm4gW107XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIExpc3QgY29udmVyc2F0aW9uc1xuICAgKi9cbiAgYXN5bmMgbGlzdENvbnZlcnNhdGlvbnMob2Zmc2V0OiBudW1iZXIgPSAwLCBsaW1pdDogbnVtYmVyID0gMTAwKSB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQuY29udmVyc2F0aW9ucy5saXN0KHtcbiAgICAgICAgb2Zmc2V0LFxuICAgICAgICBsaW1pdCxcbiAgICAgIH0pO1xuXG4gICAgICByZXR1cm4gcmVzcG9uc2UucmVzdWx0cyB8fCBbXTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbGlzdGluZyBjb252ZXJzYXRpb25zOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBbXTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogRGVsZXRlIGEgY29udmVyc2F0aW9uXG4gICAqL1xuICBhc3luYyBkZWxldGVDb252ZXJzYXRpb24oY29udmVyc2F0aW9uSWQ6IHN0cmluZyk6IFByb21pc2U8dm9pZD4ge1xuICAgIHRyeSB7XG4gICAgICBhd2FpdCB0aGlzLmNsaWVudC5jb252ZXJzYXRpb25zLmRlbGV0ZSh7XG4gICAgICAgIGlkOiBjb252ZXJzYXRpb25JZCxcbiAgICAgIH0pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBkZWxldGluZyBjb252ZXJzYXRpb246JywgZXJyb3IpO1xuICAgICAgLy8gRG9uJ3QgdGhyb3cgZXJyb3IgaW4gZGV2ZWxvcG1lbnQgbW9kZVxuICAgICAgY29uc29sZS53YXJuKCdDb252ZXJzYXRpb24gZGVsZXRpb24gZmFpbGVkLCBjb250aW51aW5nLi4uJyk7XG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOlsibG9hZENoYXRDb25maWciLCJDaGF0U2VydmljZSIsInNlbmRNZXNzYWdlIiwibWVzc2FnZSIsImNvbnZlcnNhdGlvbklkIiwibWVzc2FnZUhpc3RvcnkiLCJjb25maWciLCJjaHVua1NlYXJjaFNldHRpbmdzIiwiaW5kZXhNZWFzdXJlIiwidmVjdG9yU2VhcmNoIiwiZW5hYmxlZCIsInByb2JlcyIsImVmU2VhcmNoIiwiaHlicmlkU2VhcmNoU2V0dGluZ3MiLCJoeWJyaWRTZWFyY2giLCJmdWxsVGV4dFdlaWdodCIsImZ1bGx0ZXh0V2VpZ2h0Iiwic2VtYW50aWNXZWlnaHQiLCJmdWxsVGV4dExpbWl0IiwiZnVsbHRleHRMaW1pdCIsInJyZksiLCJncmFwaFNlYXJjaFNldHRpbmdzIiwiZ3JhcGhTZWFyY2giLCJtYXhDb21tdW5pdHlEZXNjcmlwdGlvbkxlbmd0aCIsIm1heExsbVF1ZXJpZXMiLCJtYXhMbG1RdWVyaWVzRm9yR2xvYmFsU2VhcmNoIiwibG9jYWxTZWFyY2hMaW1pdHMiLCJsaW1pdHMiLCJzZWFyY2hTZXR0aW5ncyIsInVzZVNlbWFudGljU2VhcmNoIiwidXNlSHlicmlkU2VhcmNoIiwidXNlRnVsbHRleHRTZWFyY2giLCJmaWx0ZXJzIiwic2VhcmNoRmlsdGVycyIsIkpTT04iLCJwYXJzZSIsImxpbWl0Iiwic2VhcmNoTGltaXQiLCJpbmNsdWRlTWV0YWRhdGEiLCJpbmNsdWRlTWV0YWRhdGFzIiwiY2h1bmtTZXR0aW5ncyIsImh5YnJpZFNldHRpbmdzIiwiZ3JhcGhTZXR0aW5ncyIsInJhZ0dlbmVyYXRpb25Db25maWciLCJzdHJlYW0iLCJ0ZW1wZXJhdHVyZSIsInJhZ0dlbmVyYXRpb24iLCJ0b3BQIiwibWF4VG9rZW5zVG9TYW1wbGUiLCJ1c2VyTWVzc2FnZSIsInJvbGUiLCJjb250ZW50IiwiYXBwIiwiZGVmYXVsdE1vZGUiLCJjb25zb2xlIiwibG9nIiwic3RyZWFtUmVzcG9uc2UiLCJjbGllbnQiLCJyZXRyaWV2YWwiLCJhZ2VudCIsInByb2Nlc3NTdHJlYW1SZXNwb25zZSIsInF1ZXJ5IiwicmFnIiwiZXJyb3IiLCJnZXRSZWFkZXIiLCJyZWFkZXIiLCJkZWNvZGVyIiwiVGV4dERlY29kZXIiLCJidWZmZXIiLCJmdWxsQ29udGVudCIsInNvdXJjZXMiLCJtZXRhZGF0YSIsImRvbmUiLCJ2YWx1ZSIsInJlYWQiLCJkZWNvZGUiLCJldmVudHMiLCJzcGxpdCIsInBvcCIsImV2ZW50IiwidHJpbSIsImxpbmVzIiwiZXZlbnRUeXBlIiwic3RhcnRzV2l0aCIsInNsaWNlIiwiZGF0YUxpbmUiLCJmaW5kIiwibGluZSIsImpzb25TdHIiLCJldmVudERhdGEiLCJzZWFyY2hfcmVzdWx0cyIsImNodW5rX3NlYXJjaF9yZXN1bHRzIiwiZGVsdGEiLCJjb250ZW50SXRlbXMiLCJpdGVtIiwidHlwZSIsInBheWxvYWQiLCJlcnIiLCJjcmVhdGVDb252ZXJzYXRpb24iLCJuYW1lIiwicmVzcG9uc2UiLCJjb252ZXJzYXRpb25zIiwiY3JlYXRlIiwiRGF0ZSIsInRvTG9jYWxlU3RyaW5nIiwicmVzdWx0cyIsImlkIiwiRXJyb3IiLCJub3ciLCJnZXRDb252ZXJzYXRpb25IaXN0b3J5IiwicmV0cmlldmUiLCJtYXAiLCJtc2ciLCJNYXRoIiwicmFuZG9tIiwidGltZXN0YW1wIiwidG9JU09TdHJpbmciLCJsaXN0Q29udmVyc2F0aW9ucyIsIm9mZnNldCIsImxpc3QiLCJkZWxldGVDb252ZXJzYXRpb24iLCJkZWxldGUiLCJ3YXJuIiwiY29uc3RydWN0b3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/lib/chatService.ts\n"));

/***/ })

});